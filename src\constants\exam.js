import { ICONS } from './icons';

export const EXAM_TYPES = {
    CUOI_HK1: '<PERSON><PERSON><PERSON><PERSON> học kỳ 1',
    CUOI_HK2: '<PERSON><PERSON><PERSON><PERSON> học kỳ 2',
    THI_TIEP_CAN_LAN_1: 'Thi tiếp cận lần 1',
    THI_TIEP_CAN_LAN_2: 'Thi tiếp cận lần 2',
    THI_TIEP_CAN_LAN_3: 'Thi tiếp cận lần 3'
};

export const TIME_SLOTS = {
    'all-day': 'C<PERSON> ngày',
    morning: 'Sáng',
    afternoon: 'Chiều'
};

export const DEPARTMENT_GROUPS = {
    TOAN_TIN: 'Tổ Toán – Tin học',
    TU_NHIEN: 'Tổ Tự nhiên', 
    NGU_VAN: 'Tổ Ngữ văn',
    XA_HOI: 'Tổ Xã hội',
    NGOAI_NGU: 'Tổ Ngoại ngữ',
    MY_THUAT_AM_NHAC: 'Tổ <PERSON>ỹ thuật - Âm nhạc'
}; 

export const SESSION_STATUS = {
    CHUA_PHAN_CONG: 'Chưa phân công',
    DA_PHAN_CONG: 'Đã phân công',
    DA_HOAN_THANH: 'Đã hoàn thành',
    YEU_CAU_DOI_BUOI: 'Yêu cầu đổi buổi'
};

// Exam Change Request Status Tabs
export const EXAM_CHANGE_REQUEST_TABS = [
    { key: 'PENDING', label: 'Chờ duyệt', icon: ICONS.PENDING, color: '#ff9500' },
    { key: 'APPROVED', label: 'Đã duyệt', icon: ICONS.SUCCESS, color: '#34c759' },
    { key: 'REJECTED', label: 'Từ chối', icon: ICONS.ERROR, color: '#ff3b30' }
];

export const EXAM_CHANGE_REQUEST_STATUS_LABELS = {
    PENDING: 'Chờ duyệt',
    APPROVED: 'Đã duyệt',
    REJECTED: 'Từ chối'
};

export const EXAM_CHANGE_REQUEST_STATUS_COLORS = {
    PENDING: '#ff9500',
    APPROVED: '#34c759',
    REJECTED: '#ff3b30'
};

// Teacher Exam Schedule Tabs
export const TEACHER_EXAM_SCHEDULE_TABS = [
    { key: 'all', label: 'Tất cả', icon: ICONS.LIST, color: '#0068ff' },
    { key: 'DA_PHAN_CONG', label: 'Đã phân công', icon: ICONS.SUCCESS, color: '#34c759' },
    { key: 'DA_HOAN_THANH', label: 'Đã hoàn thành', icon: ICONS.TARGET, color: '#5856d6' },
    { key: 'YEU_CAU_DOI_BUOI', label: 'Yêu cầu đổi', icon: ICONS.REFRESH, color: '#ff9500' }
];

export const CHANGE_REQUEST_STATUS = {
    PENDING: 'PENDING',
    APPROVED: 'APPROVED', 
    REJECTED: 'REJECTED'
};

export const CHANGE_REQUEST_STATUS_DISPLAY = {
    PENDING: { key: 'PENDING', label: 'Chờ duyệt', icon: ICONS.PENDING, color: '#ff9500' },
    APPROVED: { key: 'APPROVED', label: 'Đã duyệt', icon: ICONS.SUCCESS, color: '#34c759' },
    REJECTED: { key: 'REJECTED', label: 'Từ chối', icon: ICONS.ERROR, color: '#ff3b30' }
};

export const SUPERVISION_STATUS_DISPLAY = {
    CHUA_PHAN_CONG: { key: 'CHUA_PHAN_CONG', label: 'Chưa phân công', icon: ICONS.PENDING, color: '#ff9500' },
    DA_PHAN_CONG: { key: 'DA_PHAN_CONG', label: 'Đã phân công', icon: ICONS.SUCCESS, color: '#34c759' },
    DA_HOAN_THANH: { key: 'DA_HOAN_THANH', label: 'Đã hoàn thành', icon: ICONS.COMPLETE, color: '#5856d6' },
    YEU_CAU_DOI_BUOI: { key: 'YEU_CAU_DOI_BUOI', label: 'Yêu cầu đổi buổi', icon: ICONS.REFRESH, color: '#ff9500' }
};
import React, { useState } from 'react';
import LoadingIndicator from './LoadingIndicator';

const ImageWithLoading = ({ 
    src, 
    alt, 
    style = {}, 
    loadingStyle = {},
    onClick,
    className = '',
    placeholder = null,
    ...props 
}) => {
    const [loading, setLoading] = useState(true);
    const [error, setError] = useState(false);

    const handleLoad = () => {
        setLoading(false);
    };

    const handleError = () => {
        setLoading(false);
        setError(true);
    };

    const containerStyle = {
        position: 'relative',
        display: 'inline-block',
        ...style
    };

    const loadingContainerStyle = {
        position: 'absolute',
        top: 0,
        left: 0,
        width: '100%',
        height: '100%',
        display: 'flex',
        alignItems: 'center',
        justifyContent: 'center',
        backgroundColor: '#f5f5f5',
        borderRadius: style.borderRadius || '8px',
        ...loadingStyle
    };

    const imageStyle = {
        ...style,
        opacity: loading ? 0 : 1,
        transition: 'opacity 0.3s ease',
        display: loading ? 'none' : 'block'
    };

    if (error) {
        return (
            <div 
                style={{
                    ...containerStyle,
                    backgroundColor: '#f0f0f0',
                    display: 'flex',
                    alignItems: 'center',
                    justifyContent: 'center',
                    color: '#999',
                    fontSize: '12px',
                    textAlign: 'center'
                }}
                className={className}
            >
                {placeholder || '❌\nLỗi tải ảnh'}
            </div>
        );
    }

    return (
        <div style={containerStyle} className={className}>
            {loading && (
                <div style={loadingContainerStyle}>
                    <LoadingIndicator size="small" />
                </div>
            )}
            <img
                src={src}
                alt={alt}
                style={imageStyle}
                onLoad={handleLoad}
                onError={handleError}
                onClick={onClick}
                {...props}
            />
        </div>
    );
};

export default ImageWithLoading; 
import React, { useState, useEffect, useContext } from 'react';
import { Box, Text, Button, Select, Input, useNavigate } from 'zmp-ui';
import { AuthContext } from '../../context/AuthContext';
import { authApi } from '../../utils/api';
import HeaderEdu from '../../components/HeaderEdu';
import HeaderSpacer from '../../components/utils/HeaderSpacer';
import BottomNavigationEdu from '../../components/BottomNavigationEdu';
import LoadingIndicator from '../../components/utils/LoadingIndicator';
import TeacherSelector from '../../components/utils/TeacherSelector';
import TabFilter from '../../components/utils/TabFilter';
import useNotification from '../../hooks/useNotification';
import { SESSION_STATUS, SESSION_STATUS_DISPLAY } from '../../constants/sessionStatus';
import { TIME_SLOTS, EXAM_TYPES } from '../../constants/exam';
import { ICONS } from '../../constants/icons';

const { Option } = Select;

const ExamSupervisionList = () => {
    const navigate = useNavigate();
    const { user } = useContext(AuthContext);
    const notification = useNotification();
    const [loading, setLoading] = useState(true);
    const [supervisions, setSupervisions] = useState([]);
    const [pagination, setPagination] = useState({
        current: 1,
        pages: 1,
        total: 0
    });
    const [selectedTab, setSelectedTab] = useState('all');

    // Filter states
    const [filters, setFilters] = useState({
        schoolYear: '',
        examType: '',
        teacher: '',
        page: 1,
        limit: 10
    });
    const [showFilters, setShowFilters] = useState(false);
    const [teacherSelectorKey, setTeacherSelectorKey] = useState(0);

    // Define tab configurations
    const tabs = [
        { key: 'all', label: 'Tất cả', icon: ICONS.LIST, color: '#0068ff' },
        { key: 'DA_PHAN_CONG', label: 'Đã phân công', icon: ICONS.SUCCESS, color: '#34c759' },
        { key: 'DA_HOAN_THANH', label: 'Đã hoàn thành', icon: ICONS.TARGET, color: '#5856d6' },
        { key: 'YEU_CAU_DOI_BUOI', label: 'Yêu cầu đổi', icon: ICONS.REFRESH, color: '#ff9500' }
    ];

    // Fetch supervisions
    const fetchSupervisions = async () => {
        setLoading(true);
        try {
            const queryParams = new URLSearchParams();
            Object.entries(filters).forEach(([key, value]) => {
                if (value) queryParams.append(key, value);
            });

            // Add status filter from tab
            if (selectedTab !== 'all') {
                queryParams.append('status', selectedTab);
            }

            const response = await authApi.get(`/exam-supervisions?${queryParams.toString()}`);
            if (response.data) {
                setSupervisions(response.data.supervisions);
                setPagination(response.data.pagination);
            }
        } catch (error) {
            console.error('Error fetching supervisions:', error);
            notification.showError('Lỗi', 'Không thể tải danh sách phân công coi thi');
        } finally {
            setLoading(false);
        }
    };

    useEffect(() => {
        fetchSupervisions();
    }, [filters, selectedTab]);

    const handleFilterChange = (field, value) => {
        setFilters(prev => ({
            ...prev,
            [field]: value,
            page: 1 // Reset to first page when filter changes
        }));
    };

    const handlePageChange = (newPage) => {
        setFilters(prev => ({
            ...prev,
            page: newPage
        }));
    };

    const handleCreateNew = () => {
        navigate('/exam-supervisions/create');
    };

    const handleEdit = (id) => {
        navigate(`/exam-supervisions/edit/${id}`);
    };

    const handleDelete = async (id) => {
        notification.confirmDelete(
            'Xóa phân công coi thi',
            'Bạn có chắc chắn muốn xóa phân công coi thi này không?',
            async () => {
                await notification.handleFormSubmission(
                    async () => {
                        await authApi.delete(`/exam-supervisions/${id}`);
                    },
                    {
                        loadingMessage: 'Đang xóa...',
                        successMessage: 'Đã xóa phân công coi thi thành công',
                        onSuccess: () => {
                            fetchSupervisions();
                        }
                    }
                );
            }
        );
    };

    return (
        <Box className="container" style={{ minHeight: '100vh', display: 'flex', flexDirection: 'column', paddingBottom: '60px', backgroundColor: '#f5f5f5' }}>
            <HeaderEdu title="Phân công coi thi" showBackButton={true} onBackClick={() => navigate(-1)} />
            <HeaderSpacer />

            <Box style={{ padding: '15px', flex: 1 }}>
                {/* Filters */}
                <Box style={{ backgroundColor: 'white', borderRadius: '10px', padding: '15px', marginBottom: '15px' }}>
                    <Box style={{ display: 'flex', justifyContent: 'space-between', alignItems: 'center', marginBottom: '15px' }}>
                        <Text bold size="large">Bộ lọc</Text>
                        <Button
                            size="small"
                            onClick={() => setShowFilters(!showFilters)}
                            style={{ backgroundColor: '#f0f8ff', color: '#0068ff' }}
                        >
                            {showFilters ? 'Ẩn' : 'Hiện'} bộ lọc
                        </Button>
                    </Box>

                    {showFilters && (
                        <>
                            <Box style={{ display: 'flex', flexDirection: 'column', gap: '12px', marginBottom: '15px' }}>
                                <Box style={{ display: 'grid', gridTemplateColumns: 'repeat(2, 1fr)', gap: '12px' }}>
                                    <Box>
                                        <Text style={{ marginBottom: '8px', fontSize: '14px', fontWeight: '500' }}>
                                            {ICONS.CALENDAR} Năm học:
                                        </Text>
                                        <Select
                                            placeholder="Chọn năm học"
                                            value={filters.schoolYear}
                                            onChange={(value) => handleFilterChange('schoolYear', value)}
                                            style={{
                                                padding: '10px',
                                                borderRadius: '6px',
                                                fontSize: '14px'
                                            }}
                                        >
                                            <Option value="2023-2024" title="2023-2024" />
                                            <Option value="2024-2025" title="2024-2025" />
                                        </Select>
                                    </Box>
                                    <Box>
                                        <Text style={{ marginBottom: '8px', fontSize: '14px', fontWeight: '500' }}>
                                            {ICONS.SUBJECT} Loại kỳ thi:
                                        </Text>
                                        <Select
                                            placeholder="Chọn loại kỳ thi"
                                            value={filters.examType}
                                            onChange={(value) => handleFilterChange('examType', value)}
                                            style={{
                                                padding: '10px',
                                                borderRadius: '6px',
                                                fontSize: '14px'
                                            }}
                                        >
                                            {Object.entries(EXAM_TYPES).map(([key, label]) => (
                                                <Option key={key} value={key} title={label} />
                                            ))}
                                        </Select>
                                    </Box>
                                </Box>
                                <Box>
                                    <TeacherSelector
                                        key={teacherSelectorKey}
                                        value={filters.teacher}
                                        onChange={(value) => handleFilterChange('teacher', value)}
                                        label={`${ICONS.TEACHER} Chọn giáo viên`}
                                        required={false}
                                        style={{ marginBottom: '0' }}
                                    />
                                </Box>
                            </Box>
                            <Box style={{ display: 'flex', gap: '10px' }}>
                                <Button
                                    size="small"
                                    onClick={() => {
                                        setFilters({
                                            schoolYear: '',
                                            examType: '',
                                            teacher: '',
                                            page: 1,
                                            limit: 10
                                        });
                                        setTeacherSelectorKey(prev => prev + 1);
                                    }}
                                    style={{
                                        backgroundColor: '#f5f5f5',
                                        color: '#666',
                                        display: 'flex',
                                        alignItems: 'center',
                                        gap: '4px'
                                    }}
                                >
                                    <Text>Xóa bộ lọc</Text>
                                </Button>
                                <Button
                                    size="small"
                                    onClick={() => fetchSupervisions()}
                                    style={{
                                        backgroundColor: '#0068ff',
                                        color: 'white',
                                        display: 'flex',
                                        alignItems: 'center',
                                        gap: '4px'
                                    }}
                                >
                                    <Text>Áp dụng</Text>
                                </Button>
                            </Box>
                        </>
                    )}
                </Box>

                {/* Tab filter */}
                <TabFilter
                    tabs={tabs}
                    selectedTab={selectedTab}
                    onTabChange={setSelectedTab}
                />

                {/* List */}
                <Box className="list" style={{ backgroundColor: 'white', borderRadius: '10px', padding: '15px' }}>
                    <Box style={{ display: 'flex', justifyContent: 'space-between', alignItems: 'center', marginBottom: '15px' }}>
                        <Text bold size="large">Danh sách phân công coi thi</Text>
                        <Button
                            onClick={handleCreateNew}
                            style={{
                                borderRadius: '28px',
                                backgroundColor: '#0068ff',
                                color: 'white',
                                display: 'flex',
                                alignItems: 'center',
                                justifyContent: 'center',
                                gap: '4px',
                                padding: '10px',
                                border: 'none', // Bỏ viền mặc định
                                cursor: 'pointer', // Con trỏ chuột
                                transition: 'transform 0.2s ease', // Hiệu ứng mượt
                            }}
                        >
                            <Text style={{
                                display: 'flex',
                                alignItems: 'center',
                                lineHeight: 1,
                                fontSize: '20px' // Icon to hơn chút
                            }}>
                                +
                            </Text>
                        </Button>
                    </Box>

                    {loading ? (
                        <LoadingIndicator />
                    ) : supervisions.length > 0 ? (
                        <Box>
                            {supervisions.map((supervision) => (
                                <Box
                                    key={supervision._id}
                                    style={{
                                        padding: '15px',
                                        border: '1px solid #e0e0e0',
                                        borderRadius: '8px',
                                        marginBottom: '10px',
                                        backgroundColor: '#fff'
                                    }}
                                >
                                    <Box style={{ display: 'flex', justifyContent: 'space-between', marginBottom: '10px' }}>
                                        <Text bold>{EXAM_TYPES[supervision.examType]} - {supervision.schoolYear}</Text>
                                        <Box style={{ display: 'flex', gap: '10px' }}>
                                            <Button
                                                size="small"
                                                onClick={() => handleEdit(supervision._id)}
                                                style={{
                                                    backgroundColor: '#0068ff',
                                                    color: 'white',
                                                    display: 'flex',
                                                    alignItems: 'center',
                                                    justifyContent: 'center',
                                                    gap: '4px',
                                                    padding: '4px 8px', // Smaller padding for small size
                                                    borderRadius: '6px', // Slightly smaller radius for compact look
                                                    border: 'none',
                                                    cursor: 'pointer',
                                                    transition: 'transform 0.2s ease, box-shadow 0.2s ease',
                                                    boxShadow: '0 1px 6px rgba(0, 196, 180, 0.2)', // Subtle shadow
                                                    fontSize: '14px', // Text size for small buttons
                                                    lineHeight: 1, // Ensure text/icon alignment
                                                    minWidth: '60px', // Minimum width for consistency
                                                }}
                                                onMouseEnter={(e) => {
                                                    e.currentTarget.style.transform = 'scale(1.05)';
                                                    e.currentTarget.style.boxShadow = '0 2px 8px rgba(0, 196, 180, 0.3)';
                                                }}
                                                onMouseLeave={(e) => {
                                                    e.currentTarget.style.transform = 'scale(1)';
                                                    e.currentTarget.style.boxShadow = '0 1px 6px rgba(0, 196, 180, 0.2)';
                                                }}
                                            >
                                                <span style={{ fontSize: '16px', lineHeight: 1 }}>{ICONS.EDIT}</span>
                                                Sửa
                                            </Button>
                                            <Button
                                                size="small"
                                                onClick={() => handleDelete(supervision._id)}
                                                style={{
                                                    background: 'linear-gradient(135deg, #ff4d4f, #b31217)', // Red gradient for Delete
                                                    color: 'white',
                                                    display: 'flex',
                                                    alignItems: 'center',
                                                    justifyContent: 'center',
                                                    gap: '4px',
                                                    padding: '4px 8px',
                                                    borderRadius: '6px',
                                                    border: 'none',
                                                    cursor: 'pointer',
                                                    transition: 'transform 0.2s ease, box-shadow 0.2s ease',
                                                    boxShadow: '0 1px 6px rgba(255, 77, 79, 0.2)',
                                                    fontSize: '14px',
                                                    lineHeight: 1,
                                                    minWidth: '60px',
                                                }}
                                                onMouseEnter={(e) => {
                                                    e.currentTarget.style.transform = 'scale(1.05)';
                                                    e.currentTarget.style.boxShadow = '0 2px 8px rgba(255, 77, 79, 0.3)';
                                                }}
                                                onMouseLeave={(e) => {
                                                    e.currentTarget.style.transform = 'scale(1)';
                                                    e.currentTarget.style.boxShadow = '0 1px 6px rgba(255, 77, 79, 0.2)';
                                                }}
                                            >
                                                <span style={{ fontSize: '16px', lineHeight: 1 }}>🗑️</span>
                                                Xóa
                                            </Button>
                                        </Box>
                                    </Box>
                                    <Text style={{ marginBottom: '5px' }}>{ICONS.TEACHER} Giáo viên: {supervision.teacher.name}</Text>
                                    <Text style={{ marginBottom: '5px' }}>{ICONS.EMAIL} Email: {supervision.teacher.email}</Text>
                                    <Text style={{ marginBottom: '10px' }}>{ICONS.MESSAGE} Ghi chú: {supervision.notes || 'Không có'}</Text>

                                    {/* Sessions */}
                                    <Box style={{ marginTop: '10px' }}>
                                        <Text bold style={{ marginBottom: '5px' }}>Các buổi thi:</Text>
                                        {supervision.sessions.map((session, index) => (
                                            <Box
                                                key={session._id}
                                                style={{
                                                    padding: '10px',
                                                    backgroundColor: '#f5f5f5',
                                                    borderRadius: '4px',
                                                    marginBottom: '5px'
                                                }}
                                            >
                                                <Text>Buổi {index + 1}: {new Date(session.date).toLocaleDateString('vi-VN')} - {TIME_SLOTS[session.timeSlot]}</Text>
                                                <Text>Phòng: {session.room}</Text>
                                                <Text>Môn: {session.subject}</Text>
                                                <Box style={{ display: 'flex', flexDirection: 'column', alignItems: 'flex-start', gap: 4 }}>
                                                    {/* Status label */}
                                                    {session.status && SESSION_STATUS_DISPLAY[session.status] && (
                                                        <Box style={SESSION_STATUS_DISPLAY[session.status].style}>
                                                            {SESSION_STATUS_DISPLAY[session.status].label}
                                                        </Box>
                                                    )}
                                                </Box>
                                            </Box>
                                        ))}
                                    </Box>
                                </Box>
                            ))}

                            {/* Pagination */}
                            {pagination.pages > 1 && (
                                <Box style={{ display: 'flex', justifyContent: 'center', gap: '10px', marginTop: '20px' }}>
                                    <Button
                                        disabled={pagination.current === 1}
                                        onClick={() => handlePageChange(pagination.current - 1)}
                                    >
                                        Trước
                                    </Button>
                                    <Text>Trang {pagination.current} / {pagination.pages}</Text>
                                    <Button
                                        disabled={pagination.current === pagination.pages}
                                        onClick={() => handlePageChange(pagination.current + 1)}
                                    >
                                        Sau
                                    </Button>
                                </Box>
                            )}
                        </Box>
                    ) : (
                        <Box style={{ textAlign: 'center', padding: '20px' }}>
                            <Text>Không có phân công coi thi nào</Text>
                        </Box>
                    )}
                </Box>
            </Box>

            <BottomNavigationEdu />
        </Box>
    );
};

export default ExamSupervisionList; 
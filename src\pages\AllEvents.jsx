import React, { useEffect, useState, useRef, useContext } from 'react';
import { Box, Text, List, useNavigate } from 'zmp-ui';
import HeaderEdu from '../components/HeaderEdu';
import BottomNavigation from '../components/BottomNavigationEdu';
import Loading from '../components/utils/Loading';
import { AuthContext } from '../context/AuthContext';
import { authApi } from '../utils/api';
import { formatEventDate, formatEventTime } from '../utils/dateUtils';
import HeaderSpacer from '../components/utils/HeaderSpacer';

const AllEvents = () => {
    const navigate = useNavigate();
    const { user, classId, loading: authLoading } = useContext(AuthContext);
    const [events, setEvents] = useState([]);
    const [loading, setLoading] = useState(true);
    const [page, setPage] = useState(1);
    const [totalPages, setTotalPages] = useState(1);
    const observerRef = useRef(null);

    // <PERSON><PERSON><PERSON> tra user và redirect nếu chưa đăng nhập
    useEffect(() => {
        if (!authLoading && !user) {
            navigate('/login', { replace: true });
        }
    }, [user, authLoading, navigate]);

    // Lấy danh sách sự kiện
    useEffect(() => {
        if (classId) {
            setLoading(true);
            authApi
                .get(`/events?classId=${classId}&page=${page}&limit=10`)
                .then((response) => {
                    setEvents((prev) => [...prev, ...response.data.events]);
                    setTotalPages(response.data.totalPages);
                    setLoading(false);
                })
                .catch((err) => {
                    console.log('Error fetching events:', err);
                    setLoading(false);
                });
        }
    }, [classId, page]);

    // Infinite scroll
    useEffect(() => {
        const observer = new IntersectionObserver(
            (entries) => {
                if (entries[0].isIntersecting && page < totalPages && !loading) {
                    setPage((prev) => prev + 1);
                }
            },
            { threshold: 0.1 }
        );

        if (observerRef.current) {
            observer.observe(observerRef.current);
        }

        return () => {
            if (observerRef.current) {
                observer.unobserve(observerRef.current);
            }
        };
    }, [page, totalPages, loading]);

    return (
        <Box style={{ backgroundColor: '#f5f5f5', minHeight: '100vh', position: 'relative' }}>
            <HeaderEdu />
            <HeaderSpacer />
            {authLoading ? (
                <Text>Đang tải thông tin...</Text>
            ) : (
                <Box style={{ padding: '15px' }}>
                    <Text bold size="xLarge" style={{ marginBottom: '15px' }}>
                        Tất cả sự kiện
                    </Text>
                    <List style={{ display: 'flex', flexDirection: 'column', gap: '15px' }}>
                        {events.length > 0 ? (
                            events.map((item) => (
                                <Box
                                    key={item._id}
                                    style={{
                                        backgroundColor: '#f9f9f9',
                                        borderRadius: '8px',
                                        padding: '12px',
                                        boxShadow: '0 2px 4px rgba(0, 0, 0, 0.1)',
                                    }}
                                >
                                    <Text bold style={{ color: '#0068ff', marginBottom: '5px' }}>
                                        {formatEventDate(item.startTime)}
                                    </Text>
                                    <Text bold style={{ marginBottom: '5px' }}>
                                        {item.title}
                                    </Text>
                                    <Text style={{ fontSize: '12px', color: '#666' }}>
                                        {formatEventTime(item.startTime, item.endTime, item.description)}
                                    </Text>
                                </Box>
                            ))
                        ) : (
                            <Text>Chưa có sự kiện</Text>
                        )}
                    </List>
                    {loading && <Loading />}
                    <div ref={observerRef} style={{ height: '20px' }} />
                </Box>
            )}
            <BottomNavigation />
        </Box>
    );
};

export default AllEvents;
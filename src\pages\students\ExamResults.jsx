import React, { useState, useEffect, useContext, useCallback } from 'react';
import { Box, Text, Button, useNavigate, List } from 'zmp-ui';
import { useParams } from 'react-router-dom';
import HeaderEdu from '../../components/HeaderEdu';
import HeaderSpacer from '../../components/utils/HeaderSpacer';
import BottomNavigationEdu from '../../components/BottomNavigationEdu';
import { AuthContext } from '../../context/AuthContext';
import { authApi } from '../../utils/api';
import LoadingIndicator from '../../components/utils/LoadingIndicator';
import { formatDistanceToNow } from 'date-fns';
import vi from 'date-fns/locale/vi';
import { ICONS } from '@/constants/icons';

const ExamResults = () => {
    const navigate = useNavigate();
    const { examId } = useParams();
    const { user, loading: authLoading } = useContext(AuthContext);

    // State
    const [results, setResults] = useState([]);
    const [examInfo, setExamInfo] = useState(null);
    const [loading, setLoading] = useState(true);
    const [sortBy, setSortBy] = useState('bestScore'); // 'bestScore', 'averageScore', 'totalAttempts', 'name'
    const [sortOrder, setSortOrder] = useState('desc'); // 'asc', 'desc'

    // Fetch exam results
    const fetchExamResults = useCallback(async () => {
        if (!user || !examId) return;
        
        setLoading(true);
        try {
            const response = await authApi.get(`/exams/${examId}/results`);
            
            if (response.data.success) {
                setResults(response.data.data || []);
                
                // Try to get exam info from first result or fetch separately
                if (response.data.data && response.data.data.length > 0) {
                    // If we have results, we might need to fetch exam info separately
                    try {
                        const examResponse = await authApi.get(`/exams/${examId}`);
                        if (examResponse.data.success) {
                            setExamInfo(examResponse.data.data);
                        }
                    } catch (err) {
                        console.error('Error fetching exam info:', err);
                    }
                }
            }
        } catch (error) {
            console.error('Error fetching exam results:', error);
        } finally {
            setLoading(false);
        }
    }, [user, examId]);

    // Navigate to student attempts
    const viewStudentAttempts = (studentId) => {
        navigate(`/student-attempts/${studentId}?examId=${examId}`);
    };

    // Sort results
    const sortedResults = React.useMemo(() => {
        if (!results) return [];
        
        const sorted = [...results].sort((a, b) => {
            let aValue, bValue;
            
            switch (sortBy) {
                case 'name':
                    aValue = a.student.name.toLowerCase();
                    bValue = b.student.name.toLowerCase();
                    break;
                case 'bestScore':
                    aValue = a.bestScore || 0;
                    bValue = b.bestScore || 0;
                    break;
                case 'averageScore':
                    aValue = a.averageScore || 0;
                    bValue = b.averageScore || 0;
                    break;
                case 'totalAttempts':
                    aValue = a.totalAttempts || 0;
                    bValue = b.totalAttempts || 0;
                    break;
                default:
                    return 0;
            }
            
            if (sortBy === 'name') {
                return sortOrder === 'asc' ? aValue.localeCompare(bValue) : bValue.localeCompare(aValue);
            } else {
                return sortOrder === 'asc' ? aValue - bValue : bValue - aValue;
            }
        });
        
        return sorted;
    }, [results, sortBy, sortOrder]);

    // Handle sort
    const handleSort = (field) => {
        if (sortBy === field) {
            setSortOrder(sortOrder === 'asc' ? 'desc' : 'asc');
        } else {
            setSortBy(field);
            setSortOrder('desc');
        }
    };

    // Calculate statistics
    const statistics = React.useMemo(() => {
        if (!results || results.length === 0) {
            return {
                totalStudents: 0,
                totalAttempts: 0,
                averageScore: 0,
                highestScore: 0,
                lowestScore: 0,
                passRate: 0
            };
        }

        const totalStudents = results.length;
        const totalAttempts = results.reduce((sum, result) => sum + (result.totalAttempts || 0), 0);
        const bestScores = results.map(result => result.bestScore || 0);
        const averageScore = bestScores.reduce((sum, score) => sum + score, 0) / totalStudents;
        const highestScore = Math.max(...bestScores);
        const lowestScore = Math.min(...bestScores);
        const passedStudents = bestScores.filter(score => score >= 5).length;
        const passRate = (passedStudents / totalStudents) * 100;

        return {
            totalStudents,
            totalAttempts,
            averageScore: Number(averageScore.toFixed(2)),
            highestScore,
            lowestScore,
            passRate: Number(passRate.toFixed(1))
        };
    }, [results]);

    // Get score color
    const getScoreColor = (score) => {
        if (score >= 8) return '#4CAF50';
        if (score >= 6.5) return '#8BC34A';
        if (score >= 5) return '#FF9800';
        if (score >= 3.5) return '#FF5722';
        return '#F44336';
    };

    // Get score emoji
    const getScoreEmoji = (score) => {
        if (score >= 8) return ICONS.TROPHY;
        if (score >= 6.5) return ICONS.MEDAL;
        if (score >= 5) return ICONS.THUMBS_UP;
        if (score >= 3.5) return ICONS.SAD;
        return ICONS.SAD;
    };

    // Check authentication
    useEffect(() => {
        if (!authLoading && !user) {
            navigate('/login', { replace: true });
        } else if (user && !user.role.includes('teacher') && !user.role.includes('TEACHER')) {
            navigate('/login', { replace: true });
        }
    }, [user, authLoading, navigate]);

    // Fetch data on mount
    useEffect(() => {
        fetchExamResults();
    }, [fetchExamResults]);

    return (
        <Box className="container" style={{ minHeight: '100vh', display: 'flex', flexDirection: 'column', paddingBottom: '60px', backgroundColor: '#f5f5f5' }}>
            <HeaderEdu
                title="Kết quả bài thi"
                subtitle={examInfo ? `${examInfo.title} - ${examInfo.subject?.name} • ${examInfo.timeLimit} phút • ${examInfo.questionsPerAttempt} câu` : "Đang tải thông tin bài thi..."}
                showBackButton
                onBackClick={() => navigate(-1)}
            />
            <HeaderSpacer />
            
            {/* Statistics */}
            <Box style={{ padding: '15px', backgroundColor: 'white', marginBottom: '10px' }}>
                <Text bold style={{ marginBottom: '15px' }}>Thống kê tổng quan</Text>
                
                <Box style={{ display: 'grid', gridTemplateColumns: 'repeat(2, 1fr)', gap: '15px', marginBottom: '15px' }}>
                    <Box style={{ backgroundColor: '#f0f8ff', borderRadius: '8px', padding: '12px', textAlign: 'center' }}>
                        <Text bold style={{ fontSize: '20px', color: '#0068ff' }}>
                            {statistics.totalStudents}
                        </Text>
                        <Text style={{ fontSize: '12px', color: '#666' }}>Học sinh tham gia</Text>
                    </Box>
                    
                    <Box style={{ backgroundColor: '#f0f8ff', borderRadius: '8px', padding: '12px', textAlign: 'center' }}>
                        <Text bold style={{ fontSize: '20px', color: '#FF9800' }}>
                            {statistics.totalAttempts}
                        </Text>
                        <Text style={{ fontSize: '12px', color: '#666' }}>Tổng lượt thi</Text>
                    </Box>
                </Box>

                <Box style={{ display: 'grid', gridTemplateColumns: 'repeat(2, 1fr)', gap: '15px', marginBottom: '15px' }}>
                    <Box style={{ backgroundColor: '#f8fff8', borderRadius: '8px', padding: '12px', textAlign: 'center' }}>
                        <Text bold style={{ fontSize: '20px', color: '#4CAF50' }}>
                            {statistics.averageScore}
                        </Text>
                        <Text style={{ fontSize: '12px', color: '#666' }}>Điểm trung bình</Text>
                    </Box>
                    
                    <Box style={{ backgroundColor: '#f8fff8', borderRadius: '8px', padding: '12px', textAlign: 'center' }}>
                        <Text bold style={{ fontSize: '20px', color: '#4CAF50' }}>
                            {statistics.passRate}%
                        </Text>
                        <Text style={{ fontSize: '12px', color: '#666' }}>Tỷ lệ đậu</Text>
                    </Box>
                </Box>

                <Box style={{ backgroundColor: '#f9f9f9', borderRadius: '8px', padding: '12px' }}>
                    <Box flex justifyContent="space-between">
                        <Box style={{ textAlign: 'center' }}>
                            <Text bold style={{ fontSize: '16px', color: '#4CAF50' }}>
                                {statistics.highestScore}
                            </Text>
                            <Text style={{ fontSize: '11px', color: '#666' }}>Cao nhất</Text>
                        </Box>
                        <Box style={{ textAlign: 'center' }}>
                            <Text bold style={{ fontSize: '16px', color: '#F44336' }}>
                                {statistics.lowestScore}
                            </Text>
                            <Text style={{ fontSize: '11px', color: '#666' }}>Thấp nhất</Text>
                        </Box>
                    </Box>
                </Box>
            </Box>

            {/* Sort Controls */}
            <Box style={{ padding: '15px', backgroundColor: 'white', marginBottom: '10px' }}>
                <Text bold style={{ marginBottom: '10px' }}>Sắp xếp theo</Text>
                <Box flex style={{ gap: '8px', flexWrap: 'wrap' }}>
                    {[
                        { key: 'bestScore', label: 'Điểm cao nhất' },
                        { key: 'averageScore', label: 'Điểm trung bình' },
                        { key: 'totalAttempts', label: 'Số lượt thi' },
                        { key: 'name', label: 'Tên học sinh' }
                    ].map((sort) => (
                        <Button
                            key={sort.key}
                            onClick={() => handleSort(sort.key)}
                            style={{
                                backgroundColor: sortBy === sort.key ? '#0068ff' : '#f0f0f0',
                                color: sortBy === sort.key ? 'white' : '#333',
                                fontSize: '12px',
                                padding: '6px 12px',
                                border: 'none'
                            }}
                        >
                            {sort.label}
                            {sortBy === sort.key && (
                                <Text style={{ marginLeft: '4px' }}>
                                    {sortOrder === 'asc' ? '↑' : '↓'}
                                </Text>
                            )}
                        </Button>
                    ))}
                </Box>
            </Box>

            {/* Results List */}
            <Box style={{ flex: 1, backgroundColor: 'white', padding: '15px' }}>
                <Box flex justifyContent="space-between" alignItems="center" style={{ marginBottom: '15px' }}>
                    <Text bold size="large">Kết quả học sinh ({results.length})</Text>
                    <Button
                        onClick={fetchExamResults}
                        style={{ backgroundColor: 'transparent', color: '#0068ff', fontSize: '20px' }}
                    >
                        {ICONS.REFRESH}
                    </Button>
                </Box>

                {loading ? (
                    <Box style={{ display: 'flex', justifyContent: 'center', padding: '40px' }}>
                        <LoadingIndicator />
                    </Box>
                ) : sortedResults.length > 0 ? (
                    <List style={{ display: 'flex', flexDirection: 'column', gap: '12px' }}>
                        {sortedResults.map((result, index) => (
                            <Box 
                                key={result.student._id}
                                style={{
                                    backgroundColor: '#f9f9f9',
                                    borderRadius: '12px',
                                    padding: '15px',
                                    boxShadow: '0 2px 8px rgba(0,0,0,0.1)',
                                    cursor: 'pointer',
                                    position: 'relative'
                                }}
                                onClick={() => viewStudentAttempts(result.student._id)}
                            >
                                {/* Ranking Badge */}
                                <Box style={{
                                    position: 'absolute',
                                    top: '10px',
                                    right: '10px',
                                    backgroundColor: index < 3 ? '#FFD700' : '#f0f0f0',
                                    color: index < 3 ? '#333' : '#666',
                                    borderRadius: '12px',
                                    padding: '2px 8px',
                                    fontSize: '11px',
                                    fontWeight: 'bold'
                                }}>
                                    #{index + 1}
                                </Box>

                                {/* Student Info */}
                                <Box flex alignItems="center" style={{ marginBottom: '10px' }}>
                                    <Box style={{
                                        width: '40px',
                                        height: '40px',
                                        borderRadius: '50%',
                                        backgroundColor: '#e8f0fe',
                                        color: '#0068ff',
                                        display: 'flex',
                                        alignItems: 'center',
                                        justifyContent: 'center',
                                        fontSize: '18px',
                                        marginRight: '12px'
                                    }}>
                                        {getScoreEmoji(result.bestScore)}
                                    </Box>
                                    <Box style={{ flex: 1 }}>
                                        <Text bold style={{ fontSize: '16px', marginBottom: '2px' }}>
                                            {result.student.name}
                                        </Text>
                                        <Text style={{ fontSize: '12px', color: '#666' }}>
                                            MSHS: {result.student.studentId}
                                        </Text>
                                    </Box>
                                </Box>

                                {/* Scores */}
                                <Box style={{ display: 'grid', gridTemplateColumns: 'repeat(3, 1fr)', gap: '10px', marginBottom: '10px' }}>
                                    <Box style={{ textAlign: 'center' }}>
                                        <Text bold style={{ 
                                            fontSize: '18px', 
                                            color: getScoreColor(result.bestScore),
                                            marginBottom: '2px'
                                        }}>
                                            {result.bestScore?.toFixed(1) || '0.0'}
                                        </Text>
                                        <Text style={{ fontSize: '11px', color: '#666' }}>Điểm cao nhất</Text>
                                    </Box>
                                    
                                    <Box style={{ textAlign: 'center' }}>
                                        <Text bold style={{ 
                                            fontSize: '18px', 
                                            color: getScoreColor(result.averageScore),
                                            marginBottom: '2px'
                                        }}>
                                            {result.averageScore?.toFixed(1) || '0.0'}
                                        </Text>
                                        <Text style={{ fontSize: '11px', color: '#666' }}>Điểm trung bình</Text>
                                    </Box>
                                    
                                    <Box style={{ textAlign: 'center' }}>
                                        <Text bold style={{ fontSize: '18px', color: '#FF9800', marginBottom: '2px' }}>
                                            {result.totalAttempts || 0}
                                        </Text>
                                        <Text style={{ fontSize: '11px', color: '#666' }}>Lượt thi</Text>
                                    </Box>
                                </Box>

                                {/* Latest Attempt */}
                                {result.latestAttempt && (
                                    <Box style={{ 
                                        backgroundColor: '#f0f8ff', 
                                        borderRadius: '8px', 
                                        padding: '8px',
                                        marginBottom: '8px'
                                    }}>
                                        <Text style={{ fontSize: '12px', color: '#666', marginBottom: '3px' }}>
                                            Lần thi gần nhất
                                        </Text>
                                        <Box flex justifyContent="space-between" alignItems="center">
                                            <Text bold style={{ 
                                                fontSize: '14px',
                                                color: getScoreColor(result.latestAttempt.score)
                                            }}>
                                                {result.latestAttempt.score?.toFixed(1)} điểm
                                            </Text>
                                            <Text style={{ fontSize: '11px', color: '#888' }}>
                                                {formatDistanceToNow(new Date(result.lastAttemptDate), { 
                                                    locale: vi, 
                                                    addSuffix: true 
                                                })}
                                            </Text>
                                        </Box>
                                    </Box>
                                )}

                                {/* Action Arrow */}
                                <Box flex justifyContent="flex-end">
                                    <Text style={{ fontSize: '16px', color: '#0068ff' }}>→</Text>
                                </Box>
                            </Box>
                        ))}
                    </List>
                ) : (
                    <Box style={{ textAlign: 'center', padding: '40px', color: '#666' }}>
                        <Text style={{ fontSize: '48px', marginBottom: '10px' }}>{ICONS.STATS}</Text>
                        <Text bold size="large" style={{ marginBottom: '5px' }}>Chưa có kết quả</Text>
                        <Text style={{ fontSize: '14px' }}>Chưa có học sinh nào làm bài thi này</Text>
                    </Box>
                )}
            </Box>

            <BottomNavigationEdu />
        </Box>
    );
};

export default ExamResults; 
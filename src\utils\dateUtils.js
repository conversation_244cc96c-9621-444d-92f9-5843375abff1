import { format, isToday, isTomorrow } from 'date-fns';
import vi from 'date-fns/locale/vi';

export const formatEventDate = (startTime) => {
    const eventDate = new Date(startTime);
    if (isNaN(eventDate.getTime())) {
        console.error('Invalid startTime for date:', startTime);
        return 'Ngày không xác định';
    }
    if (isToday(eventDate)) return 'Hôm nay';
    if (isTomorrow(eventDate)) return 'Ngày mai';
    return format(eventDate, 'dd/MM', { locale: vi });
};

export const formatEventTime = (startTime, endTime, description) => {
    // Parse startTime từ chuỗi ISO và lấy giờ trực tiếp
    const start = new Date(startTime);
    if (isNaN(start.getTime())) {
        console.error('Invalid startTime:', startTime);
        return '<PERSON>i<PERSON> không xác định';
    }

    // Lấy giờ và phút từ startTime (giữ nguyên giờ UTC)
    const hours = start.getUTCHours().toString().padStart(2, '0');
    const minutes = start.getUTCMinutes().toString().padStart(2, '0');
    const startStr = `${hours}:${minutes}`;

    if (endTime) {
        const end = new Date(endTime);
        if (isNaN(end.getTime())) {
            console.error('Invalid endTime:', endTime);
            return `${description ? description + ' • ' : ''}${startStr}`;
        }
        const endHours = end.getUTCHours().toString().padStart(2, '0');
        const endMinutes = end.getUTCMinutes().toString().padStart(2, '0');
        const endStr = `${endHours}:${endMinutes}`;
        return `${description ? description + ' • ' : ''}${startStr} - ${endStr}`;
    }

    // Debug dữ liệu
    console.log('formatEventTime:', { startTime, startStr, description });

    return `${description ? description + ': ' : ''}${startStr}`;
};

// Format date chỉ ngày tháng năm
export const formatDate = (dateString) => {
    try {
        const date = new Date(dateString);
        return date.toLocaleDateString('vi-VN', {
            day: '2-digit',
            month: '2-digit',
            year: 'numeric'
        });
    } catch {
        return 'N/A';
    }
};

// Format date với cả thời gian đến giây
export const formatDateTime = (dateString) => {
    try {
        const date = new Date(dateString);
        return date.toLocaleString('vi-VN', {
            day: '2-digit',
            month: '2-digit',
            year: 'numeric',
            hour: '2-digit',
            minute: '2-digit',
            second: '2-digit'
        });
    } catch {
        return 'N/A';
    }
};
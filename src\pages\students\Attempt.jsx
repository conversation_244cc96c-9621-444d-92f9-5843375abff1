import { useEffect, useState, useContext, useCallback, useMemo } from 'react';
import { Box, Text, Button, useNavigate, useParams, useLocation } from 'zmp-ui';
import HeaderEdu from '../../components/HeaderEdu';
import HeaderSpacer from '../../components/utils/HeaderSpacer';
import { AuthContext } from '../../context/AuthContext';
import { authApi } from '../../utils/api';
import Loading from '@/components/utils/Loading';
import useApiCache from '../../hooks/useApiCache';
import { ICONS } from '@/constants/icons';
import useNotification from '../../hooks/useNotification';


const Attempt = () => {
    const { attemptId } = useParams();
    const navigate = useNavigate();
    const location = useLocation();
    const { user } = useContext(AuthContext);
    const { error } = useNotification();
    const [currentQuestionIndex, setCurrentQuestionIndex] = useState(() => {
        // Khôi phục vị trí câu hỏi từ localStorage nếu có
        const savedIndex = localStorage.getItem(`attempt_${attemptId}_currentIndex`);
        return savedIndex ? parseInt(savedIndex, 10) : 0;
    });
    const [showFormulaModal, setShowFormulaModal] = useState(false);
    const [showSubmitModal, setShowSubmitModal] = useState(false);

    // Hàm gọi API lấy dữ liệu attempt
    const fetchAttemptData = useCallback(async () => {
        if (!user || !attemptId) return null;

        try {
            const response = await authApi.get(`/exams/exam-attempts/${attemptId}`);
            const data = response.data.data;

            // Tính thời gian còn lại
            const startTime = new Date(data.attempt.startTime).getTime();
            const timeLimit = data.exam.timeLimit * 60 * 1000; // phút -> ms
            const now = Date.now();
            const remaining = startTime + timeLimit - now;

            return {
                ...data,
                remainingTimeInSeconds: remaining > 0 ? Math.floor(remaining / 1000) : 0
            };
        } catch (err) {
            console.error('Error fetching attempt:', err);
            throw new Error('Lỗi khi tải bài làm');
        }
    }, [user, attemptId]);

    // Sử dụng useApiCache để cache kết quả API
    const {
        data: attemptData,
        loading,
        error: apiError,
        refetch
    } = useApiCache(fetchAttemptData, [user, attemptId], {
        cacheKey: `attempt_data_${attemptId}`,
        enabled: !!user && !!attemptId,
        cacheTime: 1 * 60 * 1000, // Cache 1 phút
    });

    // Trích xuất dữ liệu từ attemptData
    const attempt = attemptData?.attempt;
    const questions = attemptData?.questions || [];

    // Tính thời gian còn lại
    const calculateRemainingTime = useCallback(() => {
        if (!attemptData?.attempt?.startTime || !attemptData?.exam?.timeLimit) return 0;

        const startTime = new Date(attemptData.attempt.startTime).getTime();
        const timeLimit = attemptData.exam.timeLimit * 60 * 1000; // phút -> ms
        const now = Date.now();
        const remaining = startTime + timeLimit - now;

        return remaining > 0 ? Math.floor(remaining / 1000) : 0;
    }, [attemptData]);

    // Sử dụng useState để quản lý thời gian còn lại
    const [remainingTime, setRemainingTime] = useState(0);

    // Đếm ngược thời gian
    useEffect(() => {
        // Cập nhật thời gian ban đầu khi dữ liệu được tải
        if (attemptData) {
            setRemainingTime(calculateRemainingTime());
        }
    }, [attemptData, calculateRemainingTime]);

    // Đếm ngược thời gian
    useEffect(() => {
        if (remainingTime <= 0) return;

        const timer = setInterval(() => {
            setRemainingTime((prev) => {
                if (prev <= 1) {
                    clearInterval(timer);
                    handleSubmit();
                    return 0;
                }
                return prev - 1;
            });
        }, 1000);

        return () => clearInterval(timer);
    }, [remainingTime]);

    // Format thời gian
    const formattedTime = useMemo(() => {
        const minutes = Math.floor(remainingTime / 60);
        const secs = remainingTime % 60;
        return `${minutes}:${secs < 10 ? '0' : ''}${secs}`;
    }, [remainingTime]);

    // Hàm nộp bài
    const handleSubmit = useCallback(async () => {
        try {
            await authApi.put(`/exams/exam-attempts/${attemptId}/submit`);
            // Xóa dữ liệu lưu trong localStorage khi nộp bài
            localStorage.removeItem(`attempt_${attemptId}_currentIndex`);

            navigate(`/attempt/${attemptId}/results`, {
                state: {
                    previousPath: location.pathname,
                    previousState: { currentQuestionIndex },
                    examId: attemptData?.exam?._id // Thêm examId vào state
                }
            });
        } catch (err) {
            console.error('Error submitting attempt:', err);
            error('Lỗi khi nộp bài');
        }
    }, [attemptId, navigate, location.pathname, currentQuestionIndex, attemptData?.exam?._id]);

    // Chọn đáp án - Xử lý ngầm không hiển thị loading
    const handleAnswer = (questionId, optionId) => {
        // Lưu ý: UI sẽ tự động cập nhật khi người dùng nhấn vào đáp án do CSS đã được thiết lập
        // Chúng ta không cần cập nhật state vì CSS sẽ thay đổi giao diện dựa trên sự kiện click

        // Gọi API ngầm để lưu đáp án
        authApi.put(`/exams/exam-attempts/${attemptId}/answer`, {
            questionId,
            selectedOption: optionId
        })
        .then(() => {
            // Refresh dữ liệu ngầm sau khi lưu thành công
            // Không hiển thị loading
            setTimeout(() => {
                refetch({ showLoading: false });
            }, 500);
        })
        .catch(err => {
            console.error('Error saving answer:', err);
            // Không hiển thị lỗi để tránh làm gián đoạn trải nghiệm người dùng
        });
    };

    // Lưu vị trí câu hỏi hiện tại vào localStorage
    useEffect(() => {
        localStorage.setItem(`attempt_${attemptId}_currentIndex`, currentQuestionIndex.toString());
    }, [attemptId, currentQuestionIndex]);

    // Tính số câu đã trả lời
    const answeredCount = questions.filter((q) => q.selectedOption).length;
    const totalQuestions = questions.length;
    const progress = (answeredCount / totalQuestions) * 100;

    // Câu hỏi hiện tại
    const currentQuestion = questions[currentQuestionIndex];



    return (
        <Box className="container" style={{ minHeight: '100vh', display: 'flex', flexDirection: 'column' }}>
            <HeaderEdu
                title={attemptData?.exam?.title || "Bài làm"}
                showBackButton={true}
                onBackClick={() => {
                    console.log('Attempt: handleBack called');
                    console.log('Attempt: location.state', location.state);
                    
                    // Lấy examId từ attemptData
                    const examId = attemptData?.exam?._id;
                    console.log('Attempt: examId', examId);
                    
                    if (examId) {
                        // Restore state for ExerciseDetail từ sessionStorage hoặc location.state
                        const storedState = sessionStorage.getItem('exerciseDetailStateFromResults');
                        let exerciseDetailState = null;
                        
                        if (storedState) {
                            try {
                                exerciseDetailState = JSON.parse(storedState);
                                console.log('Attempt: Restored state for ExerciseDetail', exerciseDetailState);
                                // KHÔNG clear sessionStorage ở đây, để ExerciseDetail tự clear
                            } catch (e) {
                                console.error('Error parsing stored state:', e);
                            }
                        }
                        
                        // Quay về trang chi tiết bài tập với state được restore
                        console.log('Attempt: Navigating back to ExerciseDetail with state');
                        navigate(`/exercises/${examId}`, {
                            state: exerciseDetailState
                        });
                    } else {
                        // Nếu không có examId, quay về trang Exams
                        console.log('Attempt: No examId, fallback to /exams');
                        navigate('/exams', { replace: true });
                    }
                }}
            />
            <HeaderSpacer />

            {/* Modal xác nhận nộp bài */}
            {showSubmitModal && (
                <Box className="modal-overlay" style={{ position: 'fixed', top: 0, left: 0, right: 0, bottom: 0, backgroundColor: 'rgba(0, 0, 0, 0.5)', display: 'flex', alignItems: 'center', justifyContent: 'center', zIndex: 1000 }}>
                    <Box className="modal" style={{ backgroundColor: 'white', borderRadius: '8px', width: '90%', maxWidth: '400px' }}>
                        <Box className="modal-header" style={{ padding: '15px', borderBottom: '1px solid #eee', display: 'flex', justifyContent: 'space-between', alignItems: 'center' }}>
                            <Text className="modal-title" style={{ fontSize: '18px', fontWeight: 'bold' }}>
                                Xác nhận nộp bài
                            </Text>
                            <Button className="modal-close" style={{ fontSize: '24px', background: 'none', border: 'none', cursor: 'pointer', color: '#666' }} onClick={() => setShowSubmitModal(false)}>
                                ×
                            </Button>
                        </Box>
                        <Box className="modal-body" style={{ padding: '15px' }}>
                            <Text className="confirmation-text" style={{ fontSize: '16px', marginBottom: '15px', textAlign: 'center' }}>
                                Bạn chưa trả lời {totalQuestions - answeredCount}/{totalQuestions} câu hỏi.<br />
                                Bạn có chắc chắn muốn nộp bài?
                            </Text>
                        </Box>
                        <Box className="modal-footer" style={{ padding: '15px', borderTop: '1px solid #eee', display: 'flex', justifyContent: 'flex-end' }}>
                            <Box className="confirmation-actions" style={{ display: 'flex', gap: '10px' }}>
                                <Button className="btn btn-outline" style={{ flex: 1 }} onClick={() => setShowSubmitModal(false)}>
                                    Làm tiếp
                                </Button>
                                <Button
                                    className="btn btn-primary"
                                    style={{ flex: 1 }}
                                    onClick={() => {
                                        setShowSubmitModal(false);
                                        handleSubmit();
                                    }}
                                >
                                    Nộp bài
                                </Button>
                            </Box>
                        </Box>
                    </Box>
                </Box>
            )}

            {/* Modal công thức */}
            {showFormulaModal && (
                <Box className="modal-overlay" style={{ position: 'fixed', top: 0, left: 0, right: 0, bottom: 0, backgroundColor: 'rgba(0, 0, 0, 0.5)', display: 'flex', alignItems: 'center', justifyContent: 'center', zIndex: 1000 }}>
                    <Box className="modal" style={{ backgroundColor: 'white', borderRadius: '8px', width: '90%', maxWidth: '400px', maxHeight: '90vh', overflowY: 'auto' }}>
                        <Box className="modal-header" style={{ padding: '15px', borderBottom: '1px solid #eee', display: 'flex', justifyContent: 'space-between', alignItems: 'center' }}>
                            <Text className="modal-title" style={{ fontSize: '18px', fontWeight: 'bold' }}>
                                Công thức {attempt?.exam?.subject?.name}
                            </Text>
                            <Button className="modal-close" style={{ fontSize: '24px', background: 'none', border: 'none', cursor: 'pointer', color: '#666' }} onClick={() => setShowFormulaModal(false)}>
                                ×
                            </Button>
                        </Box>
                        <Box className="modal-body" style={{ padding: '15px' }}>
                            <Box className="formula-list" style={{ display: 'flex', flexDirection: 'column', gap: '12px' }}>
                                {/* Giả lập danh sách công thức, bạn có thể gọi API để lấy công thức thực tế */}
                                <Box className="formula-item" style={{ padding: '10px', borderRadius: '6px', backgroundColor: '#f8f9fa' }}>
                                    <Text className="formula-name" style={{ fontWeight: '500', marginBottom: '5px', color: '#0068ff' }}>
                                        Đạo hàm của hàm số bậc 3
                                    </Text>
                                    <Text className="formula-content" style={{ fontSize: '14px', color: '#333' }}>
                                        f(x) = ax³ + bx² + cx + d<br />
                                        f'(x) = 3ax² + 2bx + c
                                    </Text>
                                </Box>
                                {/* Thêm các công thức khác nếu cần */}
                            </Box>
                        </Box>
                    </Box>
                </Box>
            )}

            {/* Box hiển thị thời gian với đồng hồ cát */}
            <Box
                className="timer-box"
                style={{
                    display: 'flex',
                    alignItems: 'center',
                    justifyContent: 'center',
                    backgroundColor: remainingTime <= 30 ? '#ff4d4f' : '#fff3cd',
                    border: '2px solid #ff6f61',
                    borderRadius: '8px',
                    padding: '12px 20px',
                    margin: '15px auto',
                    maxWidth: '300px',
                    boxShadow: '0 4px 8px rgba(0, 0, 0, 0.2)',
                    fontSize: '18px',
                    fontWeight: 'bold',
                    color: remainingTime <= 30 ? 'white' : '#d32f2f',
                    position: 'relative',
                    zIndex: 10,
                }}
            >
                <Box
                    className="hourglass"
                    style={{
                        width: '24px',
                        height: '24px',
                        marginRight: '10px',
                        position: 'relative',
                        animation: 'rotate 2s infinite linear',
                    }}
                >
                    {/* SVG đồng hồ cát */}
                    <svg width="24" height="24" viewBox="0 0 24 24" fill="none" xmlns="http://www.w3.org/2000/svg">
                        <path
                            d="M5 2V6H6L7 7V17L6 18H5V22H19V18H18L17 17V7L18 6H19V2H5ZM7 4H17V6H16L15 7V17L16 18H17V20H7V18H8L9 17V7L8 6H7V4Z"
                            fill={remainingTime <= 30 ? 'white' : '#d32f2f'}
                        />
                        <path
                            d="M12 8C12 8 11 10 11 12C11 14 12 16 12 16C12 16 13 14 13 12C13 10 12 8 12 8Z"
                            fill={remainingTime <= 30 ? 'white' : '#d32f2f'}
                            className="sand"
                            style={{ animation: 'sandFall 2s infinite linear' }}
                        />
                    </svg>
                </Box>
                <Text>{formattedTime}</Text>
            </Box>

            <Box className="page-container" style={{ padding: '15px', display: 'flex', flexDirection: 'column', gap: '15px', flexGrow: 1 }}>
                {loading ? (
                    <Loading />
                ) : apiError ? (
                    <Text style={{ color: '#ff3b30', textAlign: 'center', padding: '20px' }}>{apiError}</Text>
                ) : !attemptData ? (
                    <Text style={{ color: '#ff3b30', textAlign: 'center', padding: '20px' }}>Không tìm thấy bài làm</Text>
                ) : (
                    <>
                        {/* Thanh tiến độ */}
                        <Box className="progress-section" style={{ backgroundColor: 'white', borderRadius: '8px', padding: '15px', boxShadow: '0 2px 4px rgba(0, 0, 0, 0.1)' }}>
                            <Box className="progress-info" style={{ display: 'flex', justifyContent: 'space-between', marginBottom: '8px' }}>
                                <Text className="progress-text" style={{ fontSize: '14px', color: '#666' }}>
                                    Câu {currentQuestionIndex + 1}/{totalQuestions}
                                </Text>
                                <Text className="progress-text" style={{ fontSize: '14px', color: '#666' }}>
                                    Đã trả lời: {answeredCount}/{totalQuestions}
                                </Text>
                            </Box>
                            <Box className="progress-bar" style={{ height: '6px', backgroundColor: '#e0e0e0', borderRadius: '3px', overflow: 'hidden' }}>
                                <Box className="progress-fill" style={{ height: '100%', backgroundColor: '#0068ff', width: `${progress}%` }} />
                            </Box>
                            <Box className="question-nav" style={{ display: 'flex', flexWrap: 'wrap', gap: '8px', marginTop: '12px' }}>
                                {questions.map((q, index) => (
                                    <Box
                                        key={q._id}
                                        className={`question-nav-item ${index === currentQuestionIndex ? 'current' : ''} ${q.selectedOption ? 'answered' : ''}`}
                                        style={{
                                            width: '32px',
                                            height: '32px',
                                            borderRadius: '50%',
                                            display: 'flex',
                                            alignItems: 'center',
                                            justifyContent: 'center',
                                            fontSize: '14px',
                                            fontWeight: '500',
                                            backgroundColor: index === currentQuestionIndex ? '#0068ff' : q.selectedOption ? '#e8f0fe' : '#f0f0f0',
                                            color: index === currentQuestionIndex ? 'white' : q.selectedOption ? '#0068ff' : '#666',
                                            cursor: 'pointer',
                                            border: q.selectedOption && index !== currentQuestionIndex ? '1px solid #0068ff' : 'none',
                                        }}
                                        onClick={() => setCurrentQuestionIndex(index)}
                                    >
                                        {index + 1}
                                    </Box>
                                ))}
                            </Box>
                        </Box>

                        {/* Câu hỏi hiện tại */}
                        {currentQuestion && (
                            <Box className="question-container" style={{ backgroundColor: 'white', borderRadius: '8px', padding: '20px', boxShadow: '0 2px 4px rgba(0, 0, 0, 0.1)', flexGrow: 1 }}>
                                <Text className="question-number" style={{ color: '#0068ff', fontWeight: 'bold', fontSize: '16px', marginBottom: '12px' }}>
                                    Câu {currentQuestionIndex + 1}
                                </Text>
                                <Text className="question-content" style={{ fontSize: '16px', lineHeight: '1.6', marginBottom: '20px' }} dangerouslySetInnerHTML={{ __html: currentQuestion?.content }} />
                                <Box className="options-container" style={{ display: 'flex', flexDirection: 'column', gap: '12px' }}>
                                    {currentQuestion?.options.map((option, index) => (
                                        <Box
                                            key={option._id}
                                            className={`option ${currentQuestion.selectedOption === index + 1 ? 'selected' : ''}`}
                                            style={{
                                                height: 'auto', // Mở rộng theo nội dung
                                                minHeight: '28px', // Chiều cao tối thiểu
                                                padding: '15px',
                                                borderRadius: '8px',
                                                border: `1px solid ${currentQuestion.selectedOption === index + 1 ? '#0068ff' : '#e0e0e0'}`,
                                                display: 'flex',
                                                alignItems: 'flex-start', // Căn đầu dòng thay vì giữa để đẹp khi xuống dòng
                                                cursor: 'pointer',
                                                backgroundColor: currentQuestion.selectedOption === index + 1 ? '#e8f0fe' : 'white',
                                                flexWrap: 'wrap', // Cho phép xuống dòng khi không đủ không gian
                                                gap: '8px', // Khoảng cách giữa các phần tử khi xuống dòng
                                                transition: 'all 0.2s ease', // Thêm hiệu ứng chuyển đổi mượt mà
                                            }}
                                            onClick={() => handleAnswer(currentQuestion._id, index + 1)}
                                        >
                                            <Box
                                                className="option-marker"
                                                style={{
                                                    minWidth: '28px',
                                                    height: 'auto',
                                                    borderRadius: '4px',
                                                    backgroundColor: currentQuestion.selectedOption === index + 1 ? '#0068ff' : '#f0f0f0',
                                                    display: 'flex',
                                                    alignItems: 'center',
                                                    justifyContent: 'center',
                                                    padding: '0 8px',
                                                    marginRight: '12px',
                                                    fontWeight: 'bold',
                                                    color: currentQuestion.selectedOption === index + 1 ? 'white' : '#666',
                                                    transition: 'all 0.2s ease', // Thêm hiệu ứng chuyển đổi mượt mà
                                                }}
                                            >
                                                {option.text}
                                            </Box>
                                            <Text
                                                className="option-content"
                                                style={{
                                                    fontSize: '15px',
                                                    flex: 1, // Chiếm không gian còn lại
                                                    whiteSpace: 'normal', // Cho phép xuống dòng
                                                    wordWrap: 'break-word', // Đảm bảo text dài xuống dòng đúng
                                                    lineHeight: '1.5', // Tăng khoảng cách dòng
                                                }}
                                                dangerouslySetInnerHTML={{ __html: option.content }}
                                            />
                                        </Box>
                                    ))}
                                </Box>
                                <Box className="navigation-buttons" style={{ display: 'flex', justifyContent: 'space-between', marginTop: '20px' }}>
                                    <Button
                                        className="btn btn-outline"
                                        disabled={currentQuestionIndex === 0}
                                        onClick={() => setCurrentQuestionIndex((prev) => prev - 1)}
                                    >
                                        <Text style={{ fontSize: '16px' }}>←</Text>
                                    </Button>
                                    <Button
                                        className="btn btn-primary"
                                        disabled={currentQuestionIndex === totalQuestions - 1}
                                        onClick={() => setCurrentQuestionIndex((prev) => prev + 1)}
                                    >
                                        <Text style={{ fontSize: '16px' }}>→</Text>
                                    </Button>
                                </Box>
                            </Box>
                        )}

                        {/* Phần nộp bài */}
                        <Box className="submit-section" style={{ backgroundColor: 'white', borderRadius: '8px', padding: '15px', boxShadow: '0 2px 4px rgba(0, 0, 0, 0.1)', marginTop: '10px' }}>
                            <Box className="submit-warning" style={{ color: '#856404', backgroundColor: '#fff3cd', padding: '10px', borderRadius: '4px', fontSize: '14px', marginBottom: '15px', display: 'flex', alignItems: 'center', gap: '8px' }}>
                                <Text style={{ fontSize: '18px' }}>{ICONS.WARNING}</Text>
                                <Text>Bạn hãy kiểm tra lại trước khi nộp bài</Text>
                            </Box>
                            <Box className="submit-stats" style={{ display: 'flex', justifyContent: 'space-around', marginBottom: '15px' }}>
                                <Box className="stat-item" style={{ textAlign: 'center' }}>
                                    <Text className="stat-value" style={{ fontSize: '18px', fontWeight: 'bold', color: '#0068ff' }}>{answeredCount}</Text>
                                    <Text className="stat-label" style={{ fontSize: '12px', color: '#666' }}>Đã trả lời</Text>
                                </Box>
                                <Box className="stat-item" style={{ textAlign: 'center' }}>
                                    <Text className="stat-value" style={{ fontSize: '18px', fontWeight: 'bold', color: '#0068ff' }}>{totalQuestions - answeredCount}</Text>
                                    <Text className="stat-label" style={{ fontSize: '12px', color: '#666' }}>Chưa trả lời</Text>
                                </Box>
                                <Box className="stat-item" style={{ textAlign: 'center' }}>
                                    <Text className="stat-value" style={{ fontSize: '18px', fontWeight: 'bold', color: '#0068ff' }}>{Math.round(progress)}%</Text>
                                    <Text className="stat-label" style={{ fontSize: '12px', color: '#666' }}>Hoàn thành</Text>
                                </Box>
                            </Box>
                            <Button className="btn btn-submit" style={{ backgroundColor: '#28a745', color: 'white', width: '100%', padding: '14px' }} onClick={() => setShowSubmitModal(true)}>
                                <Text>{ICONS.DRAFT} Nộp bài</Text>
                            </Button>
                        </Box>

                        {/* Nút công thức */}
                        <Button
                            className="formula-button"
                            style={{
                                width: '40px',
                                height: '40px',
                                borderRadius: '50%',
                                backgroundColor: '#f0f6ff',
                                color: '#0068ff',
                                display: 'flex',
                                alignItems: 'center',
                                justifyContent: 'center',
                                fontSize: '20px',
                                position: 'fixed',
                                bottom: '20px',
                                right: '20px',
                                boxShadow: '0 2px 10px rgba(0, 0, 0, 0.1)',
                                zIndex: 100,
                            }}
                            onClick={() => setShowFormulaModal(true)}
                        >
                            {ICONS.STATS}
                        </Button>
                    </>
                )}
            </Box>
        </Box>
    );
};

export default Attempt;

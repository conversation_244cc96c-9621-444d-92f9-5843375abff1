import React, { useState, useEffect } from 'react';
import { Box, Text, List, useNavigate, Modal, Button, Input } from 'zmp-ui';
import HeaderEdu from '../../components/HeaderEdu';
import HeaderSpacer from '../../components/utils/HeaderSpacer';
import BottomNavigationEdu from '../../components/BottomNavigationEdu';
import SmartSelect from '../../components/utils/SmartSelect';
import { authApi } from '../../utils/api';
import { ICONS } from '../../constants/icons';
import LoadingIndicator from '../../components/utils/LoadingIndicator';
import { formatDistanceToNow } from 'date-fns';
import vi from 'date-fns/locale/vi';
import { formatDateTime } from '../../utils/dateUtils';
import useNotification from '../../hooks/useNotification';

const ViolationsList = () => {
    const navigate = useNavigate();
    const notification = useNotification();
    const [violations, setViolations] = useState([]);
    const [loading, setLoading] = useState(true);
    const [selectedViolation, setSelectedViolation] = useState(null);
    const [detailVisible, setDetailVisible] = useState(false);
    const [appealProcessing, setAppealProcessing] = useState(false);

    const [page, setPage] = useState(1);
    const [hasMore, setHasMore] = useState(true);
    const [filters, setFilters] = useState({
        classId: '',
        violationType: '',
        status: '',
        schoolYear: '',
        startDate: '',
        endDate: ''
    });
    const [classes, setClasses] = useState([]);
    const [violationTypes, setViolationTypes] = useState([]);

    // Fetch initial data
    useEffect(() => {
        fetchInitialData();
    }, []);

    // Fetch violations when filters change
    useEffect(() => {
        fetchViolations(1, false);
    }, [filters]);

    const fetchInitialData = async () => {
        try {
            const [classesRes, configRes] = await Promise.all([
                authApi.get('/directory/teacher/classes'),
                authApi.get('/violations/config')
            ]);
            
            // Ensure classes is always an array
            const classesData = Array.isArray(classesRes.data?.data) ? classesRes.data.data : 
                              Array.isArray(classesRes.data) ? classesRes.data : [];
            
            // Ensure violationTypes is always an array  
            const violationTypesData = Array.isArray(configRes.data?.data?.violationTypes) ? configRes.data.data.violationTypes :
                                     Array.isArray(configRes.data?.violationTypes) ? configRes.data.violationTypes : [];
            
            setClasses(classesData);
            setViolationTypes(violationTypesData);
        } catch (error) {
            console.error('Error fetching initial data:', error);
            // Ensure arrays are set even on error
            setClasses([]);
            setViolationTypes([]);
        }
    };

    // Fetch violations
    const fetchViolations = async (pageNum = 1, append = false) => {
        try {
            if (!append) setLoading(true);
            
            const params = new URLSearchParams({
                page: pageNum.toString(),
                limit: '10'
            });

            // Add filters
            if (filters.classId) params.append('classId', filters.classId);
            if (filters.violationType) params.append('violationType', filters.violationType);
            if (filters.status) params.append('status', filters.status);
            if (filters.schoolYear) params.append('schoolYear', filters.schoolYear);
            if (filters.startDate) params.append('startDate', filters.startDate);
            if (filters.endDate) params.append('endDate', filters.endDate);

            const response = await authApi.get(`/violations?${params.toString()}`);
            // Handle different response structures
            const newViolations = response.data?.data?.docs || response.data?.violations || response.data?.data || [];
            
            if (append) {
                setViolations(prev => [...prev, ...newViolations]);
            } else {
                setViolations(newViolations);
            }
            
            // Handle different pagination structures
            const pagination = response.data?.data || response.data?.pagination || {};
            setHasMore(pagination.hasNextPage || pagination.hasNext || false);
            setPage(pageNum);
        } catch (error) {
            console.error('Error fetching violations:', error);
        } finally {
            setLoading(false);
        }
    };

    // Get violation type label
    const getViolationTypeLabel = (type) => {
        const violationType = violationTypes.find(vt => vt.code === type);
        return violationType ? violationType.label : type;
    };

    // Get status label and color
    const getStatusInfo = (status) => {
        const statusMap = {
            'pending': { label: 'Chờ xử lý', color: '#f39c12', bg: '#fff3cd' },
            'processed': { label: 'Đã xử lý', color: '#28a745', bg: '#d4edda' },
            'appealed': { label: 'Đã khiếu nại', color: '#007bff', bg: '#d1ecf1' },
            'appeal_approved': { label: 'Khiếu nại được chấp nhận', color: '#28a745', bg: '#d4edda' },
            'appeal_rejected': { label: 'Khiếu nại bị từ chối', color: '#dc3545', bg: '#f8d7da' }
        };
        return statusMap[status] || { label: status, color: '#6c757d', bg: '#f8f9fa' };
    };

    // Handle violation detail
    const handleViolationDetail = async (violation) => {
        try {
            const response = await authApi.get(`/violations/${violation._id}`);
            console.log('Violation detail response:', response.data);
            // Handle the correct data structure
            const violationData = response.data.data || response.data;
            console.log('Setting violation data:', violationData);
            setSelectedViolation(violationData);
            setDetailVisible(true);
        } catch (err) {
            console.error('Error fetching violation detail:', err);
            notification.showError('Lỗi', 'Không thể tải chi tiết vi phạm');
        }
    };

    // Handle appeal processing
    const handleAppealProcess = async (violationId, result) => {
        try {
            setAppealProcessing(true);

            const response = await authApi.post(`/violations/${violationId}/process-appeal`, {
                result: result // 'approved' or 'rejected'
            });

            if (response.data.success) {
                // Update the violation in the list
                setViolations(prev => prev.map(v =>
                    v._id === violationId
                        ? { ...v, status: result === 'approved' ? 'appeal_approved' : 'appeal_rejected' }
                        : v
                ));

                // Update selected violation if it's the same one
                if (selectedViolation && selectedViolation._id === violationId) {
                    setSelectedViolation(prev => ({
                        ...prev,
                        status: result === 'approved' ? 'appeal_approved' : 'appeal_rejected'
                    }));
                }

                notification.showSuccess('Thành công', result === 'approved' ? 'Đã chấp nhận khiếu nại' : 'Đã từ chối khiếu nại');
            }
        } catch (error) {
            console.error('Error processing appeal:', error);
            notification.showError('Lỗi', 'Có lỗi xảy ra khi xử lý khiếu nại');
        } finally {
            setAppealProcessing(false);
        }
    };

    // Load more violations
    const loadMore = () => {
        if (hasMore && !loading) {
            fetchViolations(page + 1, true);
        }
    };

    // Handle filter change
    const handleFilterChange = (field, value) => {
        setFilters(prev => ({ ...prev, [field]: value }));
        setPage(1);
    };

    // Transform data for SmartSelect
    const classOptions = [
        { value: '', name: 'Tất cả lớp' },
        ...classes.map(cls => ({
            value: cls._id || cls.id,
            name: cls.name
        }))
    ];

    const violationTypeOptions = [
        { value: '', name: 'Tất cả loại vi phạm' },
        ...violationTypes.map(type => ({
            value: type.code,
            name: type.label
        }))
    ];

    const statusOptions = [
        { value: '', name: 'Tất cả trạng thái' },
        { value: 'pending', name: 'Chờ xử lý' },
        { value: 'processed', name: 'Đã xử lý' },
        { value: 'appealed', name: 'Đã khiếu nại' },
        { value: 'appeal_approved', name: 'Khiếu nại được chấp nhận' },
        { value: 'appeal_rejected', name: 'Khiếu nại bị từ chối' }
    ];

    const schoolYearOptions = [
        { value: '', name: 'Tất cả năm học' },
        { value: '2024-2025', name: '2024-2025' },
        { value: '2023-2024', name: '2023-2024' },
        { value: '2022-2023', name: '2022-2023' }
    ];

    if (loading && violations.length === 0) {
        return (
            <Box className="container" style={{ minHeight: '100vh', display: 'flex', flexDirection: 'column', paddingBottom: '60px', backgroundColor: '#f5f5f5' }}>
                <HeaderEdu 
                    title="Danh sách vi phạm"
                    showBackButton={true}
                    onBackClick={() => navigate('/teacher')}
                />
                <HeaderSpacer />
                <Box style={{ flex: 1, display: 'flex', justifyContent: 'center', alignItems: 'center' }}>
                    <LoadingIndicator />
                </Box>
            </Box>
        );
    }

    return (
        <Box className="container" style={{ minHeight: '100vh', display: 'flex', flexDirection: 'column', paddingBottom: '60px', backgroundColor: '#f5f5f5' }}>
            <HeaderEdu 
                title="Danh sách vi phạm"
                showBackButton={true}
                onBackClick={() => navigate('/teacher')}
            />
            <HeaderSpacer />

            <Box style={{ flex: 1, padding: '15px' }}>
                {/* Filter Section */}
                <Box style={{
                    backgroundColor: 'white',
                    borderRadius: '12px',
                    padding: '15px',
                    marginBottom: '15px',
                    boxShadow: '0 2px 8px rgba(0,0,0,0.1)'
                }}>
                    <Text bold style={{ fontSize: '16px', marginBottom: '15px', color: '#333' }}>
                        {ICONS.FILTER} Bộ lọc
                    </Text>
                    
                    <Box style={{ display: 'grid', gridTemplateColumns: '1fr', gap: '12px' }}>
                        <SmartSelect
                            placeholder="Tất cả lớp"
                            value={filters.classId}
                            onChange={(value) => handleFilterChange('classId', value)}
                            options={classOptions}
                            noDataMessage="Không có lớp học nào"
                            style={{ marginBottom: '0' }}
                        />

                        <SmartSelect
                            placeholder="Tất cả loại vi phạm"
                            value={filters.violationType}
                            onChange={(value) => handleFilterChange('violationType', value)}
                            options={violationTypeOptions}
                            noDataMessage="Không có loại vi phạm nào"
                            style={{ marginBottom: '0' }}
                        />

                        <SmartSelect
                            placeholder="Tất cả trạng thái"
                            value={filters.status}
                            onChange={(value) => handleFilterChange('status', value)}
                            options={statusOptions}
                            noDataMessage="Không có trạng thái nào"
                            style={{ marginBottom: '0' }}
                        />

                        <SmartSelect
                            placeholder="Chọn năm học"
                            value={filters.schoolYear}
                            onChange={(value) => handleFilterChange('schoolYear', value)}
                            options={schoolYearOptions}
                            noDataMessage="Không có năm học nào"
                            style={{ marginBottom: '0' }}
                        />

                        {/* Date Range Filter */}
                        <Box style={{ display: 'grid', gridTemplateColumns: '1fr 1fr', gap: '10px' }}>
                            <Box>
                                <Text style={{ fontSize: '14px', marginBottom: '5px', color: '#666' }}>Từ ngày:</Text>
                                <Input
                                    type="date"
                                    value={filters.startDate}
                                    onChange={(e) => handleFilterChange('startDate', e.target.value)}
                                />
                            </Box>
                            <Box>
                                <Text style={{ fontSize: '14px', marginBottom: '5px', color: '#666' }}>Đến ngày:</Text>
                                <Input
                                    type="date"
                                    value={filters.endDate}
                                    onChange={(e) => handleFilterChange('endDate', e.target.value)}
                                />
                            </Box>
                        </Box>

                        {/* Clear Filters Button */}
                        <Button
                            size="small"
                            variant="secondary"
                            onClick={() => setFilters({
                                classId: '',
                                violationType: '',
                                status: '',
                                schoolYear: '',
                                startDate: '',
                                endDate: ''
                            })}
                            style={{ marginTop: '10px' }}
                        >
                            Xóa tất cả bộ lọc
                        </Button>
                    </Box>
                </Box>

                {/* Violations List */}
                <Box style={{ backgroundColor: 'white', borderRadius: '12px', padding: '15px' }}>
                    <Box style={{ display: 'flex', justifyContent: 'space-between', alignItems: 'center', marginBottom: '15px' }}>
                        <Text bold style={{ fontSize: '18px', color: '#333' }}>
                            {ICONS.REPORT} Danh sách vi phạm
                        </Text>
                        <Button
                            onClick={() => navigate('/create-violation')}
                            style={{
                                borderRadius: '28px',
                                backgroundColor: '#dc3545',
                                color: 'white',
                                display: 'flex',
                                alignItems: 'center',
                                justifyContent: 'center',
                                gap: '4px',
                                padding: '10px',
                                border: 'none',
                                cursor: 'pointer',
                                transition: 'transform 0.2s ease',
                            }}
                        >
                            <Text style={{
                                display: 'flex',
                                alignItems: 'center',
                                lineHeight: 1,
                                fontSize: '20px'
                            }}>
                                +
                            </Text>
                        </Button>
                    </Box>

                    {loading ? (
                        <LoadingIndicator />
                    ) : violations.length > 0 ? (
                        <Box>
                            {Array.isArray(violations) && violations.map((violation, index) => {
                                const statusInfo = getStatusInfo(violation.status);
                                return (
                                    <Box
                                        key={violation._id || index}
                                        style={{
                                            padding: '15px',
                                            border: '1px solid #e0e0e0',
                                            borderRadius: '8px',
                                            marginBottom: '10px',
                                            backgroundColor: '#fff',
                                            cursor: 'pointer',
                                            transition: 'transform 0.2s ease, box-shadow 0.2s ease',
                                        }}
                                        onClick={() => handleViolationDetail(violation)}
                                        onMouseEnter={(e) => {
                                            e.currentTarget.style.transform = 'scale(1.02)';
                                            e.currentTarget.style.boxShadow = '0 4px 12px rgba(0,0,0,0.15)';
                                        }}
                                        onMouseLeave={(e) => {
                                            e.currentTarget.style.transform = 'scale(1)';
                                            e.currentTarget.style.boxShadow = 'none';
                                        }}
                                    >
                                        <Box style={{ display: 'flex', justifyContent: 'space-between', alignItems: 'flex-start', marginBottom: '10px' }}>
                                            <Text bold style={{ fontSize: '16px', color: '#333' }}>
                                                {violation.student?.name} ({violation.student?.studentId})
                                            </Text>
                                            <Box style={{
                                                padding: '4px 8px',
                                                borderRadius: '12px',
                                                backgroundColor: statusInfo.bg,
                                                border: `1px solid ${statusInfo.color}`,
                                                marginLeft: '10px'
                                            }}>
                                                <Text style={{ fontSize: '11px', color: statusInfo.color, fontWeight: 'bold' }}>
                                                    {statusInfo.label}
                                                </Text>
                                            </Box>
                                        </Box>
                                        
                                        <Text style={{ fontSize: '14px', color: '#0068ff', marginBottom: '8px', fontWeight: '500' }}>
                                            {ICONS.WARNING} {getViolationTypeLabel(violation.violationType)}
                                        </Text>
                                        
                                        <Text style={{ fontSize: '14px', color: '#666', marginBottom: '10px', lineHeight: '1.4' }}>
                                            {violation.description}
                                        </Text>
                                        
                                        <Box style={{ display: 'flex', justifyContent: 'space-between', alignItems: 'center', paddingTop: '8px', borderTop: '1px solid #f0f0f0' }}>
                                            <Text style={{ fontSize: '12px', color: '#999' }}>
                                                {ICONS.SCHOOL} {violation.class?.name} • {formatDistanceToNow(new Date(violation.violationDate), { 
                                                    addSuffix: true, 
                                                    locale: vi 
                                                })}
                                            </Text>
                                            <Text style={{ fontSize: '14px', fontWeight: 'bold', color: '#dc3545' }}>
                                                -{violation.pointsDeducted} điểm
                                            </Text>
                                        </Box>
                                    </Box>
                                );
                            })}

                            {hasMore && (
                                <Box style={{ display: 'flex', justifyContent: 'center', marginTop: '20px' }}>
                                    <Button 
                                        onClick={loadMore}
                                        loading={loading}
                                        style={{ 
                                            backgroundColor: '#0068ff', 
                                            color: 'white',
                                            borderRadius: '8px',
                                            padding: '12px 24px'
                                        }}
                                    >
                                        {ICONS.REFRESH} Tải thêm
                                    </Button>
                                </Box>
                            )}
                        </Box>
                    ) : (
                        <Box style={{ textAlign: 'center', padding: '40px 20px' }}>
                            <Text style={{ fontSize: '48px', marginBottom: '15px' }}>{ICONS.INFO}</Text>
                            <Text bold style={{ fontSize: '18px', marginBottom: '10px', color: '#333' }}>
                                Không có vi phạm nào
                            </Text>
                            <Text style={{ color: '#666', fontSize: '14px' }}>
                                Chưa có vi phạm nào phù hợp với bộ lọc đã chọn.
                            </Text>
                        </Box>
                    )}
                </Box>
            </Box>

            {/* Violation Detail Modal */}
            <Modal
                visible={detailVisible}
                title="Chi tiết vi phạm"
                onClose={() => {
                    setDetailVisible(false);
                    setSelectedViolation(null);
                }}
            >
                {selectedViolation && selectedViolation._id && (
                    <Box style={{ padding: '20px' }}>
                        <Box style={{ marginBottom: '20px' }}>
                            <Text bold style={{ fontSize: '18px', color: '#0068ff', marginBottom: '10px' }}>
                                {selectedViolation.student?.name} ({selectedViolation.student?.studentId})
                            </Text>
                            
                            <Text bold style={{ fontSize: '16px', color: '#dc3545', marginBottom: '10px' }}>
                                {getViolationTypeLabel(selectedViolation.violationType)}
                            </Text>
                            
                            <Box style={{ marginBottom: '15px' }}>
                                <Text bold style={{ fontSize: '14px', marginBottom: '5px' }}>Mô tả:</Text>
                                <Text style={{ fontSize: '14px', color: '#333' }}>
                                    {selectedViolation.description}
                                </Text>
                            </Box>

                            <Box style={{ display: 'grid', gridTemplateColumns: '1fr 1fr', gap: '15px', marginBottom: '15px' }}>
                                <Box>
                                    <Text bold style={{ fontSize: '12px', color: '#666', marginBottom: '5px' }}>
                                        Lớp học:
                                    </Text>
                                    <Text style={{ fontSize: '14px' }}>
                                        {selectedViolation.class?.name}
                                    </Text>
                                </Box>
                                <Box>
                                    <Text bold style={{ fontSize: '12px', color: '#666', marginBottom: '5px' }}>
                                        Ngày vi phạm:
                                    </Text>
                                    <Text style={{ fontSize: '14px' }}>
                                        {selectedViolation.violationDate ? 
                                            formatDateTime(selectedViolation.violationDate) : 
                                            'Không có thông tin'
                                        }
                                    </Text>
                                </Box>
                                <Box>
                                    <Text bold style={{ fontSize: '12px', color: '#666', marginBottom: '5px' }}>
                                        Điểm trừ:
                                    </Text>
                                    <Text style={{ fontSize: '14px', color: '#dc3545', fontWeight: 'bold' }}>
                                        -{selectedViolation.pointsDeducted} điểm
                                    </Text>
                                </Box>
                                <Box>
                                    <Text bold style={{ fontSize: '12px', color: '#666', marginBottom: '5px' }}>
                                        Địa điểm:
                                    </Text>
                                    <Text style={{ fontSize: '14px' }}>
                                        {selectedViolation.location || 'Không có'}
                                    </Text>
                                </Box>
                            </Box>

                            {selectedViolation.appealReason && (
                                <Box style={{ marginBottom: '15px', padding: '12px', backgroundColor: '#f8f9fa', borderRadius: '8px' }}>
                                    <Text bold style={{ fontSize: '12px', color: '#666', marginBottom: '5px' }}>
                                        Lý do khiếu nại:
                                    </Text>
                                    <Text style={{ fontSize: '14px' }}>
                                        {selectedViolation.appealReason}
                                    </Text>
                                </Box>
                            )}

                            {/* Appeal Processing Buttons */}
                            {selectedViolation.status === 'appealed' && (
                                <Box style={{
                                    marginTop: '20px',
                                    padding: '15px',
                                    backgroundColor: '#fff3cd',
                                    borderRadius: '8px',
                                    border: '1px solid #ffeaa7'
                                }}>
                                    <Text bold style={{ fontSize: '14px', marginBottom: '15px', color: '#856404' }}>
                                        {ICONS.WARNING} Xử lý khiếu nại
                                    </Text>
                                    <Box style={{ display: 'flex', gap: '10px' }}>
                                        <Button
                                            onClick={() => handleAppealProcess(selectedViolation._id, 'approved')}
                                            loading={appealProcessing}
                                            style={{
                                                flex: 1,
                                                backgroundColor: '#28a745',
                                                color: 'white',
                                                border: 'none',
                                                borderRadius: '6px',
                                                padding: '12px',
                                                fontSize: '14px',
                                                fontWeight: 'bold'
                                            }}
                                        >
                                            {ICONS.CHECK} Chấp nhận
                                        </Button>
                                        <Button
                                            onClick={() => handleAppealProcess(selectedViolation._id, 'rejected')}
                                            loading={appealProcessing}
                                            style={{
                                                flex: 1,
                                                backgroundColor: '#dc3545',
                                                color: 'white',
                                                border: 'none',
                                                borderRadius: '6px',
                                                padding: '12px',
                                                fontSize: '14px',
                                                fontWeight: 'bold'
                                            }}
                                        >
                                            {ICONS.CLOSE} Từ chối
                                        </Button>
                                    </Box>
                                </Box>
                            )}
                        </Box>
                    </Box>
                )}
            </Modal>

            <BottomNavigationEdu />
        </Box>
    );
};

export default ViolationsList;

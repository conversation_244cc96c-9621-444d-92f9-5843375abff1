import React, { useRef, useCallback } from 'react';
import { Box, Text } from 'zmp-ui';
import { useNavigate } from 'react-router-dom';
import { authApi } from '../utils/api';
// Không cần import LoadingIndicator
import { formatDistanceToNow } from 'date-fns';
import vi from 'date-fns/locale/vi';
import useApiCache from '../hooks/useApiCache';

const NewsComponent = () => {
    const navigate = useNavigate();
    const newsContainerRef = useRef(null);

    // API function for news
    const fetchNews = useCallback(async () => {
        try {
            const response = await authApi.get('/news?page=1&limit=5');
            return response.data.news || [];
        } catch (error) {
            console.error('Error fetching news:', error);
            return []; // Luôn trả về array rỗng khi có lỗi
        }
    }, []);

    // Use useApiCache for news
    const {
        data: newsRaw = [],
        loading
    } = useApiCache(fetchNews, [], {
        cacheKey: 'news_component',
        enabled: true,
        cacheTime: 10 * 60 * 1000, // Cache 10 phút (tin tức ít thay đổi)
    });

    // Ensure news is always an array
    const news = Array.isArray(newsRaw) ? newsRaw : [];

    // Format date
    const formatNewsDate = (dateString) => {
        try {
            return formatDistanceToNow(new Date(dateString), { locale: vi, addSuffix: true });
        } catch (error) {
            return 'Không xác định';
        }
    };

    return (
        <Box className="section" style={{ marginTop: '15px', backgroundColor: 'white', padding: '15px' }}>
            <Box className="section-header" flex justifyContent="space-between" alignItems="center" style={{ marginBottom: '15px' }}>
                <Text className="section-title" bold size="large">
                    Tin tức
                </Text>
                <Text
                    className="see-all"
                    style={{ color: '#0068ff', fontSize: '14px', cursor: 'pointer' }}
                    onClick={() => navigate('/news', {
                        state: {
                            previousPath: '/student',
                            previousState: { tab: 'student' }
                        }
                    })}
                >
                    Xem tất cả
                </Text>
            </Box>

            <Box style={{ position: 'relative' }}>
                <Box
                    className="news-scroll"
                    ref={newsContainerRef}
                    style={{
                        display: 'flex',
                        overflowX: 'auto',
                        gap: '12px',
                        paddingBottom: '10px',
                        scrollBehavior: 'smooth',
                        WebkitOverflowScrolling: 'touch'
                    }}
                >
                    {news.length > 0 ? (
                        news.map((item) => (
                            <Box
                                key={item._id}
                                className="news-card"
                                style={{
                                    minWidth: '280px',
                                    borderRadius: '8px',
                                    overflow: 'hidden',
                                    boxShadow: '0 2px 6px rgba(0, 0, 0, 0.1)',
                                    flexShrink: 0,
                                    display: 'flex',
                                    backgroundColor: '#f9f9f9',
                                    height: '100px' // Chiều cao cố định
                                }}
                                onClick={() => navigate(`/news/${item._id}`, {
                                    state: {
                                        previousPath: '/student',
                                        previousState: { tab: 'student' }
                                    }
                                })}
                            >
                                <Box
                                    className="news-thumbnail"
                                    style={{
                                        width: '100px',
                                        height: '100%',
                                        overflow: 'hidden',
                                        flexShrink: 0
                                    }}
                                >
                                    <img
                                        src={item.image || 'https://via.placeholder.com/100x100?text=News'}
                                        alt={item.title}
                                        style={{
                                            width: '100%',
                                            height: '100%',
                                            objectFit: 'cover'
                                        }}
                                    />
                                </Box>
                                <Box
                                    className="news-content"
                                    style={{
                                        padding: '10px',
                                        flexGrow: 1,
                                        display: 'flex',
                                        flexDirection: 'column',
                                        justifyContent: 'space-between',
                                        height: '100%',
                                        width: '180px'
                                    }}
                                >
                                    <Text
                                        bold
                                        style={{
                                            fontSize: '14px',
                                            lineHeight: 1.3,
                                            display: '-webkit-box',
                                            WebkitLineClamp: 2,
                                            WebkitBoxOrient: 'vertical',
                                            overflow: 'hidden',
                                            textOverflow: 'ellipsis',
                                            maxHeight: '36px',
                                            wordBreak: 'break-word',
                                            whiteSpace: 'normal'
                                        }}
                                    >
                                        {item.title}
                                    </Text>
                                    <Box style={{ display: 'flex', justifyContent: 'space-between', alignItems: 'center', marginTop: 'auto' }}>
                                        <Text style={{ fontSize: '12px', color: '#888' }}>
                                            {formatNewsDate(item.createdAt)}
                                        </Text>
                                        <Box style={{ display: 'flex', alignItems: 'center', gap: '4px' }}>
                                            <Text style={{ fontSize: '12px' }}>👁️</Text>
                                            <Text style={{ fontSize: '12px', color: '#888' }}>
                                                {item.views || 0}
                                            </Text>
                                        </Box>
                                    </Box>
                                </Box>
                            </Box>
                        ))
                    ) : (
                        <Text>Chưa có tin tức</Text>
                    )}
                </Box>

                {/* Nút mũi tên scroll */}
                {news.length > 2 && (
                    <Box
                        style={{
                            position: 'absolute',
                            right: 0,
                            top: 'calc(50% - 18px)',
                            backgroundColor: 'rgba(255, 255, 255, 0.9)',
                            borderRadius: '50%',
                            width: '36px',
                            height: '36px',
                            display: 'flex',
                            alignItems: 'center',
                            justifyContent: 'center',
                            boxShadow: '0 2px 4px rgba(0, 0, 0, 0.1)',
                            cursor: 'pointer',
                            zIndex: 1
                        }}
                        onClick={() => {
                            if (newsContainerRef.current) {
                                newsContainerRef.current.scrollLeft += 300;
                            }
                        }}
                    >
                        <Text style={{ fontSize: '20px' }}>→</Text>
                    </Box>
                )}
            </Box>
        </Box>
    );
};

export default NewsComponent;

import React, { useState, useEffect, useContext } from 'react';
import { Box, Text, useNavigate, Button, Input } from 'zmp-ui';

import HeaderEdu from '../../components/HeaderEdu';
import HeaderSpacer from '../../components/utils/HeaderSpacer';
import BottomNavigationEdu from '../../components/BottomNavigationEdu';
import SmartSelect from '../../components/utils/SmartSelect';
import { AuthContext } from '../../context/AuthContext';
import { authApi } from '../../utils/api';
import { ICONS } from '../../constants/icons';
import LoadingIndicator from '../../components/utils/LoadingIndicator';
import useNotification from '../../hooks/useNotification';
import { useSchoolYear } from '../../context/SchoolYearContext';

const CreateViolation = () => {
    const navigate = useNavigate();
    const { user } = useContext(AuthContext);
    const { success, error } = useNotification();
    const {schoolYear, selectedSchoolYear} = useSchoolYear();
    const [loading, setLoading] = useState(false);
    const [classes, setClasses] = useState([]);
    const [students, setStudents] = useState([]);
    const [violationTypes, setViolationTypes] = useState([]);
    const [formData, setFormData] = useState({
        classId: '',
        studentId: '',
        violationType: '',
        description: '',
        violationDate: new Date().toISOString().split('T')[0],
        location: ''
    });
    const [errors, setErrors] = useState({});
    const [submitting, setSubmitting] = useState(false);

    // Fetch initial data
    useEffect(() => {
        fetchInitialData();
    }, []);

    const fetchInitialData = async () => {
        try {
            setLoading(true);
            
            // Fetch classes and violation config
            const [classesRes, configRes] = await Promise.all([
                authApi.get('/directory/teacher/classes'),
                authApi.get('/violations/config')
            ]);
            
            const classesData = Array.isArray(classesRes.data.data) ? classesRes.data.data : [];
            const violationTypesData = Array.isArray(configRes.data?.data?.violationTypes) ? configRes.data.data.violationTypes : [];
            
            setClasses(classesData);
            setViolationTypes(violationTypesData);
            
        } catch (error) {
            console.error('Error fetching initial data:', error);
            setClasses([]);
            setViolationTypes([]);
        } finally {
            setLoading(false);
        }
    };

    // Fetch students when class changes
    useEffect(() => {
        if (formData.classId) {
            fetchStudents(formData.classId);
        } else {
            setStudents([]);
            setFormData(prev => ({ ...prev, studentId: '' }));
        }
    }, [formData.classId]);

    const fetchStudents = async (classId) => {
        try {
            const response = await authApi.get(`/directory/class/${classId}`);
            const studentsData = Array.isArray(response.data.data?.students) ? response.data.data.students : [];
            setStudents(studentsData);
        } catch (error) {
            console.error('Error fetching students:', error);
            setStudents([]);
        }
    };

    // Handle form input changes
    const handleInputChange = (field, value) => {
        setFormData(prev => ({ ...prev, [field]: value }));
        if (errors[field]) {
            setErrors(prev => ({ ...prev, [field]: '' }));
        }
    };

    // Validate form
    const validateForm = () => {
        const newErrors = {};

        if (!formData.classId) newErrors.classId = 'Vui lòng chọn lớp học';
        if (!formData.studentId) newErrors.studentId = 'Vui lòng chọn học sinh';
        if (!formData.violationType) newErrors.violationType = 'Vui lòng chọn loại vi phạm';
        if (!formData.description.trim()) newErrors.description = 'Vui lòng nhập mô tả vi phạm';
        if (formData.description.trim().length < 5) newErrors.description = 'Mô tả phải có ít nhất 5 ký tự';
        if (!formData.violationDate) newErrors.violationDate = 'Vui lòng chọn ngày vi phạm';

        setErrors(newErrors);
        return Object.keys(newErrors).length === 0;
    };

    // Handle form submission
    const handleSubmit = async () => {
        if (!validateForm()) return;

        try {
            setSubmitting(true);
            
            const submitData = {
                ...formData,
                schoolYear: selectedSchoolYear || '2024-2025'
            };

            await authApi.post('/violations', submitData);
            
            success('Tạo báo cáo vi phạm thành công');
            navigate('/teacher');
        } catch (err) {
            console.error('Error creating violation:', err);
            error('Có lỗi xảy ra khi tạo báo cáo vi phạm');
        } finally {
            setSubmitting(false);
        }
    };

    // Transform data for SmartSelect
    const classOptions = classes.map(cls => ({
        value: cls.id,
        name: cls.name,
        classRoom: cls.classRoom
    }));

    const studentOptions = students.map(student => ({
        value: student._id || student.id,
        name: student.name,
        studentId: student.studentId
    }));

    const violationTypeOptions = violationTypes.map(type => ({
        value: type.code,
        name: type.label,
        label: type.label,
        points: type.points
    }));

    if (loading) {
        return (
            <Box className="container" style={{ minHeight: '100vh', display: 'flex', flexDirection: 'column', paddingBottom: '60px', backgroundColor: '#f5f5f5' }}>
                <HeaderEdu 
                    title="Tạo báo cáo vi phạm"
                    showBackButton={true}
                    onBackClick={() => navigate('/teacher')}
                />
                <HeaderSpacer />
                <Box style={{ flex: 1, display: 'flex', justifyContent: 'center', alignItems: 'center' }}>
                    <LoadingIndicator />
                </Box>
            </Box>
        );
    }

    return (
        <Box className="container" style={{ minHeight: '100vh', display: 'flex', flexDirection: 'column', paddingBottom: '60px', backgroundColor: '#f5f5f5' }}>
            <HeaderEdu 
                title="Tạo báo cáo vi phạm"
                showBackButton={true}
                onBackClick={() => navigate('/teacher')}
            />
            <HeaderSpacer />

            <Box style={{ flex: 1, padding: '15px' }}>
                <Box style={{
                    backgroundColor: 'white',
                    borderRadius: '12px',
                    padding: '20px',
                    boxShadow: '0 2px 8px rgba(0,0,0,0.1)'
                }}>
                    <Text bold style={{ fontSize: '18px', marginBottom: '20px', color: '#0068ff', textAlign: 'center' }}>
                        {ICONS.REPORT} Báo cáo vi phạm học sinh
                    </Text>

                    {/* Class Selection */}
                    <SmartSelect
                        label="Lớp học"
                        placeholder="Chọn lớp học"
                        value={formData.classId}
                        onChange={(value) => handleInputChange('classId', value)}
                        options={classOptions}
                        required={true}
                        error={errors.classId}
                        renderOption={(option) => `${option.name} - ${option.classRoom || ''}`}
                        searchKey="name"
                        noDataMessage="Không có lớp học nào"
                    />

                    {/* Student Selection */}
                    <SmartSelect
                        label="Học sinh"
                        placeholder={students.length === 0 ? "Không có học sinh" : "Chọn học sinh"}
                        value={formData.studentId}
                        onChange={(value) => handleInputChange('studentId', value)}
                        options={studentOptions}
                        disabled={!formData.classId || students.length === 0}
                        required={true}
                        error={errors.studentId}
                        renderOption={(option) => `${option.name} (${option.studentId || ''})`}
                        searchKey="name"
                        noDataMessage="Lớp học này chưa có học sinh nào"
                    />

                    {/* Violation Type Selection */}
                    <SmartSelect
                        label="Loại vi phạm"
                        placeholder="Chọn loại vi phạm"
                        value={formData.violationType}
                        onChange={(value) => handleInputChange('violationType', value)}
                        options={violationTypeOptions}
                        required={true}
                        error={errors.violationType}
                        renderOption={(option) => `${option.label} (-${option.points} điểm)`}
                        searchKey="label"
                        noDataMessage="Không có loại vi phạm nào"
                    />

                    {/* Description */}
                    <Box style={{ marginBottom: '20px' }}>
                        <Text bold style={{ fontSize: '14px', marginBottom: '8px', color: '#333' }}>
                            Mô tả chi tiết <Text style={{ color: '#dc3545' }}>*</Text>
                        </Text>
                        <Input.TextArea
                            placeholder="Mô tả chi tiết về vi phạm (tối thiểu 5 ký tự)..."
                            value={formData.description}
                            onChange={(e) => handleInputChange('description', e.target.value)}
                            rows={4}
                        />
                        <Text style={{ 
                            fontSize: '12px', 
                            color: formData.description.length < 5 ? '#dc3545' : '#28a745',
                            marginTop: '5px'
                        }}>
                            {formData.description.length}/5 ký tự tối thiểu
                        </Text>
                        {errors.description && (
                            <Text style={{ color: '#dc3545', fontSize: '12px', marginTop: '5px' }}>
                                {errors.description}
                            </Text>
                        )}
                    </Box>

                    {/* Violation Date */}
                    <Box style={{ marginBottom: '20px' }}>
                        <Text bold style={{ fontSize: '14px', marginBottom: '8px', color: '#333' }}>
                            Ngày vi phạm <Text style={{ color: '#dc3545' }}>*</Text>
                        </Text>
                        <Input
                            type="date"
                            value={formData.violationDate}
                            onChange={(e) => handleInputChange('violationDate', e.target.value)}
                            max={new Date().toISOString().split('T')[0]}
                        />
                        {errors.violationDate && (
                            <Text style={{ color: '#dc3545', fontSize: '12px', marginTop: '5px' }}>
                                {errors.violationDate}
                            </Text>
                        )}
                    </Box>

                    {/* Location (Optional) */}
                    <Box style={{ marginBottom: '30px' }}>
                        <Text bold style={{ fontSize: '14px', marginBottom: '8px', color: '#333' }}>
                            Địa điểm vi phạm
                        </Text>
                        <Input
                            placeholder="Ví dụ: Lớp học, sân trường, hành lang..."
                            value={formData.location}
                            onChange={(e) => handleInputChange('location', e.target.value)}
                        />
                    </Box>

                    {/* Submit Button */}
                    <Button
                        fullWidth
                        onClick={handleSubmit}
                        loading={submitting}
                        disabled={submitting}
                        style={{
                            backgroundColor: '#dc3545',
                            color: 'white',
                            height: '48px',
                            fontSize: '16px',
                            fontWeight: 'bold'
                        }}
                    >
                        {submitting ? 'Đang tạo báo cáo...' : `${ICONS.REPORT} Tạo báo cáo vi phạm`}
                    </Button>
                </Box>

                {/* Info Card */}
                <Box style={{
                    backgroundColor: '#fff3cd',
                    borderRadius: '12px',
                    padding: '15px',
                    marginTop: '20px',
                    border: '1px solid #ffeaa7'
                }}>
                    <Text style={{ fontSize: '14px', color: '#856404', textAlign: 'center' }}>
                        {ICONS.TIP} <Text bold>Lưu ý:</Text> Báo cáo vi phạm sẽ được gửi thông báo đến phụ huynh và ảnh hưởng đến điểm thi đua của học sinh.
                    </Text>
                </Box>
            </Box>

            <BottomNavigationEdu />
        </Box>
    );
};

export default CreateViolation;

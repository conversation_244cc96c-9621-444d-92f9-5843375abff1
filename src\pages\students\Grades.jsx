import React, { useEffect, useState, useContext } from 'react';
import { Box, Text, Button, useNavigate } from 'zmp-ui';
import HeaderEdu from '../../components/HeaderEdu';
import BottomNavigationEdu from '../../components/BottomNavigationEdu';
import { AuthContext } from '../../context/AuthContext';
import { authApi } from '../../utils/api';
import { format } from 'date-fns';
import vi from 'date-fns/locale/vi';
import Loading from '@/components/utils/Loading';
import { getSubjectIcon, getSubjectIconClass } from '../../utils/subjectUtils';
import HeaderSpacer from '../../components/utils/HeaderSpacer';
import { ICONS } from '@/constants/icons';
import { useSchoolYear } from '../../context/SchoolYearContext';

const Grades = () => {
    const navigate = useNavigate();
    const { user, classId, loading: authLoading } = useContext(AuthContext);
    const [loading, setLoading] = useState(true);
    const [semester, setSemester] = useState(1); // 1: Kỳ I, 2: Kỳ II, 3: Cả năm
    const { selectedSchoolYear } = useSchoolYear(); // Lấy selectedSchoolYear từ context
    const [subjectFilter, setSubjectFilter] = useState(''); // subjectId để lọc
    const [grades, setGrades] = useState([]); // Điểm chi tiết
    const [averageGrades, setAverageGrades] = useState([]); // Điểm trung bình môn
    const [summary, setSummary] = useState(null); // Tổng kết học kỳ
    const [expandedSubject, setExpandedSubject] = useState(null); // Môn đang mở
    const [error, setError] = useState(null);
    const [filterLoading, setFilterLoading] = useState(false); // Loading riêng cho bộ lọc

    // Kiểm tra đăng nhập
    useEffect(() => {
        if (!authLoading && !user) {
            navigate('/login', { replace: true });
        } else if (user && !user.role.includes('student')) {
            localStorage.removeItem('token');
            localStorage.removeItem('user');
            navigate('/login', { replace: true });
        }
    }, [user, authLoading, navigate]);

    // Lấy dữ liệu điểm
    useEffect(() => {
        if (user?._id && classId) {
            setLoading(true);
            setError(null);

            // Lấy điểm chi tiết
            authApi
                .get(`/grades/student/${user._id}?subjectId=${subjectFilter}&semester=${semester}`)
                .then((response) => {
                    console.log('Grades API response:', response.data);
                    setGrades(response.data.data || []);
                })
                .catch((err) => {
                    console.error('Error fetching grades:', err);
                    setError('Lỗi khi lấy điểm chi tiết');
                });

            // Lấy điểm trung bình
            authApi
                .get(`/grades/average/student/${user._id}?semester=${semester}`)
                .then((response) => {
                    console.log('Average Grades API response:', response.data);
                    setAverageGrades(response.data.data || []);
                })
                .catch((err) => {
                    console.error('Error fetching average grades:', err);
                    setError('Lỗi khi lấy điểm trung bình');
                });

            // Lấy tổng kết học kỳ
            authApi
                .get(`/grades/summary/student/${user._id}?semester=${semester}`)
                .then((response) => {
                    console.log('Summary API response:', response.data);
                    setSummary(response.data.data[0] || null);
                })
                .catch((err) => {
                    console.error('Error fetching summary:', err);
                    setError('Lỗi khi lấy tổng kết học kỳ');
                })
                .finally(() => {
                    setLoading(false);
                });
        }
    }, [user, classId, semester, selectedSchoolYear, subjectFilter]);

    // Chuyển đổi học kỳ
    const handleSemesterChange = (newSemester) => {
        setSemester(newSemester);
        setExpandedSubject(null); // Đóng tất cả môn khi đổi kỳ
    };

    // Chuyển đổi năm học
    const handleSchoolYearChange = (newSchoolYear) => {
        setSchoolYear(newSchoolYear);
        setExpandedSubject(null);
    };

    // Lọc môn học
    const handleSubjectFilter = (subjectId) => {
        setSubjectFilter(subjectId);
        setExpandedSubject(null);
    };

    // Mở/đóng môn học
    const toggleSubject = (subjectId) => {
        setExpandedSubject(expandedSubject === subjectId ? null : subjectId);
    };

    // Định dạng ngày
    const formatDate = (dateString) => {
        try {
            return format(new Date(dateString), 'dd/MM/yyyy', { locale: vi });
        } catch {
            return 'N/A';
        }
    };

    // Định dạng loại điểm
    const formatGradeType = (type) => {
        const types = {
            oral: 'Miệng',
            fifteen_minutes: '15 phút',
            lesson: '1 tiết',
            midterm: 'Giữa kỳ',
            final: 'Cuối kỳ',
        };
        return types[type] || type;
    };

    // Định dạng xếp loại
    const formatClassification = (classification) => {
        const classes = {
            'Giỏi': 'status-excellent',
            'Khá': 'status-good',
            'Trung bình': 'status-average',
            'Yếu': 'status-poor',
        };
        return classes[classification] || 'status-average';
    };

    // Lấy màu điểm trung bình
    const getAverageColor = (average) => {
        if (average >= 8) return '';
        if (average >= 6.5) return 'warning';
        return 'danger';
    };

    return (
        <Box
            className="container"
            style={{
                minHeight: '100vh',
                display: 'flex',
                flexDirection: 'column',
                paddingBottom: '60px',
                backgroundColor: '#f5f5f5',
            }}
        >
            <HeaderEdu title="Điểm số" showBackButton={true} />
            <HeaderSpacer />

            {/* Semester Tabs */}
            <Box
                className="semester-tabs"
                style={{
                    display: 'flex',
                    backgroundColor: 'white',
                    marginBottom: '10px',
                    position: 'sticky',
                    top: 0,
                    zIndex: 90,
                    boxShadow: '0 2px 4px rgba(0, 0, 0, 0.1)',
                }}
            >
                {[
                    { id: 1, name: 'Học kỳ I' },
                    { id: 2, name: 'Học kỳ II' },
                    { id: 3, name: 'Cả năm' },
                ].map((tab) => (
                    <Text
                        key={tab.id}
                        className={`tab ${semester === tab.id ? 'active' : ''}`}
                        style={{
                            flex: 1,
                            textAlign: 'center',
                            padding: '12px 0',
                            fontWeight: 'bold',
                            color: semester === tab.id ? '#0068ff' : '#666',
                            cursor: 'pointer',
                            borderBottom: semester === tab.id ? '3px solid #0068ff' : '3px solid transparent',
                        }}
                        onClick={() => handleSemesterChange(tab.id)}
                    >
                        {tab.name}
                    </Text>
                ))}
            </Box>

            {/* School Year Selector */}
            <Box
                className="school-year"
                style={{
                    backgroundColor: 'white',
                    padding: '10px 15px',
                    display: 'flex',
                    justifyContent: 'space-between',
                    alignItems: 'center',
                    marginBottom: '10px',
                    borderRadius: '8px',
                    boxShadow: '0 2px 4px rgba(0, 0, 0, 0.1)',
                }}
            >
                <Text className="school-year-title" style={{ fontSize: '14px', color: '#666' }}>
                    Năm học: {selectedSchoolYear}
                </Text>
            </Box>

            {/* Loading State */}
            {loading ? (
                <Loading />
            ) : error ? (
                <Text style={{ color: '#ff3b30', textAlign: 'center', padding: '20px' }}>{error}</Text>
            ) : (
                <>
                    {/* Overall Grade */}
                    {summary && (
                        <Box
                            className="overall-grade"
                            style={{
                                backgroundColor: 'white',
                                padding: '15px',
                                marginBottom: '10px',
                                borderRadius: '8px',
                                boxShadow: '0 2px 4px rgba(0, 0, 0, 0.1)',
                            }}
                        >
                            <Box
                                className="grade-header"
                                style={{ display: 'flex', justifyContent: 'space-between', alignItems: 'center', marginBottom: '15px' }}
                            >
                                <Text className="grade-title" style={{ fontSize: '16px', fontWeight: 'bold' }}>
                                    Tổng kết điểm {semester === 3 ? 'Cả năm' : `Học kỳ ${semester}`}
                                </Text>
                                <Text className={`status ${formatClassification(summary.classification)}`} style={{ padding: '3px 8px', borderRadius: '12px', fontSize: '11px', fontWeight: 'bold' }}>
                                    {summary.classification}
                                </Text>
                            </Box>
                            <Box className="grade-summary" style={{ display: 'flex', gap: '15px' }}>
                                <Box
                                    className="grade-card highlight"
                                    style={{
                                        backgroundColor: '#0068ff',
                                        color: 'white',
                                        borderRadius: '8px',
                                        padding: '12px',
                                        flex: 1,
                                        textAlign: 'center',
                                    }}
                                >
                                    <Text className="grade-card-title" style={{ fontSize: '12px', marginBottom: '5px' }}>
                                        Điểm trung bình
                                    </Text>
                                    <Text className="grade-card-value" style={{ fontSize: '24px', fontWeight: 'bold' }}>
                                        {summary.gpa.toFixed(1)}
                                    </Text>
                                    <Text className="grade-card-subtitle" style={{ fontSize: '11px', opacity: 0.8 }}>
                                        Xếp loại {summary.classification}
                                    </Text>
                                </Box>
                                <Box
                                    className="grade-card"
                                    style={{
                                        backgroundColor: '#f0f6ff',
                                        borderRadius: '8px',
                                        padding: '12px',
                                        flex: 1,
                                        textAlign: 'center',
                                    }}
                                >
                                    <Text className="grade-card-title" style={{ fontSize: '12px', marginBottom: '5px' }}>
                                        Hạnh kiểm
                                    </Text>
                                    <Text className="grade-card-value" style={{ fontSize: '24px', fontWeight: 'bold' }}>
                                        {summary.conduct}
                                    </Text>
                                    <Text className="grade-card-subtitle" style={{ fontSize: '11px', opacity: 0.8 }}>
                                        Xếp loại {summary.conduct}
                                    </Text>
                                </Box>
                                <Box
                                    className="grade-card"
                                    style={{
                                        backgroundColor: '#f0f6ff',
                                        borderRadius: '8px',
                                        padding: '12px',
                                        flex: 1,
                                        textAlign: 'center',
                                    }}
                                >
                                    <Text className="grade-card-title" style={{ fontSize: '12px', marginBottom: '5px' }}>
                                        Xếp hạng
                                    </Text>
                                    <Text className="grade-card-value" style={{ fontSize: '24px', fontWeight: 'bold' }}>
                                        {summary.classRanking}
                                    </Text>
                                    <Text className="grade-card-subtitle" style={{ fontSize: '11px', opacity: 0.8 }}>
                                        Trong lớp
                                    </Text>
                                </Box>
                            </Box>
                        </Box>
                    )}

                    {/* Subject Filter */}
                    <Box
                        className="subject-filter"
                        style={{
                            backgroundColor: 'white',
                            padding: '12px 15px',
                            display: 'flex',
                            justifyContent: 'space-between',
                            alignItems: 'center',
                            marginBottom: '1px',
                            borderRadius: '8px 8px 0 0',
                            boxShadow: '0 2px 4px rgba(0, 0, 0, 0.1)',
                        }}
                    >
                        <Text className="filter-title" style={{ fontSize: '16px', fontWeight: 'bold' }}>
                            Điểm theo môn học
                        </Text>
                        <Box
                            className="filter-actions"
                            style={{
                                display: 'flex',
                                alignItems: 'center',
                                gap: '10px',
                                overflowX: 'auto',
                                whiteSpace: 'nowrap',
                                padding: '5px 0',
                                scrollbarWidth: 'none', // Ẩn thanh cuộn trên Firefox
                                '-ms-overflow-style': 'none', // Ẩn thanh cuộn trên IE
                            }}
                        >
                            <Button
                                className="filter-button"
                                style={{
                                    display: 'flex',
                                    alignItems: 'center',
                                    gap: '5px',
                                    color: subjectFilter === '' ? '#0068ff' : '#666',
                                    fontSize: '14px',
                                    padding: '5px 10px',
                                    borderRadius: '12px',
                                    backgroundColor: subjectFilter === '' ? '#e8f0fe' : 'transparent',
                                    border: subjectFilter === '' ? '1px solid #0068ff' : '1px solid #ddd',
                                    cursor: 'pointer',
                                    flexShrink: 0,
                                }}
                                onClick={() => handleSubjectFilter('')}
                                loading={filterLoading && subjectFilter === ''}
                            >
                                <Text>Tất cả</Text>
                            </Button>
                            {averageGrades
                                .filter((avg, index, self) =>
                                    avg && avg.subject && avg.subject._id &&
                                    self.findIndex(item => item && item.subject && item.subject._id === avg.subject._id) === index
                                )
                                .map((avg) => (
                                    <Button
                                        key={`filter-${avg.subject._id}`}
                                        className="filter-button"
                                        style={{
                                            display: 'flex',
                                            alignItems: 'center',
                                            gap: '5px',
                                            color: subjectFilter === avg.subject._id ? '#0068ff' : '#666',
                                            fontSize: '14px',
                                            padding: '5px 10px',
                                            borderRadius: '12px',
                                            backgroundColor: subjectFilter === avg.subject._id ? '#e8f0fe' : 'transparent',
                                            border: subjectFilter === avg.subject._id ? '1px solid #0068ff' : '1px solid #ddd',
                                            cursor: 'pointer',
                                            flexShrink: 0,
                                        }}
                                        onClick={() => handleSubjectFilter(avg.subject._id)}
                                        loading={filterLoading && subjectFilter === avg.subject._id}
                                    >
                                        <Text>{avg.subject.name}</Text>
                                    </Button>
                                ))}
                        </Box>
                    </Box>

                    {/* Grades List */}
                    <Box
                        className="grades-container"
                        style={{
                            backgroundColor: 'white',
                            borderRadius: '0 0 8px 8px',
                            overflow: 'hidden',
                            boxShadow: '0 2px 4px rgba(0, 0, 0, 0.1)',
                            marginBottom: '15px',
                        }}
                    >
                        {filterLoading ? (
                            <Loading />
                        ) : averageGrades.length > 0 ? (
                            averageGrades
                                .filter((avg) => avg && avg.subject && avg.subject._id && (!subjectFilter || avg.subject._id === subjectFilter))
                                .filter((avg, index, self) =>
                                    self.findIndex(item => item && item.subject && item.subject._id === avg.subject._id) === index
                                )
                                .map((avg, index) => {
                                    const subjectGrades = grades.filter((grade) => grade && grade.subject && grade.subject._id === avg.subject._id);
                                    return (
                                        <Box
                                            key={`subject-${avg.subject._id}-${index}`}
                                            className={`subject-section ${expandedSubject === avg.subject._id ? 'active' : ''}`}
                                            style={{ borderBottom: '1px solid #eee', padding: 0 }}
                                            onClick={() => toggleSubject(avg.subject._id)}
                                        >
                                            <Box
                                                className="subject-header"
                                                style={{
                                                    display: 'grid',
                                                    gridTemplateColumns: 'auto 60px 30px',
                                                    alignItems: 'center',
                                                    padding: '15px',
                                                    backgroundColor: '#f9f9f9',
                                                    cursor: 'pointer',
                                                    gap: '10px',
                                                }}
                                            >
                                                <Box className="subject-info" style={{ display: 'flex', alignItems: 'center', gap: '10px' }}>
                                                    <Box
                                                        className={`subject-icon ${getSubjectIconClass(avg.subject.name)}`}
                                                        style={{
                                                            width: '36px',
                                                            height: '36px',
                                                            borderRadius: '8px',
                                                            display: 'flex',
                                                            alignItems: 'center',
                                                            justifyContent: 'center',
                                                            fontSize: '18px',
                                                            color: 'white',
                                                        }}
                                                    >
                                                        {getSubjectIcon(avg.subject.name)}
                                                    </Box>
                                                    <Box>
                                                        <Text className="subject-name" style={{ fontWeight: 'bold' }}>
                                                            {avg.subject.name}
                                                        </Text>
                                                        <Text className="subject-code" style={{ fontSize: '11px', color: '#666' }}>
                                                            {avg.subject.code}
                                                        </Text>
                                                    </Box>
                                                </Box>
                                                <Text
                                                    className={`subject-avg ${getAverageColor(avg.average)}`}
                                                    style={{
                                                        fontSize: '18px',
                                                        fontWeight: 'bold',
                                                        textAlign: 'center',
                                                        justifySelf: 'center'
                                                    }}
                                                >
                                                    {avg.average.toFixed(1)}
                                                </Text>
                                                <Text
                                                    className="expanded-icon"
                                                    style={{
                                                        transition: 'transform 0.3s',
                                                        transform: expandedSubject === avg.subject._id ? 'rotate(180deg)' : 'rotate(0deg)',
                                                        justifySelf: 'center'
                                                    }}
                                                >
                                                    {ICONS.DOWN}
                                                </Text>
                                            </Box>
                                            <Box
                                                className="subject-grades"
                                                style={{ padding: expandedSubject === avg.subject._id ? '0 15px 15px' : 0, display: expandedSubject === avg.subject._id ? 'block' : 'none' }}
                                            >
                                                <Box
                                                    className="grades-table"
                                                    style={{ width: '100%', borderCollapse: 'collapse', marginTop: '10px', fontSize: '14px' }}
                                                    as="table"
                                                >
                                                    <thead>
                                                        <tr>
                                                            <th style={{ textAlign: 'left', padding: '8px 5px', color: '#666', fontWeight: 'normal', fontSize: '12px', borderBottom: '1px solid #eee' }}>
                                                                Loại điểm
                                                            </th>
                                                            <th style={{ textAlign: 'center', padding: '8px 5px', color: '#666', fontWeight: 'normal', fontSize: '12px', borderBottom: '1px solid #eee' }}>
                                                                Điểm
                                                            </th>
                                                            <th style={{ textAlign: 'center', padding: '8px 5px', color: '#666', fontWeight: 'normal', fontSize: '12px', borderBottom: '1px solid #eee' }}>
                                                                Hệ số
                                                            </th>
                                                            <th style={{ textAlign: 'left', padding: '8px 5px', color: '#666', fontWeight: 'normal', fontSize: '12px', borderBottom: '1px solid #eee' }}>
                                                                Ngày kiểm tra
                                                            </th>
                                                            <th style={{ textAlign: 'left', padding: '8px 5px', color: '#666', fontWeight: 'normal', fontSize: '12px', borderBottom: '1px solid #eee' }}>
                                                                Mô tả
                                                            </th>
                                                        </tr>
                                                    </thead>
                                                    <tbody>
                                                        {subjectGrades.map((grade) => (
                                                            <tr key={grade._id}>
                                                                <td style={{ padding: '10px 5px', borderBottom: '1px solid #eee' }}>
                                                                    <Text
                                                                        className={`grade-type grade-type-${grade.gradeType}`}
                                                                        style={{
                                                                            padding: '3px 6px',
                                                                            borderRadius: '4px',
                                                                            fontSize: '12px',
                                                                            display: 'inline-block',
                                                                            textAlign: 'center',
                                                                            minWidth: '75px',
                                                                            backgroundColor:
                                                                                grade.gradeType === 'oral'
                                                                                    ? '#e8f0fe'
                                                                                    : grade.gradeType === 'fifteen_minutes'
                                                                                        ? '#e6f7ee'
                                                                                        : grade.gradeType === 'lesson'
                                                                                            ? '#fff8e6'
                                                                                            : grade.gradeType === 'midterm'
                                                                                                ? '#f0e8fe'
                                                                                                : '#ffe5e5',
                                                                            color:
                                                                                grade.gradeType === 'oral'
                                                                                    ? '#0068ff'
                                                                                    : grade.gradeType === 'fifteen_minutes'
                                                                                        ? '#34c759'
                                                                                        : grade.gradeType === 'lesson'
                                                                                            ? '#ff9500'
                                                                                            : grade.gradeType === 'midterm'
                                                                                                ? '#5856d6'
                                                                                                : '#ff3b30',
                                                                        }}
                                                                    >
                                                                        {formatGradeType(grade.gradeType)}
                                                                    </Text>
                                                                </td>
                                                                <td className="grade-value" style={{ padding: '10px 5px', borderBottom: '1px solid #eee', fontWeight: 'bold', textAlign: 'center' }}>
                                                                    {grade.value.toFixed(1)}
                                                                </td>
                                                                <td className="coefficient" style={{ padding: '10px 5px', borderBottom: '1px solid #eee', fontSize: '12px', color: '#666', textAlign: 'center' }}>
                                                                    x{grade.coefficient}
                                                                </td>
                                                                <td className="date" style={{ padding: '10px 5px', borderBottom: '1px solid #eee', fontSize: '12px', color: '#666' }}>
                                                                    {formatDate(grade.date)}
                                                                </td>
                                                                <td style={{ padding: '10px 5px', borderBottom: '1px solid #eee' }}>{grade.description}</td>
                                                            </tr>
                                                        ))}
                                                    </tbody>
                                                </Box>
                                                <Box
                                                    className="average-section"
                                                    style={{ display: 'flex', justifyContent: 'space-between', marginTop: '10px', paddingTop: '10px', borderTop: '1px dashed #eee' }}
                                                >
                                                    <Text className="average-title" style={{ fontSize: '14px', fontWeight: 'bold', color: '#666' }}>
                                                        Điểm trung bình môn:
                                                    </Text>
                                                    <Text className="average-value" style={{ fontSize: '16px', fontWeight: 'bold', color: '#0068ff' }}>
                                                        {avg.average.toFixed(1)}
                                                    </Text>
                                                </Box>
                                            </Box>
                                        </Box>
                                    );
                                })
                        ) : (
                            <Text style={{ padding: '15px', textAlign: 'center', color: '#666' }}>Chưa có điểm môn học</Text>
                        )}
                    </Box>
                </>
            )}

            <BottomNavigationEdu active="grades" />
        </Box>
    );
};

export default Grades;
import React, { useState } from 'react';
import { Box, Text, Input, Modal } from 'zmp-ui';

const SmartSelect = ({ 
    placeholder, 
    value, 
    onChange, 
    options = [], 
    disabled = false,
    renderOption,
    searchKey = 'name',
    label,
    required = false,
    error,
    style = {},
    modalTitle,
    noDataMessage = "Không có dữ liệu",
    noResultsMessage = "Không tìm thấy kết quả"
}) => {
    const [isOpen, setIsOpen] = useState(false);
    const [searchTerm, setSearchTerm] = useState('');
    
    const filteredOptions = options.filter(option => {
        if (!searchTerm) return true;
        
        const searchLower = searchTerm.toLowerCase();
        
        // Search by searchKey
        if (option[searchKey] && option[searchKey].toLowerCase().includes(searchLower)) {
            return true;
        }
        
        // Search by label if exists
        if (option.label && option.label.toLowerCase().includes(searchLower)) {
            return true;
        }
        
        // Search by name if exists
        if (option.name && option.name.toLowerCase().includes(searchLower)) {
            return true;
        }
        
        return false;
    });
    
    const selectedOption = options.find(opt => opt.value === value);
    
    const handleOptionClick = (optionValue) => {
        onChange(optionValue);
        setIsOpen(false);
        setSearchTerm('');
    };
    
    const handleClose = () => {
        setIsOpen(false);
        setSearchTerm('');
    };
    
    return (
        <Box style={{ marginBottom: '20px', ...style }}>
            {label && (
                <Text bold style={{ fontSize: '14px', marginBottom: '8px', color: '#333' }}>
                    {label} {required && <Text style={{ color: '#dc3545' }}>*</Text>}
                </Text>
            )}
            
            <Box
                onClick={() => !disabled && setIsOpen(true)}
                style={{
                    border: '1px solid #ddd',
                    borderRadius: '8px',
                    padding: '12px',
                    backgroundColor: disabled ? '#f5f5f5' : 'white',
                    cursor: disabled ? 'not-allowed' : 'pointer',
                    minHeight: '44px',
                    display: 'flex',
                    alignItems: 'center',
                    justifyContent: 'space-between',
                    color: selectedOption ? '#333' : '#999',
                    opacity: disabled ? 0.6 : 1
                }}
            >
                <Text style={{ 
                    color: selectedOption ? '#333' : '#999',
                    fontSize: '14px',
                    flex: 1
                }}>
                    {selectedOption ? (renderOption ? renderOption(selectedOption) : selectedOption.name || selectedOption.label) : placeholder}
                </Text>
                <Text style={{ color: '#666', fontSize: '12px' }}>
                    {disabled ? '' : '▼'}
                </Text>
            </Box>
            
            {error && (
                <Text style={{ color: '#dc3545', fontSize: '12px', marginTop: '5px' }}>
                    {error}
                </Text>
            )}
            
            <Modal
                visible={isOpen}
                onClose={handleClose}
                title={modalTitle || `Chọn ${label || 'mục'}`}
                style={{ 
                    '--modal-border-radius': '16px 16px 0 0',
                    '--modal-max-height': '80vh'
                }}
            >
                <Box style={{ padding: '0 16px 16px' }}>
                    {/* Search Input */}
                    <Input
                        placeholder={`Tìm kiếm ${(label || 'mục').toLowerCase()}...`}
                        value={searchTerm}
                        onChange={(e) => setSearchTerm(e.target.value)}
                        style={{ 
                            marginBottom: '16px',
                            border: '1px solid #ddd',
                            borderRadius: '8px'
                        }}
                        prefix="🔍"
                    />
                    
                    {/* Options List */}
                    <Box style={{ 
                        maxHeight: '50vh', 
                        overflowY: 'auto',
                        border: '1px solid #eee',
                        borderRadius: '8px'
                    }}>
                        {filteredOptions.length === 0 ? (
                            <Box style={{ padding: '20px', textAlign: 'center' }}>
                                <Text style={{ color: '#999' }}>
                                    {options.length === 0 ? noDataMessage : noResultsMessage}
                                </Text>
                            </Box>
                        ) : (
                            filteredOptions.map((option, index) => (
                                <Box
                                    key={option.value || index}
                                    onClick={() => handleOptionClick(option.value)}
                                    style={{
                                        padding: '12px 16px',
                                        borderBottom: index < filteredOptions.length - 1 ? '1px solid #eee' : 'none',
                                        cursor: 'pointer',
                                        backgroundColor: value === option.value ? '#e3f2fd' : 'transparent',
                                        transition: 'background-color 0.2s'
                                    }}
                                >
                                    <Text style={{ 
                                        fontSize: '14px',
                                        color: value === option.value ? '#1976d2' : '#333',
                                        fontWeight: value === option.value ? 'bold' : 'normal'
                                    }}>
                                        {renderOption ? renderOption(option) : option.name || option.label}
                                    </Text>
                                </Box>
                            ))
                        )}
                    </Box>
                    
                    {/* Results Count */}
                    {filteredOptions.length > 0 && (
                        <Text style={{ 
                            fontSize: '12px', 
                            color: '#666', 
                            marginTop: '8px', 
                            textAlign: 'center' 
                        }}>
                            {filteredOptions.length} kết quả
                        </Text>
                    )}
                </Box>
            </Modal>
        </Box>
    );
};

export default SmartSelect; 
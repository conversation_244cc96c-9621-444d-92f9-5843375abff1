import React, { useState, useContext, useEffect, useCallback, useRef } from 'react';
import { Box, Text, Button, useNavigate, Modal, Input, Select, DatePicker } from 'zmp-ui';
import HeaderEdu from '../../components/HeaderEdu';
import HeaderSpacer from '../../components/utils/HeaderSpacer';
import BottomNavigationEdu from '../../components/BottomNavigationEdu';
import { AuthContext } from '../../context/AuthContext';
import { authApi } from '../../utils/api';
import { format, formatDistanceToNow } from 'date-fns';
import vi from 'date-fns/locale/vi';
import LoadingIndicator from '../../components/utils/LoadingIndicator';
import DateInput from '../../components/utils/DateInput';
import TabFilter from '../../components/utils/TabFilter';
import useApiCache from '../../hooks/useApiCache';
import useNotification from '../../hooks/useNotification';
import useInfiniteScroll from '../../hooks/useInfiniteScroll';
import ImageWithLoading from '../../components/utils/ImageWithLoading';
import { TIME_SLOTS } from '../../constants/exam';
import { ICONS } from '../../constants/icons';

const MyLeaveRequests = () => {
    const navigate = useNavigate();
    const { user, loading: authLoading } = useContext(AuthContext);
    const [selectedTab, setSelectedTab] = useState('all');
    const [selectedRequest, setSelectedRequest] = useState(null);
    const [detailModalVisible, setDetailModalVisible] = useState(false);
    const [deleteModalVisible, setDeleteModalVisible] = useState(false);
    const [requestToDelete, setRequestToDelete] = useState(null);
    const [deleting, setDeleting] = useState(false);

    // Filter and pagination states
    const [currentPage, setCurrentPage] = useState(1);
    const [pageLimit] = useState(15);
    const [dateFilter, setDateFilter] = useState({
        startDate: '',
        endDate: ''
    });
    const [displayDateFilter, setDisplayDateFilter] = useState({
        startDate: '',
        endDate: ''
    });
    const [showFilters, setShowFilters] = useState(false);

    // Pagination info from API response
    const [paginationInfo, setPaginationInfo] = useState({
        totalCount: 0,
        totalPages: 0,
        hasNextPage: false,
        hasPrevPage: false,
        count: 0
    });

    // Infinite scroll states
    const [allLeaveRequests, setAllLeaveRequests] = useState([]);
    const [loadingMore, setLoadingMore] = useState(false);

    const notification = useNotification();

    // Kiểm tra đăng nhập
    useEffect(() => {
        if (!authLoading && !user) {
            navigate('/login', { replace: true });
        }
    }, [user, authLoading, navigate]);

    // API function để lấy danh sách đơn xin nghỉ của user với filters và pagination
    const fetchMyLeaveRequests = useCallback(async (page = 1, isLoadMore = false) => {
        if (!user) return [];

        if (isLoadMore) {
            setLoadingMore(true);
        }

        try {
            // Build query parameters
            const params = new URLSearchParams();
            if (selectedTab !== 'all') {
                params.append('status', selectedTab);
            }
            params.append('page', page.toString());
            params.append('limit', pageLimit.toString());

            // Send yyyy-mm-dd format to API
            if (dateFilter.startDate) {
                params.append('startDate', dateFilter.startDate);
            }
            if (dateFilter.endDate) {
                params.append('endDate', dateFilter.endDate);
            }

            const response = await authApi.get(`/leave-requests/user/${user._id}?${params.toString()}`);
            const responseData = response.data;

            // Update pagination info
            setPaginationInfo({
                totalCount: responseData.totalCount || 0,
                totalPages: responseData.totalPages || 0,
                hasNextPage: responseData.hasNextPage || false,
                hasPrevPage: responseData.hasPrevPage || false,
                count: responseData.count || 0
            });

            const newData = responseData.data || [];

            if (isLoadMore) {
                setAllLeaveRequests(prev => [...prev, ...newData]);
                setCurrentPage(page);
            }

            return newData;
        } catch (error) {
            console.error('Error fetching my leave requests:', error);
            // Reset pagination info on error
            setPaginationInfo({
                totalCount: 0,
                totalPages: 0,
                hasNextPage: false,
                hasPrevPage: false,
                count: 0
            });
            return [];
        } finally {
            if (isLoadMore) {
                setLoadingMore(false);
            }
        }
    }, [user, selectedTab, pageLimit, dateFilter.startDate, dateFilter.endDate]);

    // Use useApiCache for initial load only
    const {
        loading,
        refetch,
        data: cachedData
    } = useApiCache(
        () => fetchMyLeaveRequests(1, false),
        [user?._id, selectedTab, dateFilter.startDate, dateFilter.endDate],
        {
            cacheKey: `my_leave_requests_${user?._id}_${selectedTab}_${dateFilter.startDate}_${dateFilter.endDate}`,
            enabled: !!user,
            cacheTime: 1 * 60 * 1000, // Giảm cache time xuống 1 phút
            onSuccess: (data) => {
                // Update allLeaveRequests when API call succeeds
                setAllLeaveRequests(Array.isArray(data) ? data : []);
                setCurrentPage(1);
            }
        }
    );

    // Reset page when filters change
    useEffect(() => {
        setCurrentPage(1);
    }, [selectedTab, dateFilter.startDate, dateFilter.endDate]);

    // Load more function for infinite scroll
    const handleLoadMore = useCallback(() => {
        if (currentPage < paginationInfo.totalPages && !loadingMore) {
            fetchMyLeaveRequests(currentPage + 1, true);
        }
    }, [currentPage, paginationInfo.totalPages, loadingMore, fetchMyLeaveRequests]);

    // Infinite scroll hook
    const { observerRef } = useInfiniteScroll({
        currentPage,
        totalPages: paginationInfo.totalPages,
        loading: loadingMore,
        onLoadMore: handleLoadMore,
        enabled: true
    });

    // Ensure leaveRequests is always an array
    const safeLeaveRequests = Array.isArray(allLeaveRequests) ? allLeaveRequests : [];

    // Helper functions for date conversion
    const formatDateForDisplay = (dateString) => {
        if (!dateString) return '';
        const date = new Date(dateString);
        return format(date, 'dd/MM/yyyy');
    };

    // Handle date filter change
    const handleDateFilterChange = (field, value) => {
        // Store yyyy-mm-dd for API
        setDateFilter(prev => ({
            ...prev,
            [field]: value
        }));

        // Store dd/mm/yyyy for display
        if (value) {
            setDisplayDateFilter(prev => ({
                ...prev,
                [field]: formatDateForDisplay(value)
            }));
        } else {
            setDisplayDateFilter(prev => ({
                ...prev,
                [field]: ''
            }));
        }
    };

    // Clear filters
    const clearFilters = () => {
        setDateFilter({
            startDate: '',
            endDate: ''
        });
        setDisplayDateFilter({
            startDate: '',
            endDate: ''
        });
        setCurrentPage(1);
        setSelectedTab('all');
    };

    // Pagination handlers
    const handlePageChange = (newPage) => {
        if (newPage >= 1 && newPage <= paginationInfo.totalPages) {
            setCurrentPage(newPage);
        }
    };

    // Format date
    const formatDate = (dateString) => {
        try {
            return format(new Date(dateString), 'dd/MM/yyyy', { locale: vi });
        } catch {
            return 'N/A';
        }
    };

    // Get status color and text
    const getStatusInfo = (status) => {
        const statusMap = {
            pending: { color: '#ff9500', text: 'Chờ duyệt', bg: '#fff8e6', icon: ICONS.PENDING },
            approved: { color: '#34c759', text: 'Đã duyệt', bg: '#e6f7ee', icon: ICONS.SUCCESS },
            rejected: { color: '#ff3b30', text: 'Từ chối', bg: '#ffe5e5', icon: ICONS.ERROR }
        };
        return statusMap[status] || statusMap.pending;
    };

    // Handle request click
    const handleRequestClick = (request) => {
        setSelectedRequest(request);
        setDetailModalVisible(true);
    };

    // Handle delete request
    const handleDeleteRequest = (request, e) => {
        e.stopPropagation();
        setRequestToDelete(request);
        setDeleteModalVisible(true);
    };

    // Confirm delete
    const confirmDelete = async () => {
        if (!requestToDelete) return;

        setDeleting(true);
        try {
            await authApi.delete(`/leave-requests/${requestToDelete._id}`);
            setDeleteModalVisible(false);
            setRequestToDelete(null);
            // Force refetch to update UI
            await refetch(true);
            notification.showSuccess('Thành công', 'Đã xóa yêu cầu xin phép');
        } catch (error) {
            console.error('Error deleting leave request:', error);
            notification.showError('Lỗi', 'Không thể xóa yêu cầu. Vui lòng thử lại sau.');
        } finally {
            setDeleting(false);
        }
    };

    // Navigate to create new request
    const handleCreateRequest = () => {
        if (user.role.includes('student')) {
            navigate('/student-leave-request');
        } else if (user.role.includes('teacher') || user.role.includes('TEACHER')) {
            navigate('/teacher-leave-request');
        }
    };

    if (authLoading) {
        return (
            <Box style={{ display: 'flex', justifyContent: 'center', alignItems: 'center', minHeight: '100vh' }}>
                <LoadingIndicator />
            </Box>
        );
    }

    return (
        <Box className="container" style={{ minHeight: '100vh', display: 'flex', flexDirection: 'column', paddingBottom: '60px', backgroundColor: '#f5f5f5' }}>
            <HeaderEdu
                title="Đơn xin nghỉ của tôi"
                showBackButton={true}
                onBackClick={() => navigate(-1)}
            />
            <HeaderSpacer />

            <Box style={{ padding: '15px', flex: 1 }}>
                {/* Filters */}
                <Box style={{ backgroundColor: 'white', borderRadius: '10px', padding: '15px', marginBottom: '15px' }}>
                    <Box style={{ display: 'flex', justifyContent: 'space-between', alignItems: 'center', marginBottom: '15px' }}>
                        <Text bold size="large">Bộ lọc</Text>
                        <Button
                            size="small"
                            onClick={() => setShowFilters(!showFilters)}
                            style={{ backgroundColor: '#f0f8ff', color: '#0068ff' }}
                        >
                            {showFilters ? 'Ẩn' : 'Hiện'} bộ lọc
                        </Button>
                    </Box>

                    {showFilters && (
                        <>
                            <Box style={{ display: 'grid', gridTemplateColumns: 'repeat(2, 1fr)', gap: '12px', marginBottom: '15px' }}>
                                <Box>
                                    <Text style={{ marginBottom: '8px', fontSize: '14px', fontWeight: '500' }}>
                                        Từ ngày:
                                    </Text>
                                    <DateInput
                                        value={dateFilter.startDate}
                                        onChange={(e) => handleDateFilterChange('startDate', e.target.value)}
                                        style={{
                                            padding: '10px',
                                            borderRadius: '6px',
                                            fontSize: '14px'
                                        }}
                                    />
                                    {displayDateFilter.startDate && (
                                        <Text style={{ fontSize: '12px', color: '#0068ff', marginTop: '4px', fontWeight: '500' }}>
                                            {ICONS.CALENDAR} {displayDateFilter.startDate}
                                        </Text>
                                    )}
                                </Box>
                                <Box>
                                    <Text style={{ marginBottom: '8px', fontSize: '14px', fontWeight: '500' }}>
                                        Đến ngày:
                                    </Text>
                                    <DateInput
                                        value={dateFilter.endDate}
                                        onChange={(e) => handleDateFilterChange('endDate', e.target.value)}
                                        min={dateFilter.startDate}
                                        style={{
                                            padding: '10px',
                                            borderRadius: '6px',
                                            fontSize: '14px'
                                        }}
                                    />
                                    {displayDateFilter.endDate && (
                                        <Text style={{ fontSize: '12px', color: '#0068ff', marginTop: '4px', fontWeight: '500' }}>
                                            {ICONS.CALENDAR} {displayDateFilter.endDate}
                                        </Text>
                                    )}
                                </Box>
                            </Box>
                            <Box style={{ display: 'flex', gap: '10px' }}>
                                <Button
                                    size="small"
                                    onClick={clearFilters}
                                    style={{ backgroundColor: '#f5f5f5', color: '#666' }}
                                >
                                    Xóa bộ lọc
                                </Button>
                                <Button
                                    size="small"
                                    onClick={() => refetch()}
                                    style={{ backgroundColor: '#0068ff', color: 'white' }}
                                >
                                    Áp dụng
                                </Button>
                            </Box>
                        </>
                    )}
                </Box>

                {/* Tab filter */}
                <TabFilter
                    tabs={[
                        { key: 'all', label: 'Tất cả', icon: ICONS.LIST, color: '#0068ff' },
                        { key: 'pending', label: 'Chờ duyệt', icon: ICONS.PENDING, color: '#ff9500' },
                        { key: 'approved', label: 'Đã duyệt', icon: ICONS.SUCCESS, color: '#34c759' },
                        { key: 'rejected', label: 'Từ chối', icon: ICONS.ERROR, color: '#ff3b30' }
                    ]}
                    selectedTab={selectedTab}
                    onTabChange={setSelectedTab}
                />

                {/* Stats Summary */}
                {paginationInfo.totalCount > 0 && (
                    <Box style={{ backgroundColor: 'white', borderRadius: '10px', padding: '15px', marginBottom: '15px' }}>
                        <Text style={{ fontSize: '14px', color: '#666' }}>
                            Hiển thị {paginationInfo.count} trong tổng số {paginationInfo.totalCount} đơn •
                            Trang {currentPage}/{paginationInfo.totalPages}
                        </Text>
                    </Box>
                )}

                {/* Request list */}
                {loading ? (
                    <Box style={{ display: 'flex', justifyContent: 'center', padding: '40px' }}>
                        <LoadingIndicator />
                    </Box>
                ) : safeLeaveRequests.length === 0 ? (
                    <Box style={{ backgroundColor: 'white', borderRadius: '10px', padding: '40px', textAlign: 'center' }}>
                        <Text style={{ fontSize: '48px', marginBottom: '10px' }}>
                            {selectedTab === 'pending' ? ICONS.LOADING :
                                                                    selectedTab === 'approved' ? ICONS.SUCCESS :
                                    selectedTab === 'rejected' ? ICONS.ERROR : ICONS.EDIT}
                        </Text>
                        <Text bold size="large" style={{ marginBottom: '8px', color: '#666' }}>
                            {selectedTab === 'all' ? 'Chưa có đơn xin nghỉ' :
                                selectedTab === 'pending' ? 'Không có đơn chờ duyệt' :
                                    selectedTab === 'approved' ? 'Không có đơn đã duyệt' :
                                        'Không có đơn bị từ chối'}
                        </Text>
                        <Text style={{ color: '#999', marginBottom: '20px' }}>
                            {selectedTab === 'all' ? 'Bạn chưa tạo đơn xin nghỉ nào. Hãy tạo đơn đầu tiên!' :
                                selectedTab === 'pending' ? 'Hiện tại không có đơn nào đang chờ duyệt.' :
                                    selectedTab === 'approved' ? 'Chưa có đơn xin nghỉ nào được phê duyệt.' :
                                        'Chưa có đơn xin nghỉ nào bị từ chối.'}
                        </Text>
                        <Button
                            onClick={handleCreateRequest}
                            style={{
                                backgroundColor: '#0068ff',
                                color: 'white',
                                padding: '12px 24px',
                                borderRadius: '8px'
                            }}
                        >
                            + Tạo đơn xin nghỉ mới
                        </Button>
                    </Box>
                ) : (
                    <Box style={{ display: 'flex', flexDirection: 'column', gap: '12px' }}>
                        {safeLeaveRequests.map((request) => {
                            const statusInfo = getStatusInfo(request.status);
                            const canDelete = request.status === 'pending';

                            return (
                                <Box
                                    key={request._id}
                                    onClick={() => handleRequestClick(request)}
                                    style={{
                                        backgroundColor: 'white',
                                        borderRadius: '10px',
                                        padding: '15px',
                                        cursor: 'pointer',
                                        border: `1px solid ${statusInfo.color}20`,
                                        boxShadow: '0 2px 8px rgba(0,0,0,0.1)',
                                        position: 'relative'
                                    }}
                                >
                                    <Box style={{ display: 'flex', justifyContent: 'space-between', alignItems: 'flex-start', marginBottom: '10px' }}>
                                        <Box style={{ flex: 1 }}>
                                            <Text style={{ fontSize: '12px', color: '#666', marginBottom: '5px' }}>
                                                {request.requestType === 'teacher' ? `${ICONS.TEACHER} Đơn xin nghỉ dạy` : `${ICONS.STUDENT} Đơn xin nghỉ học`} •
                                                {formatDistanceToNow(new Date(request.createdAt), { locale: vi, addSuffix: true })}
                                            </Text>
                                            <Text bold size="large" style={{ marginBottom: '5px' }}>
                                                {ICONS.CALENDAR} {formatDate(request.startDate)}
                                                {request.startDate !== request.endDate && ` - ${formatDate(request.endDate)}`}
                                            </Text>
                                        </Box>
                                        <Box style={{ display: 'flex', alignItems: 'center', gap: '8px' }}>
                                            <Box
                                                style={{
                                                    backgroundColor: statusInfo.bg,
                                                    color: statusInfo.color,
                                                    padding: '4px 8px',
                                                    borderRadius: '12px',
                                                    fontSize: '12px',
                                                    fontWeight: 'bold',
                                                    display: 'flex',
                                                    alignItems: 'center',
                                                    gap: '4px'
                                                }}
                                            >
                                                <span>{statusInfo.icon}</span>
                                                {statusInfo.text}
                                            </Box>
                                            {canDelete && (
                                                <Button
                                                    onClick={(e) => handleDeleteRequest(request, e)}
                                                    style={{
                                                        backgroundColor: '#ff4444',
                                                        color: 'white',
                                                        padding: '4px 8px',
                                                        borderRadius: '6px',
                                                        fontSize: '12px',
                                                        minHeight: 'auto'
                                                    }}
                                                >
                                                    🗑️
                                                </Button>
                                            )}
                                        </Box>
                                    </Box>

                                    <Box style={{ marginBottom: '10px' }}>
                                        <Text style={{ fontSize: '14px', marginBottom: '5px' }}>
                                            <Text bold>Buổi:</Text> {Array.isArray(request.sessions) 
                                                ? request.sessions.map(session => TIME_SLOTS[session]).join(', ')
                                                : TIME_SLOTS[request.sessions] || request.sessions}
                                        </Text>
                                        {request.class && (
                                            <Text style={{ fontSize: '14px', marginBottom: '5px' }}>
                                                <Text bold>Lớp:</Text> {request.class.name}
                                            </Text>
                                        )}
                                    </Box>

                                    <Text style={{ fontSize: '14px', color: '#333', lineHeight: 1.4 }}>
                                        <Text bold>Lý do:</Text> {request.reason.length > 100 ? `${request.reason.substring(0, 100)}...` : request.reason}
                                    </Text>

                                    {request.approverNotes && (
                                        <Box style={{ marginTop: '10px', padding: '8px', backgroundColor: '#f9f9f9', borderRadius: '6px' }}>
                                            <Text style={{ fontSize: '12px', color: '#666' }}>
                                                <Text bold>{ICONS.MESSAGE} Ghi chú duyệt:</Text> {request.approverNotes}
                                            </Text>
                                        </Box>
                                    )}

                                    {request.approver && (
                                        <Text style={{ fontSize: '11px', color: '#999', marginTop: '8px' }}>
                                            Duyệt bởi: {request.approver.name} • {formatDistanceToNow(new Date(request.updatedAt), { locale: vi, addSuffix: true })}
                                        </Text>
                                    )}
                                </Box>
                            );
                        })}
                    </Box>
                )}

                {/* Infinite scroll loading indicator */}
                {loadingMore && (
                    <Box style={{ display: 'flex', justifyContent: 'center', padding: '20px' }}>
                        <LoadingIndicator />
                    </Box>
                )}

                {/* Infinite scroll observer */}
                <div ref={observerRef} style={{ height: '20px' }} />
            </Box>

            {/* Detail Modal */}
            <Modal
                visible={detailModalVisible}
                title="Chi tiết đơn xin nghỉ"
                onClose={() => {
                    setDetailModalVisible(false);
                    setSelectedRequest(null);
                }}
                actions={[
                    { text: 'Đóng', close: true }
                ]}
            >
                {selectedRequest && (
                    <Box style={{ padding: '20px' }}>
                        <Box style={{ marginBottom: '20px' }}>
                            <Box style={{ display: 'flex', justifyContent: 'space-between', alignItems: 'center', marginBottom: '15px' }}>
                                <Text bold size="large">
                                    {selectedRequest.requestType === 'teacher' ? 'Đơn xin nghỉ dạy' : 'Đơn xin nghỉ học'}
                                </Text>
                                <Box
                                    style={{
                                        backgroundColor: getStatusInfo(selectedRequest.status).bg,
                                        color: getStatusInfo(selectedRequest.status).color,
                                        padding: '6px 12px',
                                        borderRadius: '12px',
                                        fontSize: '12px',
                                        fontWeight: 'bold'
                                    }}
                                >
                                    {getStatusInfo(selectedRequest.status).text}
                                </Box>
                            </Box>

                            <Box style={{ backgroundColor: '#f9f9f9', padding: '15px', borderRadius: '8px', marginBottom: '15px' }}>
                                <Text style={{ marginBottom: '8px' }}>
                                    <Text bold>Thời gian:</Text> {formatDate(selectedRequest.startDate)}
                                    {selectedRequest.startDate !== selectedRequest.endDate && ` - ${formatDate(selectedRequest.endDate)}`}
                                </Text>
                                <Text style={{ marginBottom: '8px' }}>
                                    <Text bold>Buổi:</Text> {Array.isArray(selectedRequest.sessions) 
                                        ? selectedRequest.sessions.map(session => TIME_SLOTS[session]).join(', ')
                                        : TIME_SLOTS[selectedRequest.sessions] || selectedRequest.sessions}
                                </Text>
                                {selectedRequest.class && (
                                    <Text style={{ marginBottom: '8px' }}>
                                        <Text bold>Lớp:</Text> {selectedRequest.class.name}
                                    </Text>
                                )}
                                <Text style={{ marginBottom: '8px' }}>
                                    <Text bold>Ngày tạo:</Text> {formatDate(selectedRequest.createdAt)}
                                </Text>
                                <Text>
                                    <Text bold>Lý do:</Text> {selectedRequest.reason}
                                </Text>
                            </Box>

                            {/* Attachments */}
                            {selectedRequest.attachments && selectedRequest.attachments.length > 0 && (
                                <Box style={{ marginBottom: '15px' }}>
                                    <Text bold style={{ marginBottom: '8px' }}>Tài liệu đính kèm:</Text>
                                    <Box style={{ display: 'flex', gap: '10px', flexWrap: 'wrap' }}>
                                        {selectedRequest.attachments.map((attachment, index) => (
                                            <ImageWithLoading
                                                key={index}
                                                src={attachment.data}
                                                alt={`Attachment ${index + 1}`}
                                                style={{
                                                    width: '80px',
                                                    height: '80px',
                                                    objectFit: 'cover',
                                                    borderRadius: '8px',
                                                    border: '1px solid #ddd',
                                                    cursor: 'pointer'
                                                }}
                                                onClick={() => window.open(attachment.data, '_blank')}
                                                placeholder="📷\nLỗi tải ảnh"
                                            />
                                        ))}
                                    </Box>
                                </Box>
                            )}

                            {selectedRequest.approverNotes && (
                                <Box style={{ padding: '12px', backgroundColor: '#e8f0fe', borderRadius: '8px', border: '1px solid #0068ff' }}>
                                    <Text bold style={{ marginBottom: '5px', color: '#0068ff' }}>
                                        Ghi chú từ người duyệt:
                                    </Text>
                                    <Text style={{ color: '#333' }}>{selectedRequest.approverNotes}</Text>
                                    {selectedRequest.approver && (
                                        <Text style={{ fontSize: '12px', color: '#666', marginTop: '8px' }}>
                                            - {selectedRequest.approver.name} • {formatDistanceToNow(new Date(selectedRequest.updatedAt), { locale: vi, addSuffix: true })}
                                        </Text>
                                    )}
                                </Box>
                            )}
                        </Box>
                    </Box>
                )}
            </Modal>

            {/* Delete Confirmation Modal */}
            <Modal
                visible={deleteModalVisible}
                title="Xác nhận xóa"
                onClose={() => {
                    setDeleteModalVisible(false);
                    setRequestToDelete(null);
                }}
                actions={[
                    { text: 'Hủy', close: true },
                    {
                        text: deleting ? 'Đang xóa...' : 'Xóa đơn',
                        close: false,
                        onClick: confirmDelete,
                        disabled: deleting,
                        style: { backgroundColor: '#ff3b30', color: 'white' }
                    }
                ]}
            >
                <Box style={{ padding: '20px', textAlign: 'center' }}>
                    <Text style={{ fontSize: '48px', marginBottom: '10px' }}>{ICONS.DELETE}</Text>
                    <Text bold size="large" style={{ marginBottom: '10px' }}>
                        Xóa đơn xin nghỉ?
                    </Text>
                    <Text style={{ color: '#666' }}>
                        Bạn có chắc chắn muốn xóa đơn xin nghỉ này không? Hành động này không thể hoàn tác.
                    </Text>
                </Box>
            </Modal>

            {/* Floating Action Button */}
            <Button
                onClick={handleCreateRequest}
                style={{
                    position: 'fixed',
                    bottom: '80px',
                    right: '20px',
                    width: '56px',
                    height: '56px',
                    borderRadius: '28px',
                    backgroundColor: '#0068ff',
                    color: 'white',
                    display: 'flex',
                    alignItems: 'center',
                    justifyContent: 'center',
                    fontSize: '24px',
                    boxShadow: '0 2px 10px rgba(0, 104, 255, 0.3)',
                    zIndex: 100,
                }}
            >
                +
            </Button>

            <BottomNavigationEdu />
        </Box>
    );
};

export default MyLeaveRequests; 
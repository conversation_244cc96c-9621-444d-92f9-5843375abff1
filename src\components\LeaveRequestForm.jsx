import React, { useState } from 'react';
import { Box, Text, Button, Input, Checkbox } from 'zmp-ui';
import { format } from 'date-fns';
import DateInput from './utils/DateInput';

const LeaveRequestForm = ({
    type = 'student', // 'student' or 'teacher'
    onSubmit,
    loading = false,
    error = '',
    title,
    note,
}) => {
    const [formData, setFormData] = useState({
        startDate: '',
        endDate: '',
        sessions: [],
        reason: '',
        attachments: []
    });
    const [previewImages, setPreviewImages] = useState([]);
    const [uploadProgress, setUploadProgress] = useState(0);
    const [totalImages, setTotalImages] = useState(0);

    // Handle form changes
    const handleInputChange = (field, value) => {
        setFormData(prev => ({
            ...prev,
            [field]: value
        }));
    };

    // Handle session selection
    const handleSessionChange = (session, checked) => {
        setFormData(prev => {
            const sessions = checked 
                ? [...prev.sessions, session]
                : prev.sessions.filter(s => s !== session);
            return { ...prev, sessions };
        });
    };

    // Handle image selection
    const handleImageUpload = (event) => {
        const files = Array.from(event.target.files);
        
        files.forEach(file => {
            if (file.type.startsWith('image/')) {
                const reader = new FileReader();
                reader.onload = (e) => {
                    const base64Data = e.target.result;
                    // Lưu file gốc và preview URL
                    setFormData(prev => ({
                        ...prev,
                        attachments: [...prev.attachments, {
                            file: file,
                            previewUrl: base64Data
                        }]
                    }));
                    setPreviewImages(prev => [...prev, base64Data]);
                };
                reader.readAsDataURL(file);
            }
        });
    };

    // Remove attachment
    const removeAttachment = (index) => {
        setFormData(prev => ({
            ...prev,
            attachments: prev.attachments.filter((_, i) => i !== index)
        }));
        setPreviewImages(prev => prev.filter((_, i) => i !== index));
    };

    // Validate form
    const validateForm = () => {
        if (!formData.startDate) {
            return 'Vui lòng chọn ngày bắt đầu';
        }
        if (!formData.endDate) {
            return 'Vui lòng chọn ngày kết thúc';
        }
        if (new Date(formData.startDate) > new Date(formData.endDate)) {
            return 'Ngày bắt đầu không thể sau ngày kết thúc';
        }
        if (formData.sessions.length === 0) {
            return 'Vui lòng chọn ít nhất một buổi';
        }
        if (!formData.reason.trim()) {
            return 'Vui lòng nhập lý do xin nghỉ';
        }
        return null;
    };

    // Handle submit
    const handleSubmit = () => {
        const error = validateForm();
        if (error) {
            return onSubmit({ error });
        }

        onSubmit({
            data: {
                ...formData,
                requestType: type
            }
        });
    };

    return (
        <Box style={{ backgroundColor: 'white', borderRadius: '10px', padding: '20px', marginBottom: '20px' }}>
            <Text.Title size="large" style={{ marginBottom: '20px', color: '#0068ff' }}>
                {title || (type === 'teacher' ? 'Thông tin đơn xin nghỉ dạy' : 'Thông tin đơn xin nghỉ học')}
            </Text.Title>

            {note && (
                <Box style={{ backgroundColor: '#e8f0fe', padding: '12px', borderRadius: '8px', marginBottom: '20px', border: '1px solid #0068ff' }}>
                    <Text style={{ color: '#0068ff', fontSize: '14px' }}>
                        📋 {note}
                    </Text>
                </Box>
            )}

            {error && (
                <Box style={{ backgroundColor: '#fee', padding: '12px', borderRadius: '8px', marginBottom: '15px', border: '1px solid #fcc' }}>
                    <Text style={{ color: '#c33', fontSize: '14px' }}>{error}</Text>
                </Box>
            )}

            {/* Ngày bắt đầu */}
            <Box style={{ marginBottom: '20px' }}>
                <Box style={{ marginBottom: '8px' }}>
                    <Text.Title style={{ color: '#333', display: 'inline' }}>
                        Ngày bắt đầu nghỉ
                    </Text.Title>
                    <Text style={{ color: 'red', display: 'inline' }}> *</Text>
                </Box>
                <DateInput
                    value={formData.startDate}
                    onChange={(e) => handleInputChange('startDate', e.target.value)}
                    min={format(new Date(), 'yyyy-MM-dd')}
                    style={{
                        padding: '12px',
                        borderRadius: '8px',
                        fontSize: '16px'
                    }}
                />
            </Box>

            {/* Ngày kết thúc */}
            <Box style={{ marginBottom: '20px' }}>
                <Box style={{ marginBottom: '8px' }}>
                    <Text.Title style={{ color: '#333', display: 'inline' }}>
                        Ngày kết thúc nghỉ
                    </Text.Title>
                    <Text style={{ color: 'red', display: 'inline' }}> *</Text>
                </Box>
                <DateInput
                    value={formData.endDate}
                    onChange={(e) => handleInputChange('endDate', e.target.value)}
                    min={formData.startDate || format(new Date(), 'yyyy-MM-dd')}
                    style={{
                        padding: '12px',
                        borderRadius: '8px',
                        fontSize: '16px'
                    }}
                />
            </Box>

            {/* Buổi học/dạy */}
            <Box style={{ marginBottom: '20px' }}>
                <Box style={{ marginBottom: '12px' }}>
                    <Text.Title style={{ color: '#333', display: 'inline' }}>
                        Buổi {type === 'teacher' ? 'dạy' : 'học'} nghỉ
                    </Text.Title>
                    <Text style={{ color: 'red', display: 'inline' }}> *</Text>
                </Box>
                <Box style={{ display: 'flex', gap: '15px', flexWrap: 'wrap' }}>
                    <Box style={{ display: 'flex', alignItems: 'center', gap: '8px' }}>
                        <Checkbox
                            checked={formData.sessions.includes('morning')}
                            onChange={(e) => handleSessionChange('morning', e.target.checked)}
                        />
                        <Text>Buổi sáng</Text>
                    </Box>
                    <Box style={{ display: 'flex', alignItems: 'center', gap: '8px' }}>
                        <Checkbox
                            checked={formData.sessions.includes('afternoon')}
                            onChange={(e) => handleSessionChange('afternoon', e.target.checked)}
                        />
                        <Text>Buổi chiều</Text>
                    </Box>
                </Box>
            </Box>

            {/* Lý do */}
            <Box style={{ marginBottom: '20px' }}>
                <Box style={{ marginBottom: '8px' }}>
                    <Text.Title style={{ color: '#333', display: 'inline' }}>
                        Lý do xin nghỉ
                    </Text.Title>
                    <Text style={{ color: 'red', display: 'inline' }}> *</Text>
                </Box>
                <Input
                    type="textarea"
                    placeholder={`Nhập lý do xin nghỉ ${type === 'teacher' ? 'dạy' : 'học'} (ví dụ: nghỉ ốm, việc gia đình...)...`}
                    value={formData.reason}
                    onChange={(e) => handleInputChange('reason', e.target.value)}
                    style={{ minHeight: '100px' }}
                />
            </Box>

            {/* File đính kèm */}
            <Box style={{ marginBottom: '20px' }}>
                <Text.Title style={{ marginBottom: '8px', color: '#333' }}>
                    File đính kèm (tùy chọn)
                </Text.Title>
                <Box style={{ position: 'relative' }}>
                    <input
                        type="file"
                        accept="image/*"
                        multiple
                        onChange={handleImageUpload}
                        style={{
                            position: 'absolute',
                            width: '100%',
                            height: '100%',
                            opacity: 0,
                            cursor: 'pointer',
                            zIndex: 2
                        }}
                    />
                    <Box
                        style={{
                            border: '2px dashed #0068ff',
                            borderRadius: '8px',
                            padding: '20px',
                            textAlign: 'center',
                            backgroundColor: '#f8f9ff',
                            transition: 'all 0.3s ease',
                            cursor: 'pointer',
                            '&:hover': {
                                backgroundColor: '#f0f4ff',
                                borderColor: '#0052cc'
                            }
                        }}
                    >
                        <Text style={{ color: '#0068ff', fontSize: '16px', marginBottom: '8px' }}>
                            📁 Chọn file hoặc kéo thả vào đây
                        </Text>
                        <Text style={{ fontSize: '12px', color: '#666' }}>
                            Có thể đính kèm giấy tờ y tế, thông báo... (chỉ hỗ trợ file ảnh)
                        </Text>
                    </Box>
                </Box>
            </Box>

            {/* Preview images */}
            {previewImages.length > 0 && (
                <Box style={{ marginBottom: '20px' }}>
                    <Text.Title style={{ marginBottom: '8px', color: '#333' }}>
                        Ảnh đính kèm:
                    </Text.Title>
                    <Box style={{ display: 'flex', gap: '10px', flexWrap: 'wrap' }}>
                        {previewImages.map((src, index) => (
                            <Box key={index} style={{ position: 'relative' }}>
                                <img
                                    src={src}
                                    alt={`Attachment ${index + 1}`}
                                    style={{
                                        width: '80px',
                                        height: '80px',
                                        objectFit: 'cover',
                                        borderRadius: '8px',
                                        border: '1px solid #ddd'
                                    }}
                                />
                                <Button
                                    onClick={() => removeAttachment(index)}
                                    style={{
                                        position: 'absolute',
                                        top: '-5px',
                                        right: '-5px',
                                        width: '20px',
                                        height: '20px',
                                        minWidth: '20px',
                                        maxWidth: '20px',
                                        borderRadius: '50%',
                                        backgroundColor: '#ff4444',
                                        color: 'white',
                                        fontSize: '12px',
                                        padding: '0 !important',
                                        minHeight: '20px',
                                        maxHeight: '20px',
                                        display: 'flex',
                                        alignItems: 'center',
                                        justifyContent: 'center',
                                        border: 'none'
                                    }}
                                >
                                    ×
                                </Button>
                            </Box>
                        ))}
                    </Box>
                </Box>
            )}

            {/* Upload progress */}
            {loading && totalImages > 0 && (
                <Box style={{ marginTop: '10px', textAlign: 'center' }}>
                    <Text>
                        Đang tải lên ảnh {uploadProgress}/{totalImages}
                    </Text>
                    <div style={{
                        width: '100%',
                        height: '4px',
                        backgroundColor: '#eee',
                        borderRadius: '2px',
                        marginTop: '5px'
                    }}>
                        <div style={{
                            width: `${(uploadProgress / totalImages) * 100}%`,
                            height: '100%',
                            backgroundColor: '#0068ff',
                            borderRadius: '2px',
                            transition: 'width 0.3s ease'
                        }} />
                    </div>
                </Box>
            )}

            {/* Submit button */}
            <Button
                fullWidth
                variant="primary"
                onClick={handleSubmit}
                loading={loading}
                disabled={loading}
                style={{
                    backgroundColor: '#0068ff',
                    marginTop: '10px',
                    padding: '15px'
                }}
            >
                {loading ? 'Đang gửi đơn...' : 'Gửi đơn xin nghỉ'}
            </Button>
        </Box>
    );
};

export default LeaveRequestForm; 
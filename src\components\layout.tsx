import { getSystemInfo } from 'zmp-sdk';
import { AnimationRoutes, App, Route, SnackbarProvider, ZMPRouter } from 'zmp-ui';
import { AppProps } from 'zmp-ui/app';
import { useEffect, useState, useRef, useMemo } from 'react';
import { useNavigate, useLocation } from 'zmp-ui';
import useSwipeNavigation from '@/hooks/useSwipeNavigation';
import Login from '@/pages/Login';
import StudentEdu from '@/pages/students/StudentEdu';
import TeacherEdu from '@/pages/teachers/TeacherEdu';
import Profile from '@/pages/Profile';
import ScheduleEdu from '@/pages/students/ScheduleEdu';
import AllAnnouncements from '@/pages/AllAnnouncements';
import AllEvents from '@/pages/AllEvents';
import AllNews from '@/pages/AllNews';
import NewsDetail from '@/pages/NewsDetail';
import { parseJwt } from '@/utils/jwt';
import { SchoolYearProvider } from '@/context/SchoolYearContext';
import { AuthProvider } from '@/context/AuthContext';
import AuthInitializer from '@/components/AuthInitializer';
import Grades from '@/pages/students/Grades';
import Exams from '@/pages/students/Exams';
import ExerciseDetail from '@/pages/students/ExerciseDetail';
import Attempt from '@/pages/students/Attempt';
import AttemptResults from '@/pages/students/AttemptResults';
import ExerciseListBySubject from '@/pages/students/ExerciseListBySubject';
import Attendance from '@/pages/students/Attendance';
import Directory from '@/pages/Directory';
import AllTeacherClasses from '@/pages/teachers/AllTeacherClasses';
// Violation Management Pages
import MyViolations from '@/pages/students/MyViolations';
import ConductPoints from '@/pages/students/ConductPoints';
import CreateViolation from '@/pages/teachers/CreateViolation';
import ViolationsList from '@/pages/teachers/ViolationsList';
import ViolationsStats from '@/pages/teachers/ViolationsStats';
import ConductManagement from '@/pages/teachers/ConductManagement';
import TeacherSchedule from '@/pages/teachers/TeacherSchedule';
import StudentLeaveRequest from '@/pages/students/StudentLeaveRequest';
import TeacherLeaveRequest from '@/pages/teachers/TeacherLeaveRequest';
import LeaveRequestApproval from '@/pages/teachers/LeaveRequestApproval';
import MyLeaveRequests from '@/pages/teachers/MyLeaveRequests';
import TeacherGrading from '@/pages/teachers/TeacherGrading';
import ExamResults from '@/pages/students/ExamResults';
import StudentAttempts from '@/pages/students/StudentAttempts';
import SchoolYearSelection from '@/pages/SchoolYearSelection';
import StudentAttendanceDetails from '@/pages/students/StudentAttendanceDetails';
import NotificationProvider from '@/components/utils/NotificationProvider';
import AnnouncementDetail from '@/components/AnnouncementDetail';
import ExamSupervisionList from '@/pages/exam-supervisions/ExamSupervisionList';
import ExamSupervisionForm from '@/pages/exam-supervisions/ExamSupervisionForm';
import ExamChangeRequests from '@/pages/exam-supervisions/ExamChangeRequests';
import ExamChangeRequestForm from '@/pages/exam-supervisions/ExamChangeRequestForm';
import TeacherExamSchedule from '@/pages/exam-supervisions/TeacherExamSchedule';
import EditExamSupervision from '@/pages/exam-supervisions/EditExamSupervision';
import MyExamChangeRequests from '@/pages/exam-supervisions/MyExamChangeRequests';

// Route mapping để biết component nào render cho path nào
const getComponentForPath = (path: string, navigate: any) => {
  // Helper function to wrap components with AuthInitializer
  const withAuth = (Component: any) => (
    <AuthInitializer>
      <Component />
    </AuthInitializer>
  );

  if (path === '/login') return <Login />;
  if (path === '/student') return withAuth(StudentEdu);
  if (path === '/teacher') return withAuth(TeacherEdu);
  if (path === '/profile') return withAuth(Profile);
  if (path === '/schedule') return withAuth(ScheduleEdu);
  if (path === '/announcements') return withAuth(AllAnnouncements);
  if (path === '/all-events') return withAuth(AllEvents);
  if (path === '/grades') return withAuth(Grades);
  if (path === '/exams') return withAuth(Exams);
  if (path === '/attendance') return withAuth(Attendance);
  if (path === '/directories') return withAuth(Directory);
  if (path === '/news') return withAuth(AllNews);
  if (path === '/teacher-classes') return withAuth(AllTeacherClasses);
  // Violation Management Routes
  if (path === '/my-violations') return withAuth(MyViolations);
  if (path === '/conduct-points') return withAuth(ConductPoints);
  if (path === '/create-violation') return withAuth(CreateViolation);
  if (path === '/violations-list') return withAuth(ViolationsList);
  if (path === '/violations-stats') return withAuth(ViolationsStats);
  if (path === '/conduct-management') return withAuth(ConductManagement);
  if (path === '/teacher-schedule') return withAuth(TeacherSchedule);
  if (path === '/teacher-grading') return withAuth(TeacherGrading);
  
  // Leave request routes
  if (path === '/student-leave-request') return withAuth(StudentLeaveRequest);
  if (path === '/teacher-leave-request') return withAuth(TeacherLeaveRequest);
  if (path === '/leave-request-approval') return withAuth(LeaveRequestApproval);
  if (path === '/my-leave-requests') return withAuth(MyLeaveRequests);
  
  // Dynamic routes
  if (path.startsWith('/exercises/')) return withAuth(ExerciseDetail);
  if (path.startsWith('/attempt/') && path.endsWith('/results')) return withAuth(AttemptResults);
  if (path.startsWith('/attempt/')) return withAuth(Attempt);
  if (path.startsWith('/subjects/') && path.endsWith('/exercises')) return withAuth(ExerciseListBySubject);
  if (path.startsWith('/news/')) return withAuth(NewsDetail);
  if (path.startsWith('/exam-results/')) return withAuth(ExamResults);
  if (path.startsWith('/student-attempts/')) return withAuth(StudentAttempts);
  if (path.startsWith('/student-attendance-details/')) return withAuth(StudentAttendanceDetails);
  if (path === '/announcement-detail' || path.startsWith('/announcement-detail/')) return withAuth(AnnouncementDetail);
  
  // Add new exam supervision routes
  if (path === '/exam-supervisions') return withAuth(ExamSupervisionList);
  if (path === '/exam-supervisions/create') return withAuth(ExamSupervisionForm);
  if (path === '/exam-supervisions/edit/:id') return withAuth(ExamSupervisionForm);
  if (path === '/exam-change-requests') return withAuth(ExamChangeRequests);
  if (path === '/exam-change-requests/create') return withAuth(ExamChangeRequestForm);
  if (path === '/my-exam-change-requests') return withAuth(MyExamChangeRequests);
  
  // New teacher exam schedule route
  if (path === '/teacher-exam-schedule') return withAuth(TeacherExamSchedule);
  
  // Fallback
  return withAuth(StudentEdu);
};

const AuthRedirect = () => {
  const navigate = useNavigate();

  useEffect(() => {
    const token = localStorage.getItem('token');
    if (token) {
      const parsedToken = parseJwt(token);
      if (parsedToken && parsedToken.exp * 1000 > Date.now()) {
        if (parsedToken.user.role.includes('student')) {
          navigate('/student', { replace: true });
        } else if (parsedToken.user.role.includes('teacher') || parsedToken.user.role.includes('TEACHER')) {
          navigate('/teacher', { replace: true });
        } else {
          localStorage.removeItem('token');
          localStorage.removeItem('user');
          localStorage.removeItem('selectedSchoolYear');
          localStorage.removeItem('showNoClassModal');
          navigate('/login', { replace: true });
        }
      } else {
        localStorage.removeItem('token');
        localStorage.removeItem('user');
        localStorage.removeItem('selectedSchoolYear');
        localStorage.removeItem('showNoClassModal');
        navigate('/login', { replace: true });
      }
    } else {
      navigate('/login', { replace: true });
    }
  }, [navigate]);

  return null;
};

const SwipeWrapper = ({ children }) => {
  const navigate = useNavigate();
  const location = useLocation();
  const [isSwipeNavigating, setIsSwipeNavigating] = useState(false);
  
  // Track navigation history
  const historyStackRef = useRef<string[]>([]);
  const [previousPath, setPreviousPath] = useState<string>('');
  
  // Update history when location changes
  useEffect(() => {
    const currentPath = location.pathname;
    const lastPath = historyStackRef.current[historyStackRef.current.length - 1];
    
    if (currentPath !== lastPath) {
      // Set previous path before updating history
      if (historyStackRef.current.length > 0) {
        setPreviousPath(historyStackRef.current[historyStackRef.current.length - 1]);
      }
      
      // Add to history
      historyStackRef.current.push(currentPath);
      
      // Keep only last 10 entries to prevent memory issues
      if (historyStackRef.current.length > 10) {
        historyStackRef.current = historyStackRef.current.slice(-10);
      }
    }
  }, [location.pathname]);
  
  // Check nếu trang hiện tại là restricted page thì disable swipe
  const restrictedPages = ['/login', '/school-year-selection', '/'];
  const isRestrictedPage = restrictedPages.includes(location.pathname);
  
  // Sử dụng hook swipe navigation mới với previous screen preview
  const { 
    isActiveSwipe, 
    swipeProgress, 
    getCurrentScreenStyles, 
    getPreviousScreenStyles,
    previousScreenVisible,
    canShowPreviousScreen
  } = useSwipeNavigation({
    enabled: !isRestrictedPage, // Disable hoàn toàn trên restricted pages
    threshold: 80, // Giảm từ 100 xuống 80 để dễ trigger hơn
    velocityThreshold: 0.3, // Giảm từ 0.4 xuống 0.3 để responsive hơn
    edgeThreshold: 40, // Tăng từ 30 lên 40 để vùng swipe rộng hơn
    maxEdgeStart: 30, // Tăng từ 20 lên 30 để dễ bắt đầu swipe hơn
    resistanceDistance: 60, // Tăng từ 50 lên 60 để có hiệu ứng mượt hơn
    enablePreviousScreenPreview: true, // Enable Facebook-like effect
    enableHapticFeedback: true,
    enableVisualFeedback: false, // Tắt visual feedback mặc định, tự custom
    debounceMs: 50, // Giảm từ 100 xuống 50 để responsive hơn
    
    onSwipeComplete: () => {
      // Prevent double navigation
      if (isSwipeNavigating) {
        console.log('Layout Swipe: Navigation already in progress, ignoring');
        return;
      }
      
      setIsSwipeNavigating(true);
      console.log('Layout Swipe: onSwipeComplete triggered');
      console.log('Layout Swipe: current location', location);
      console.log('Layout Swipe: location.state', location.state);
      console.log('Layout Swipe: window.history.length', window.history.length);
      
      // NGĂN CHẶN swipe back từ một số trang đặc biệt để tránh navigation loop
      const restrictedPages = ['/login', '/school-year-selection', '/'];
      if (restrictedPages.includes(location.pathname)) {
        console.log('Layout Swipe: Swipe back disabled for restricted page:', location.pathname);
        setIsSwipeNavigating(false);
        return;
      }
      
      // Sử dụng logic CHÍNH XÁC y hệt HeaderEdu
      // Nếu có thông tin trang trước đó trong state, quay lại trang đó
      if (location.state?.previousPath) {
        console.log('Layout Swipe: Navigating to previousPath', location.state.previousPath);
        navigate(location.state.previousPath, {
          replace: true, // Use replace to avoid adding to history
          state: location.state.previousState || {}
        });
      } else if (location.state?.returnTo) {
        console.log('Layout Swipe: Navigating to returnTo', location.state.returnTo);
        navigate(location.state.returnTo.path, {
          replace: true,
          state: location.state.returnTo.state || {}
        });
      } else if (window.history.length > 1) {
        // Use browser's native back navigation
        console.log('Layout Swipe: Using browser back navigation');
        window.history.back();
      } else {
        // Fallback navigation - nhưng cần check role để redirect đúng
        console.log('Layout Swipe: Using fallback navigation');
        const token = localStorage.getItem('token');
        if (token) {
          try {
            const parsedToken = JSON.parse(atob(token.split('.')[1]));
            const userRole = parsedToken.user?.role;
            if (userRole?.includes('teacher')) {
              navigate('/teacher', { replace: true });
            } else if (userRole?.includes('student')) {
              navigate('/student', { replace: true });
            } else {
              navigate('/announcements', { replace: true });
            }
          } catch (error) {
            console.error('Layout Swipe: Error parsing token:', error);
            navigate('/announcements', { replace: true });
          }
        } else {
          navigate('/announcements', { replace: true });
        }
      }
      
      // Reset flag after a delay
      setTimeout(() => setIsSwipeNavigating(false), 1000);
    },
  });

  // Back indicator style (only show if no previous screen preview)
  const backIndicatorStyle: any = {
    position: 'fixed',
    left: `${isActiveSwipe && !canShowPreviousScreen ? Math.min(swipeProgress * 40, 25) : -40}px`,
    top: '50%',
    transform: 'translateY(-50%)',
    width: '32px',
    height: '32px',
    backgroundColor: '#0068ff',
    borderRadius: '50%',
    display: 'flex',
    alignItems: 'center',
    justifyContent: 'center',
    color: 'white',
    fontSize: '16px',
    fontWeight: 'bold',
    opacity: isActiveSwipe && !canShowPreviousScreen ? Math.min(swipeProgress * 1.5, 1) : 0,
    transition: isActiveSwipe ? 'none' : 'all 0.3s cubic-bezier(0.4, 0, 0.2, 1)',
    zIndex: 9998,
    boxShadow: '0 4px 12px rgba(0, 104, 255, 0.4)',
    pointerEvents: 'none'
  };

  // Overlay effect - darken the background when swiping
  const overlayStyle: any = {
    position: 'fixed',
    top: 0,
    left: 0,
    width: '100%',
    height: '100%',
    background: `rgba(0, 0, 0, ${isActiveSwipe ? Math.min(swipeProgress * 0.3, 0.3) : 0})`,
    pointerEvents: 'none',
    transition: isActiveSwipe ? 'none' : 'background 0.3s cubic-bezier(0.4, 0, 0.2, 1)',
    zIndex: canShowPreviousScreen ? 0 : 9997 // Lower z-index when showing previous screen
  };

  // Memoize previous screen component để tránh re-render không cần thiết
  const previousScreenComponent = useMemo(() => {
    if (!previousPath) return null;
    return getComponentForPath(previousPath, navigate);
  }, [previousPath, navigate]);

  return (
    <>
      {/* Previous Screen Preview */}
      {canShowPreviousScreen && previousScreenVisible && previousScreenComponent && (
        <div style={getPreviousScreenStyles()}>
          {previousScreenComponent}
        </div>
      )}
      
      {/* Overlay Effect */}
      <div style={overlayStyle} />
      
      {/* Back Indicator (only when not showing previous screen) */}
      <div style={backIndicatorStyle}>
        ←
      </div>
      
      {/* Main Content */}
      <div style={getCurrentScreenStyles()}>
        {children}
      </div>
    </>
  );
};

const Layout = () => {
  return (
    <App theme={getSystemInfo().zaloTheme as AppProps['theme']}>
      <ZMPRouter>
        <AuthProvider>
          <SchoolYearProvider>
            <SnackbarProvider>
              <NotificationProvider>
                <SwipeWrapper>
                  <AnimationRoutes>
                    <Route path="/" element={<AuthRedirect />} />
                    <Route path="/login" element={<Login />} />
                    <Route
                      path="/school-year-selection"
                      element={
                        <AuthInitializer>
                          <SchoolYearSelection />
                        </AuthInitializer>
                      }
                    />
                    <Route
                      path="/student"
                      element={
                        <AuthInitializer>
                          <StudentEdu />
                        </AuthInitializer>
                      }
                    />
                    <Route
                      path="/teacher"
                      element={
                        <AuthInitializer>
                          <TeacherEdu />
                        </AuthInitializer>
                      }
                    />
                    <Route
                      path="/profile"
                      element={
                        <AuthInitializer>
                          <Profile />
                        </AuthInitializer>
                      }
                    />
                    <Route
                      path="/schedule"
                      element={
                        <AuthInitializer>
                          <ScheduleEdu />
                        </AuthInitializer>
                      }
                    />
                    <Route
                      path="/announcements"
                      element={
                        <AuthInitializer>
                          <AllAnnouncements />
                        </AuthInitializer>
                      }
                    />
                    <Route
                      path="/all-events"
                      element={
                        <AuthInitializer>
                          <AllEvents />
                        </AuthInitializer>
                      }
                    />
                    <Route
                      path="/grades"
                      element={
                        <AuthInitializer>
                          <Grades />
                        </AuthInitializer>
                      }
                    />
                    <Route
                      path="/exams"
                      element={
                        <AuthInitializer>
                          <Exams />
                        </AuthInitializer>
                      }
                    />
                    <Route
                      path="/exercises/:examId"
                      element={
                        <AuthInitializer>
                          <ExerciseDetail />
                        </AuthInitializer>
                      }
                    />
                    <Route
                      path="/attempt/:attemptId"
                      element={
                        <AuthInitializer>
                          <Attempt />
                        </AuthInitializer>
                      }
                    />
                    <Route
                      path="/attempt/:attemptId/results"
                      element={
                        <AuthInitializer>
                          <AttemptResults />
                        </AuthInitializer>
                      }
                    />
                    <Route
                      path="/subjects/:subjectId/exercises"
                      element={
                        <AuthInitializer>
                          <ExerciseListBySubject />
                        </AuthInitializer>
                      }
                    />
                    <Route
                      path="/attendance"
                      element={
                        <AuthInitializer>
                          <Attendance />
                        </AuthInitializer>
                      }
                    />
                    <Route
                      path="/directories"
                      element={
                        <AuthInitializer>
                          <Directory />
                        </AuthInitializer>
                      }
                    />
                    <Route
                      path="/news"
                      element={
                        <AuthInitializer>
                          <AllNews />
                        </AuthInitializer>
                      }
                    />
                    <Route
                      path="/news/:id"
                      element={
                        <AuthInitializer>
                          <NewsDetail />
                        </AuthInitializer>
                      }
                    />
                    <Route
                      path="/teacher-classes"
                      element={
                        <AuthInitializer>
                          <AllTeacherClasses />
                        </AuthInitializer>
                      }
                    />
                    <Route
                      path="/teacher-schedule"
                      element={
                        <AuthInitializer>
                          <TeacherSchedule />
                        </AuthInitializer>
                      }
                    />
                    <Route
                      path="/student-leave-request"
                      element={
                        <AuthInitializer>
                          <StudentLeaveRequest />
                        </AuthInitializer>
                      }
                    />
                    <Route
                      path="/teacher-leave-request"
                      element={
                        <AuthInitializer>
                          <TeacherLeaveRequest />
                        </AuthInitializer>
                      }
                    />
                    <Route
                      path="/leave-request-approval"
                      element={
                        <AuthInitializer>
                          <LeaveRequestApproval />
                        </AuthInitializer>
                      }
                    />
                    <Route
                      path="/my-leave-requests"
                      element={
                        <AuthInitializer>
                          <MyLeaveRequests />
                        </AuthInitializer>
                      }
                    />
                    <Route
                      path="/teacher-grading"
                      element={
                        <AuthInitializer>
                          <TeacherGrading />
                        </AuthInitializer>
                      }
                    />
                    <Route
                      path="/exam-results/:examId"
                      element={
                        <AuthInitializer>
                          <ExamResults />
                        </AuthInitializer>
                      }
                    />
                    <Route
                      path="/student-attempts/:studentId"
                      element={
                        <AuthInitializer>
                          <StudentAttempts />
                        </AuthInitializer>
                      }
                    />
                    <Route
                      path="/student-attendance-details/:studentId"
                      element={
                        <AuthInitializer>
                          <StudentAttendanceDetails />
                        </AuthInitializer>
                      }
                    />
                    <Route
                      path="/announcement-detail"
                      element={
                        <AuthInitializer>
                          <AnnouncementDetail />
                        </AuthInitializer>
                      }
                    />
                    <Route
                      path="/exam-supervisions"
                      element={
                        <AuthInitializer>
                          <ExamSupervisionList />
                        </AuthInitializer>
                      }
                    />
                    <Route
                      path="/exam-supervisions/create"
                      element={
                        <AuthInitializer>
                          <ExamSupervisionForm />
                        </AuthInitializer>
                      }
                    />
                    <Route
                      path="/exam-supervisions/edit/:id"
                      element={
                        <AuthInitializer>
                          <EditExamSupervision />
                        </AuthInitializer>
                      }
                    />
                    <Route
                      path="/exam-change-requests"
                      element={
                        <AuthInitializer>
                          <ExamChangeRequests />
                        </AuthInitializer>
                      }
                    />
                    <Route
                      path="/exam-change-requests/create"
                      element={
                        <AuthInitializer>
                          <ExamChangeRequestForm />
                        </AuthInitializer>
                      }
                    />
                    <Route
                      path="/teacher-exam-schedule"
                      element={
                        <AuthInitializer>
                          <TeacherExamSchedule />
                        </AuthInitializer>
                      }
                    />
                    <Route
                      path="/my-exam-change-requests"
                      element={
                        <AuthInitializer>
                          <MyExamChangeRequests />
                        </AuthInitializer>
                      }
                    />
                    {/* Violation Management Routes */}
                    <Route
                      path="/my-violations"
                      element={
                        <AuthInitializer>
                          <MyViolations />
                        </AuthInitializer>
                      }
                    />
                    <Route
                      path="/conduct-points"
                      element={
                        <AuthInitializer>
                          <ConductPoints />
                        </AuthInitializer>
                      }
                    />
                    <Route
                      path="/create-violation"
                      element={
                        <AuthInitializer>
                          <CreateViolation />
                        </AuthInitializer>
                      }
                    />
                    <Route
                      path="/violations-list"
                      element={
                        <AuthInitializer>
                          <ViolationsList />
                        </AuthInitializer>
                      }
                    />
                    <Route
                      path="/violations-stats"
                      element={
                        <AuthInitializer>
                          <ViolationsStats />
                        </AuthInitializer>
                      }
                    />
                    <Route
                      path="/conduct-management"
                      element={
                        <AuthInitializer>
                          <ConductManagement />
                        </AuthInitializer>
                      }
                    />
                  </AnimationRoutes>
                </SwipeWrapper>
              </NotificationProvider>
            </SnackbarProvider>
          </SchoolYearProvider>
        </AuthProvider>
      </ZMPRouter>
    </App>
  );
};

export default Layout;
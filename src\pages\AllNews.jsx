import React, { useEffect, useState, useRef, useContext } from 'react';
import { Box, Text, List, useNavigate } from 'zmp-ui';
import HeaderEdu from '../components/HeaderEdu';
import HeaderSpacer from '../components/utils/HeaderSpacer';
import BottomNavigationEdu from '../components/BottomNavigationEdu';
import LoadingIndicator from '../components/utils/LoadingIndicator';
import { AuthContext } from '../context/AuthContext';
import { authApi } from '../utils/api';
import { formatDistanceToNow } from 'date-fns';
import vi from 'date-fns/locale/vi';
import { ICONS } from '@/constants/icons';
const AllNews = () => {
    const navigate = useNavigate();
    const { user, loading: authLoading } = useContext(AuthContext);
    const [news, setNews] = useState([]);
    const [loading, setLoading] = useState(true);
    const [page, setPage] = useState(1);
    const [totalPages, setTotalPages] = useState(1);
    const observerRef = useRef(null);

    // Kiểm tra user và redirect nếu chưa đăng nhập
    useEffect(() => {
        if (!authLoading && !user) {
            navigate('/login', { replace: true });
        }
    }, [user, authLoading, navigate]);

    // Lấy danh sách tin tức
    useEffect(() => {
        setLoading(true);
        authApi
            .get(`/news?page=${page}&limit=10`)
            .then((response) => {
                if (page === 1) {
                    setNews(response.data.news);
                } else {
                    setNews((prev) => [...prev, ...response.data.news]);
                }
                setTotalPages(response.data.totalPages);
                setLoading(false);
            })
            .catch((err) => {
                console.log('Error fetching news:', err);
                setLoading(false);
            });
    }, [page]);

    // Format date
    const formatNewsDate = (dateString) => {
        try {
            return formatDistanceToNow(new Date(dateString), { locale: vi, addSuffix: true });
        } catch (error) {
            return 'Không xác định';
        }
    };

    // Infinite scroll
    useEffect(() => {
        const observer = new IntersectionObserver(
            (entries) => {
                if (entries[0].isIntersecting && page < totalPages && !loading) {
                    setPage((prev) => prev + 1);
                }
            },
            { threshold: 0.1 }
        );

        if (observerRef.current) {
            observer.observe(observerRef.current);
        }

        return () => {
            if (observerRef.current) {
                observer.unobserve(observerRef.current);
            }
        };
    }, [page, totalPages, loading]);

    return (
        <Box className="container" style={{ minHeight: '100vh', display: 'flex', flexDirection: 'column', paddingBottom: '60px' }}>
            <HeaderEdu onBackClick={() => navigate(-1)} showBackButton={true} />
            <HeaderSpacer />

            {authLoading ? (
                <Text>Đang tải thông tin...</Text>
            ) : (
                <Box className="page-container" style={{ padding: '15px', display: 'flex', flexDirection: 'column', gap: '15px' }}>
                    <Text bold size="xLarge" style={{ marginBottom: '15px' }}>
                        Tất cả tin tức
                    </Text>
                    <List style={{ display: 'flex', flexDirection: 'column', gap: '15px' }}>
                        {news.length > 0 ? (
                            news.map((item) => (
                                <Box
                                    key={item._id}
                                    style={{
                                        backgroundColor: 'white',
                                        borderRadius: '8px',
                                        overflow: 'hidden',
                                        boxShadow: '0 2px 4px rgba(0, 0, 0, 0.1)',
                                        cursor: 'pointer'
                                    }}
                                    onClick={() => navigate(`/news/${item._id}`, {
                                        state: {
                                            previousPath: '/news',
                                            previousState: { tab: 'news' }
                                        }
                                    })}
                                >
                                    {item.image && (
                                        <Box style={{ width: '100%', height: '150px', overflow: 'hidden' }}>
                                            <img
                                                src={item.image}
                                                alt={item.title}
                                                style={{
                                                    width: '100%',
                                                    height: '100%',
                                                    objectFit: 'cover'
                                                }}
                                            />
                                        </Box>
                                    )}
                                    <Box style={{ padding: '12px' }}>
                                        <Text
                                            bold
                                            style={{
                                                marginBottom: '5px',
                                                fontSize: '16px',
                                                display: '-webkit-box',
                                                WebkitLineClamp: 2,
                                                WebkitBoxOrient: 'vertical',
                                                overflow: 'hidden',
                                                textOverflow: 'ellipsis',
                                                maxHeight: '48px',
                                                wordBreak: 'break-word',
                                                whiteSpace: 'normal'
                                            }}
                                        >
                                            {item.title}
                                        </Text>
                                        <Box style={{ display: 'flex', justifyContent: 'space-between', alignItems: 'center' }}>
                                            <Text style={{ fontSize: '12px', color: '#666' }}>
                                                {formatNewsDate(item.createdAt)} • {item.categoryLabel || 'Tin tức chung'}
                                            </Text>
                                            <Box style={{ display: 'flex', alignItems: 'center', gap: '4px' }}>
                                                <Text style={{ fontSize: '12px' }}>{ICONS.EYE}</Text>
                                                <Text style={{ fontSize: '12px', color: '#666' }}>
                                                    {item.views || 0}
                                                </Text>
                                            </Box>
                                        </Box>
                                    </Box>
                                </Box>
                            ))
                        ) : (
                            <Text>Chưa có tin tức</Text>
                        )}
                    </List>
                    {loading && <LoadingIndicator />}
                    <div ref={observerRef} style={{ height: '20px' }} />
                </Box>
            )}

            <BottomNavigationEdu />
        </Box>
    );
};

export default AllNews;

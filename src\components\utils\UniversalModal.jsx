import React, { useEffect, useState } from 'react';
import { Modal, Box, Text, Button } from 'zmp-ui';
import { ICONS } from '../../constants/icons';

const UniversalModal = ({ 
    visible, 
    options = {}, 
    onClose 
}) => {
    const {
        type = 'confirm',
        title = '',
        message = '',
        confirmText = 'Xác nhận',
        cancelText = 'Hủy',
        showCancel = true,
        confirmDanger = false,
        onConfirm = () => {},
        onCancel = () => {},
        icon = null,
        loading = false
    } = options;

    // Tạo actions array
    const actions = [];

    // Thêm nút Cancel nếu cần
    if (showCancel && cancelText) {
        actions.push({
            text: cancelText,
            close: false, // Không để Modal tự close
            onClick: () => {
                onCancel();
                onClose();
            },
            style: {
                backgroundColor: '#f5f5f5',
                color: '#333'
            }
        });
    }

    // Thêm nút Confirm
    actions.push({
        text: loading ? '<PERSON><PERSON> xử lý...' : confirmText,
        close: false, // Không để Modal tự close
        onClick: async () => {
            try {
                await onConfirm();
                onClose(); // Chỉ close khi hoàn tất
            } catch (error) {
                console.error('Error in onConfirm:', error);
                onClose(); // Close kể cả khi có lỗi
            }
        },
        danger: confirmDanger,
        disabled: loading,
        style: {
            backgroundColor: confirmDanger ? '#f44336' : '#0068ff',
            color: 'white'
        }
    });

    // Xác định icon hiển thị
    const getDisplayIcon = () => {
        if (icon) return icon;
        
        // Auto icon based on title or type
        if (title.toLowerCase().includes('thành công')) return ICONS.SUCCESS;
        if (title.toLowerCase().includes('lỗi')) return ICONS.ERROR;
        if (title.toLowerCase().includes('cảnh báo')) return ICONS.WARNING;
        if (title.toLowerCase().includes('xóa')) return ICONS.DELETE;
        if (title.toLowerCase().includes('xác nhận')) return ICONS.QUESTION;
        if (type === 'alert') return ICONS.INFO;
        
        return null;
    };

    const displayIcon = getDisplayIcon();

    return (
        <Modal
            visible={visible}
            title={title}
            actions={actions}
        >
            <Box style={{ padding: '20px', textAlign: 'center' }}>
                {displayIcon && (
                    <Text style={{ 
                        fontSize: '48px', 
                        marginBottom: '20px',
                        display: 'block'
                    }}>
                        {displayIcon}
                    </Text>
                )}
                <Text style={{ 
                    lineHeight: 1.5, 
                    fontSize: '16px',
                    color: '#333'
                }}>
                    {message}
                </Text>
            </Box>
        </Modal>
    );
};

export default UniversalModal; 
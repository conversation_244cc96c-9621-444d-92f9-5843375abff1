import axios from 'axios';
import config from '../config/config';

// Instance cho Authorization: Bearer
const api = axios.create({
  baseURL: config.apiBaseUrl,
});

api.interceptors.request.use((config) => {
  const token = localStorage.getItem('token');
  if (token) {
    config.headers['Authorization'] = `Bear<PERSON> ${token}`;
  }

  // Add school year parameter to relevant endpoints
  const selectedSchoolYear = localStorage.getItem('selectedSchoolYear');
  if (selectedSchoolYear && config.url) {
    // List of endpoints that should include school year parameter
    const schoolYearEndpoints = [
      '/grades/',
      '/schedules/',
      '/attendance/statistics/',
      '/exams/',
      '/directory/classes',
      '/directory/teacher/classes',
      '/violations/',
      '/violations/stats',
      '/violations/conduct/'
    ];

    // Check if this endpoint should include school year
    const shouldIncludeSchoolYear = schoolYearEndpoints.some(endpoint =>
      config.url.includes(endpoint)
    );

    if (shouldIncludeSchoolYear) {
      // Add school year parameter to URL
      const separator = config.url.includes('?') ? '&' : '?';
      if (!config.url.includes('schoolYear=')) {
        config.url += `${separator}schoolYear=${selectedSchoolYear}`;
      }
    }
  }

  return config;
});

// Instance cho x-auth-token
const authApi = axios.create({
  baseURL: config.apiBaseUrl,
});

authApi.interceptors.request.use((config) => {
  const token = localStorage.getItem('token');
  if (token) {
    config.headers['x-auth-token'] = token;
  }

  // Add school year parameter to relevant endpoints
  const selectedSchoolYear = localStorage.getItem('selectedSchoolYear');
  if (selectedSchoolYear && config.url) {
    // List of endpoints that should include school year parameter
    const schoolYearEndpoints = [
      '/grades/',
      '/schedules/',
      '/attendance/statistics/',
      '/exams/',
      '/directory/classes',
      '/directory/teacher/classes',
      '/violations/',
      '/violations/stats',
      '/violations/conduct/'
    ];

    // Check if this endpoint should include school year
    const shouldIncludeSchoolYear = schoolYearEndpoints.some(endpoint =>
      config.url.includes(endpoint)
    );

    if (shouldIncludeSchoolYear) {
      // Add school year parameter to URL
      const separator = config.url.includes('?') ? '&' : '?';
      if (!config.url.includes('schoolYear=')) {
        config.url += `${separator}schoolYear=${selectedSchoolYear}`;
      }
    }
  }

  return config;
});

export { api, authApi };
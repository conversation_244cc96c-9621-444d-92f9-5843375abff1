import React, { useState, useEffect, useContext } from 'react';
import { Box, Text, useNavigate } from 'zmp-ui';
import HeaderEdu from '../../components/HeaderEdu';
import HeaderSpacer from '../../components/utils/HeaderSpacer';
import BottomNavigationEdu from '../../components/BottomNavigationEdu';
import { AuthContext } from '../../context/AuthContext';
import { authApi } from '../../utils/api';
import { ICONS } from '../../constants/icons';
import Loading from '../../components/utils/Loading';
import { formatDistanceToNow } from 'date-fns';
import vi from 'date-fns/locale/vi';
import { useSchoolYear } from '../../context/SchoolYearContext';

const ConductPoints = () => {
    const navigate = useNavigate();
    const { user } = useContext(AuthContext);
    const { selectedSchoolYear } = useSchoolYear();
    const [conductData, setConductData] = useState(null);
    const [loading, setLoading] = useState(true);

    // Fetch conduct points
    const fetchConductPoints = async () => {
        try {
            setLoading(true);
            const response = await authApi.get(`/violations/conduct/${user._id}?schoolYear=${selectedSchoolYear || '2024-2025'}`);
            console.log('Conduct API Response:', response.data); // Debug log
            if (response.data?.success && response.data?.data) {
                setConductData(response.data.data);
            } else {
                setConductData(null);
            }
        } catch (error) {
            console.error('Error fetching conduct points:', error);
            setConductData(null);
        } finally {
            setLoading(false);
        }
    };

    useEffect(() => {
        if (user?._id) {
            fetchConductPoints();
        }
    }, [user, selectedSchoolYear]);

    // Get classification info
    const getClassificationInfo = (classification) => {
        const classMap = {
            'excellent': { label: 'Xuất sắc', color: '#28a745', icon: '🏆', bg: '#d4edda' },
            'good': { label: 'Tốt', color: '#17a2b8', icon: '⭐', bg: '#d1ecf1' },
            'fair': { label: 'Khá', color: '#ffc107', icon: '👍', bg: '#fff3cd' },
            'average': { label: 'Trung bình', color: '#fd7e14', icon: '👌', bg: '#ffeaa7' },
            'weak': { label: 'Yếu', color: '#dc3545', icon: '👎', bg: '#f8d7da' }
        };
        return classMap[classification] || { label: classification, color: '#6c757d', icon: '❓', bg: '#f8f9fa' };
    };

    // Get points color
    const getPointsColor = (points) => {
        if (points >= 80) return '#28a745';
        if (points >= 65) return '#17a2b8';
        if (points >= 50) return '#ffc107';
        if (points >= 35) return '#fd7e14';
        return '#dc3545';
    };

    if (loading) {
        return <Loading />;
    }

    if (!conductData) {
        return (
            <Box className="container" style={{ minHeight: '100vh', display: 'flex', flexDirection: 'column', paddingBottom: '60px', backgroundColor: '#f5f5f5' }}>
                <HeaderEdu 
                    title="Điểm thi đua"
                    showBackButton={true}
                    onBackClick={() => navigate('/student')}
                />
                <HeaderSpacer />
                <Box style={{ flex: 1, padding: '15px', textAlign: 'center' }}>
                    <Text style={{ fontSize: '48px', marginBottom: '15px' }}>{ICONS.INFO}</Text>
                    <Text bold style={{ fontSize: '18px', marginBottom: '10px' }}>
                        Chưa có dữ liệu điểm thi đua
                    </Text>
                    <Text style={{ color: '#666', fontSize: '14px' }}>
                        Dữ liệu điểm thi đua sẽ được cập nhật sau khi có vi phạm hoặc khen thưởng.
                    </Text>
                </Box>
                <BottomNavigationEdu />
            </Box>
        );
    }

    const classificationInfo = getClassificationInfo(conductData.currentClassification);

    return (
        <Box className="container" style={{ minHeight: '100vh', display: 'flex', flexDirection: 'column', paddingBottom: '60px', backgroundColor: '#f5f5f5' }}>
            <HeaderEdu 
                title="Điểm thi đua"
                showBackButton={true}
                onBackClick={() => navigate('/student')}
            />
            <HeaderSpacer />

            <Box style={{ flex: 1, padding: '15px' }}>
                {/* Current Points Card */}
                <Box style={{
                    backgroundColor: 'white',
                    borderRadius: '15px',
                    padding: '20px',
                    marginBottom: '20px',
                    boxShadow: '0 2px 10px rgba(0,0,0,0.1)',
                    textAlign: 'center'
                }}>
                    <Text style={{ fontSize: '14px', color: '#666', marginBottom: '10px' }}>
                        Điểm thi đua hiện tại
                    </Text>
                    <Text style={{ 
                        fontSize: '48px', 
                        fontWeight: 'bold', 
                        color: getPointsColor(conductData.currentPoints),
                        marginBottom: '10px'
                    }}>
                        {conductData.currentPoints}
                    </Text>
                    <Box style={{
                        display: 'inline-flex',
                        alignItems: 'center',
                        padding: '8px 16px',
                        borderRadius: '20px',
                        backgroundColor: classificationInfo.bg,
                        border: `2px solid ${classificationInfo.color}`
                    }}>
                        <Text style={{ fontSize: '20px', marginRight: '8px' }}>
                            {classificationInfo.icon}
                        </Text>
                        <Text style={{ 
                            fontSize: '16px', 
                            fontWeight: 'bold', 
                            color: classificationInfo.color 
                        }}>
                            {classificationInfo.label}
                        </Text>
                    </Box>
                </Box>

                {/* Statistics Cards */}
                <Box style={{ display: 'grid', gridTemplateColumns: '1fr 1fr', gap: '15px', marginBottom: '20px' }}>
                    <Box style={{
                        backgroundColor: 'white',
                        borderRadius: '12px',
                        padding: '15px',
                        textAlign: 'center',
                        boxShadow: '0 2px 8px rgba(0,0,0,0.1)'
                    }}>
                        <Text style={{ fontSize: '24px', marginBottom: '5px' }}>{ICONS.VIOLATION}</Text>
                        <Text style={{ fontSize: '20px', fontWeight: 'bold', color: '#dc3545', marginBottom: '5px' }}>
                            {conductData.totalViolations}
                        </Text>
                        <Text style={{ fontSize: '12px', color: '#666' }}>
                            Tổng vi phạm
                        </Text>
                    </Box>

                    <Box style={{
                        backgroundColor: 'white',
                        borderRadius: '12px',
                        padding: '15px',
                        textAlign: 'center',
                        boxShadow: '0 2px 8px rgba(0,0,0,0.1)'
                    }}>
                        <Text style={{ fontSize: '24px', marginBottom: '5px' }}>{ICONS.CALENDAR}</Text>
                        <Text style={{ fontSize: '20px', fontWeight: 'bold', color: '#0068ff', marginBottom: '5px' }}>
                            {conductData.schoolYear}
                        </Text>
                        <Text style={{ fontSize: '12px', color: '#666' }}>
                            Năm học
                        </Text>
                    </Box>
                </Box>

                {/* Point History */}
                <Box style={{
                    backgroundColor: 'white',
                    borderRadius: '12px',
                    padding: '20px',
                    boxShadow: '0 2px 8px rgba(0,0,0,0.1)'
                }}>
                    <Text bold style={{ fontSize: '16px', marginBottom: '15px', color: '#333' }}>
                        {ICONS.STATS} Lịch sử thay đổi điểm
                    </Text>

                    {conductData.pointHistory && conductData.pointHistory.length > 0 ? (
                        <Box style={{ maxHeight: '300px', overflowY: 'auto' }}>
                            {conductData.pointHistory.map((history, index) => (
                                <Box key={index} style={{
                                    display: 'flex',
                                    justifyContent: 'space-between',
                                    alignItems: 'center',
                                    padding: '12px 0',
                                    borderBottom: index < conductData.pointHistory.length - 1 ? '1px solid #eee' : 'none'
                                }}>
                                    <Box style={{ flex: 1 }}>
                                        <Text style={{ fontSize: '14px', fontWeight: 'bold', marginBottom: '4px' }}>
                                            {history.reason}
                                        </Text>
                                        <Text style={{ fontSize: '12px', color: '#666' }}>
                                            {formatDistanceToNow(new Date(history.date), { 
                                                addSuffix: true, 
                                                locale: vi 
                                            })}
                                        </Text>
                                    </Box>
                                    <Box style={{ textAlign: 'right' }}>
                                        <Text style={{
                                            fontSize: '14px',
                                            fontWeight: 'bold',
                                            color: history.points > 0 ? '#28a745' : '#dc3545'
                                        }}>
                                            {history.points > 0 ? '+' : ''}{history.points}
                                        </Text>
                                        <Text style={{ fontSize: '12px', color: '#666' }}>
                                            {history.previousPoints} → {history.newPoints}
                                        </Text>
                                    </Box>
                                </Box>
                            ))}
                        </Box>
                    ) : (
                        <Box style={{ textAlign: 'center', padding: '20px' }}>
                            <Text style={{ fontSize: '32px', marginBottom: '10px' }}>{ICONS.INFO}</Text>
                            <Text style={{ color: '#666', fontSize: '14px' }}>
                                Chưa có lịch sử thay đổi điểm
                            </Text>
                        </Box>
                    )}
                </Box>

                {/* Info Card */}
                <Box style={{
                    backgroundColor: '#f8f9fa',
                    borderRadius: '12px',
                    padding: '15px',
                    marginTop: '20px',
                    border: '1px solid #dee2e6'
                }}>
                    <Text style={{ fontSize: '14px', color: '#666', textAlign: 'center' }}>
                        {ICONS.TIP} <Text bold>Thang điểm thi đua:</Text> Xuất sắc (80-100), Tốt (65-79), Khá (50-64), Trung bình (35-49), Yếu (0-34)
                    </Text>
                </Box>
            </Box>

            <BottomNavigationEdu />
        </Box>
    );
};

export default ConductPoints;

import React, { useState, useEffect, useContext } from 'react';
import { Box, Text, Button, Select, Input, useNavigate } from 'zmp-ui';
import { AuthContext } from '../../context/AuthContext';
import { authApi } from '../../utils/api';
import HeaderEdu from '../../components/HeaderEdu';
import HeaderSpacer from '../../components/utils/HeaderSpacer';
import BottomNavigationEdu from '../../components/BottomNavigationEdu';
import LoadingIndicator from '../../components/utils/LoadingIndicator';
import TeacherSelector from '../../components/utils/TeacherSelector';
import useNotification from '../../hooks/useNotification';
import { EXAM_TYPES, TIME_SLOTS } from '../../constants/exam';
import ZaloNotificationToggle from '../../components/utils/ZaloNotificationToggle';
import { formatExamSupervisionMessage } from '../../components/utils/ZaloMessageFormatter';
import useAnnouncement from '../../hooks/useAnnouncement';

const { Option } = Select;

const ExamSupervisionForm = ({ isEdit = false, supervisionId = null }) => {
    const navigate = useNavigate();
    const { user } = useContext(AuthContext);
    const notification = useNotification();
    const { sendCustomNotification } = useAnnouncement();
    const [loading, setLoading] = useState(false);
    const [formData, setFormData] = useState({
        examType: '',
        schoolYear: '',
        teacher: '',
        sessions: [{ date: '', timeSlot: '', room: '', subject: '' }],
        notes: ''
    });
    const [selectedTeacherData, setSelectedTeacherData] = useState(null);
    const [sendZaloNotification, setSendZaloNotification] = useState(false);

    // Fetch supervision details if editing
    const fetchSupervisionDetails = async () => {
        if (!isEdit || !supervisionId) return;
        
        setLoading(true);
        try {
            const response = await authApi.get(`/exam-supervisions/${supervisionId}`);
            if (response.data) {
                const supervision = response.data;
                setFormData({
                    examType: supervision.examType,
                    schoolYear: supervision.schoolYear,
                    teacher: supervision.teacher._id,
                    sessions: supervision.sessions.map(session => ({
                        _id: session._id,
                        date: new Date(session.date).toISOString().split('T')[0],
                        timeSlot: session.timeSlot,
                        room: session.room,
                        subject: session.subject,
                        status: session.status
                    })),
                    notes: supervision.notes || ''
                });
            }
        } catch (error) {
            console.error('Error fetching supervision details:', error);
            notification.showError('Lỗi', 'Không thể tải thông tin phân công coi thi');
        } finally {
            setLoading(false);
        }
    };

    useEffect(() => {
        if (isEdit) {
            fetchSupervisionDetails();
        }
    }, [isEdit, supervisionId]);

    const handleInputChange = (field, value) => {
        setFormData(prev => ({
            ...prev,
            [field]: value
        }));
    };

    const handleSessionChange = (index, field, value) => {
        setFormData(prev => ({
            ...prev,
            sessions: prev.sessions.map((session, i) => 
                i === index ? { ...session, [field]: value } : session
            )
        }));
    };

    const addSession = () => {
        setFormData(prev => ({
            ...prev,
            sessions: [...prev.sessions, { date: '', timeSlot: '', room: '', subject: '' }]
        }));
    };

    const removeSession = (index) => {
        if (formData.sessions.length <= 1) {
            notification.showError('Lỗi', 'Phải có ít nhất một buổi thi');
            return;
        }

        setFormData(prev => ({
            ...prev,
            sessions: prev.sessions.filter((_, i) => i !== index)
        }));
    };

    const validateForm = () => {
        const errors = [];

        if (!formData.examType) {
            errors.push('Vui lòng chọn loại kỳ thi');
        }
        if (!formData.schoolYear) {
            errors.push('Vui lòng chọn năm học');
        }
        if (!formData.teacher) {
            errors.push('Vui lòng chọn giáo viên');
        }
        if (formData.sessions.length === 0) {
            errors.push('Vui lòng thêm ít nhất một buổi thi');
        }

        formData.sessions.forEach((session, index) => {
            if (!session.date) {
                errors.push(`Vui lòng chọn ngày thi cho buổi thi thứ ${index + 1}`);
            }
            if (!session.timeSlot) {
                errors.push(`Vui lòng chọn ca thi cho buổi thi thứ ${index + 1}`);
            }
        });

        if (errors.length > 0) {
            notification.showError('Lỗi', errors.join('\n'));
            return false;
        }
        return true;
    };

    // Fetch teacher data when selected
    const handleTeacherChange = async (teacherId) => {
        handleInputChange('teacher', teacherId);
        if (teacherId) {
            try {
                const response = await authApi.get(`/directory/user/${teacherId}`);
                setSelectedTeacherData(response.data.data);
            } catch (error) {
                console.error('Error fetching teacher data:', error);
                setSelectedTeacherData(null);
            }
        } else {
            setSelectedTeacherData(null);
        }
    };

    const handleSubmit = async () => {
        if (!validateForm()) return;

        setLoading(true);
        try {
            const payload = {
                examType: formData.examType,
                schoolYear: formData.schoolYear,
                teacher: formData.teacher,
                sessions: formData.sessions.map(session => ({
                    _id: session._id,
                    date: session.date,
                    timeSlot: session.timeSlot,
                    room: session.room,
                    subject: session.subject
                })),
                notes: formData.notes
            };

            let supervisionResponse;
            if (isEdit) {
                supervisionResponse = await authApi.put(`/exam-supervisions/${supervisionId}`, payload);
                notification.showSuccess('Thành công', 'Đã cập nhật phân công coi thi');
            } else {
                supervisionResponse = await authApi.post('/exam-supervisions', payload);
                notification.showSuccess('Thành công', 'Đã tạo phân công coi thi mới');
            }
            navigate('/exam-supervisions');

            // Create message once for both notifications
            const message = formatExamSupervisionMessage({
                examType: formData.examType,
                schoolYear: formData.schoolYear,
                sessions: formData.sessions,
                notes: formData.notes
            });

            // Send announcement notification (always)
            if (selectedTeacherData) {
                try {
                    await sendCustomNotification({
                        title: isEdit ? 'Lịch coi thi đã được cập nhật' : 'Phân công coi thi mới',
                        content: message,
                        users: [selectedTeacherData]
                    });
                } catch (announcementError) {
                    console.error('Error sending announcement notification:', announcementError);
                    // Don't show error to user for announcement failure
                }
            }

            // Send Zalo notification if enabled and teacher has Zalo ID
            if (sendZaloNotification && selectedTeacherData?.zaloId) {
                try {
                    await authApi.post('/zalo/send-user-message', {
                        userId: selectedTeacherData.zaloId,
                        message: message
                    });
                } catch (zaloError) {
                    console.error('Error sending Zalo notification:', zaloError);
                    notification.error('Không thể gửi thông báo qua Zalo');
                }
            }
        } catch (error) {
            console.error('Error saving supervision:', error);
            notification.showError('Lỗi', 'Không thể lưu phân công coi thi');
        } finally {
            setLoading(false);
        }
    };

    if (loading && isEdit) {
        return (
            <Box className="container" style={{ minHeight: '100vh', display: 'flex', flexDirection: 'column', paddingBottom: '60px', backgroundColor: '#f5f5f5' }}>
                <HeaderEdu title="Chỉnh sửa công coi thi" showBackButton={true} onBackClick={() => navigate(-1)} />
                <HeaderSpacer />
                <Box style={{ flex: 1, display: 'flex', justifyContent: 'center', alignItems: 'center' }}>
                    <LoadingIndicator />
                </Box>
            </Box>
        );
    }

    return (
        <Box className="container" style={{ minHeight: '100vh', display: 'flex', flexDirection: 'column', paddingBottom: '60px', backgroundColor: '#f5f5f5' }}>
            <HeaderEdu title={isEdit ? 'Chỉnh sửa phân công coi thi' : 'Tạo phân công coi thi mới'} showBackButton={true} onBackClick={() => navigate(-1)} />
            <HeaderSpacer />

            <Box className="form" style={{ padding: '15px', backgroundColor: 'white' }}>
                {/* Basic Information */}
                <Box style={{ marginBottom: '20px' }}>
                    <Text style={{ marginBottom: '5px' }}>
                        Loại kỳ thi <Text style={{ color: 'red' }}>*</Text>
                    </Text>
                    <Select
                        value={formData.examType}
                        onChange={(value) => handleInputChange('examType', value)}
                        style={{ marginBottom: '15px' }}
                    >
                        {Object.entries(EXAM_TYPES).map(([key, label]) => (
                            <Option key={key} value={key} title={label} />
                        ))}
                    </Select>

                    <Text style={{ marginBottom: '5px' }}>
                        Năm học <Text style={{ color: 'red' }}>*</Text>
                    </Text>
                    <Select
                        value={formData.schoolYear}
                        onChange={(value) => handleInputChange('schoolYear', value)}
                        style={{ marginBottom: '15px' }}
                    >
                        <Option value="2023-2024" title="2023-2024" />
                        <Option value="2024-2025" title="2024-2025" />
                    </Select>

                    {/* Teacher Selection with updated onChange */}
                    <TeacherSelector
                        value={formData.teacher}
                        onChange={handleTeacherChange}
                        label="Chọn giáo viên"
                        required={true}
                        style={{ marginBottom: '15px' }}
                    />
                </Box>

                {/* Sessions */}
                <Box style={{ marginBottom: '20px' }}>
                    <Box style={{ display: 'flex', justifyContent: 'space-between', alignItems: 'center', marginBottom: '10px' }}>
                        <Text bold>
                            Buổi thi <Text style={{ color: 'red' }}>*</Text>
                        </Text>
                        <Button size="small" onClick={addSession}>Thêm buổi thi</Button>
                    </Box>

                    {formData.sessions.map((session, index) => (
                        <Box
                            key={index}
                            style={{
                                padding: '15px',
                                border: '1px solid #e0e0e0',
                                borderRadius: '8px',
                                marginBottom: '10px',
                                backgroundColor: '#f9f9f9'
                            }}
                        >
                            <Box style={{ display: 'flex', justifyContent: 'space-between', marginBottom: '10px' }}>
                                <Text bold>Buổi thi {index + 1}</Text>
                                <Button size="small" danger onClick={() => removeSession(index)}>Xóa</Button>
                            </Box>

                            <Box style={{ display: 'grid', gridTemplateColumns: 'repeat(2, 1fr)', gap: '10px' }}>
                                <Box>
                                    <Text style={{ marginBottom: '5px' }}>
                                        Ngày thi <Text style={{ color: 'red' }}>*</Text>
                                    </Text>
                                    <Input
                                        type="date"
                                        value={session.date}
                                        onChange={(e) => handleSessionChange(index, 'date', e.target.value)}
                                    />
                                </Box>

                                <Box>
                                    <Text style={{ marginBottom: '5px' }}>
                                        Ca thi <Text style={{ color: 'red' }}>*</Text>
                                    </Text>
                                    <Select
                                        value={session.timeSlot}
                                        onChange={(value) => handleSessionChange(index, 'timeSlot', value)}
                                    >
                                        {Object.entries(TIME_SLOTS).map(([key, label]) => (
                                            <Option key={key} value={key} title={label} />
                                        ))}
                                    </Select>
                                </Box>

                                <Box>
                                    <Text style={{ marginBottom: '5px' }}>Phòng thi</Text>
                                    <Input
                                        value={session.room}
                                        onChange={(e) => handleSessionChange(index, 'room', e.target.value)}
                                    />
                                </Box>

                                <Box>
                                    <Text style={{ marginBottom: '5px' }}>Môn thi</Text>
                                    <Input
                                        value={session.subject}
                                        onChange={(e) => handleSessionChange(index, 'subject', e.target.value)}
                                    />
                                </Box>
                            </Box>
                        </Box>
                    ))}
                </Box>

                {/* Notes */}
                <Box style={{ marginBottom: '20px' }}>
                    <Text style={{ marginBottom: '5px' }}>Ghi chú</Text>
                    <textarea
                        value={formData.notes}
                        onChange={(e) => handleInputChange('notes', e.target.value)}
                        style={{
                            width: '100%',
                            minHeight: '100px',
                            padding: '10px',
                            border: '1px solid #e0e0e0',
                            borderRadius: '8px',
                            resize: 'vertical'
                        }}
                    />
                </Box>

                {/* Zalo Notification Toggle */}
                <Box style={{ marginBottom: '20px' }}>
                    <ZaloNotificationToggle
                        checked={sendZaloNotification}
                        onChange={setSendZaloNotification}
                        hasZaloId={selectedTeacherData?.zaloId}
                    />
                </Box>

                {/* Action Buttons */}
                <Box style={{ display: 'flex', gap: '10px', justifyContent: 'flex-end' }}>
                    <Button onClick={() => navigate('/exam-supervisions')}>Hủy</Button>
                    <Button primary onClick={handleSubmit} loading={loading}>
                        {isEdit ? 'Cập nhật' : 'Tạo mới'}
                    </Button>
                </Box>
            </Box>

            <BottomNavigationEdu />
        </Box>
    );
};

export default ExamSupervisionForm; 
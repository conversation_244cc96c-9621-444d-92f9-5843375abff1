import React, { useEffect, useState, useContext } from 'react';
import { useNavigate, Box, Text, Button, SnackbarProvider } from 'zmp-ui';
import HeaderEdu from '../components/HeaderEdu';
import BottomNavigation from '../components/BottomNavigationEdu';
import Loading from '../components/utils/Loading';
import { AuthContext } from '../context/AuthContext';
import { parseJwt } from '../utils/jwt';
import { authApi } from '../utils/api';
import HeaderSpacer from '../components/utils/HeaderSpacer';
import { ICONS } from '@/constants/icons';

const Profile = () => {
  const navigate = useNavigate();
  const { user, loading: authLoading, logout } = useContext(AuthContext);
  const [error, setError] = useState(null);

  useEffect(() => {
    const token = localStorage.getItem('token');
    if (!token) {
      navigate('/login', { replace: true });
      return;
    }
    const parsedToken = parseJwt(token);
    if (!parsedToken || parsedToken.exp * 1000 < Date.now()) {
      localStorage.removeItem('token');
      logout(); // Gọi logout từ AuthContext
      return;
    }

    // Nếu user chưa được load từ AuthContext, gọi API để lấy
    if (!user && !authLoading) {
      authApi
        .get('/auth/me')
        .then((response) => {
          // AuthContext đã xử lý user, không cần set lại
        })
        .catch((err) => {
          setError(err.response?.data?.msg || 'Không thể lấy thông tin người dùng');
          logout(); // Gọi logout nếu lỗi
        });
    }
  }, [navigate, user, authLoading, logout]);

  const handleLogout = () => {
    logout(); // Sử dụng logout từ AuthContext
  };

  // Thêm hàm kiểm tra lỗi để tránh crash
  const renderSafely = () => {
    try {
      if (!user) return <Loading />;

      return (
            <Box
              style={{
                padding: '20px',
                display: 'flex',
                flexDirection: 'column',
                alignItems: 'center',
                textAlign: 'center',
              }}
            >
              <Box
                className="profile-avatar"
                style={{
                  width: '120px',
                  height: '120px',
                  borderRadius: '50%',
                  marginBottom: '20px',
                  position: 'relative',
                  border: '3px solid #0068ff',
                  overflow: 'hidden'
                }}
              >
                {user.avatar ? (
                  <img
                    src={user.avatar}
                    alt={user.name}
                    style={{
                      width: '100%',
                      height: '100%',
                      objectFit: 'cover',
                    }}
                    onError={(e) => {
                      e.target.style.display = 'none';
                      e.target.nextSibling.style.display = 'flex';
                    }}
                  />
                ) : null}
                <Box
                  className={`avatar-placeholder ${user.gender}`}
                  style={{
                    display: user.avatar ? 'none' : 'flex',
                    alignItems: 'center',
                    justifyContent: 'center',
                    width: '100%',
                    height: '100%',
                    fontSize: '60px',
                    background: user.gender === 'male'
                      ? 'linear-gradient(135deg, #c4e3ff, #6cacff)'
                      : 'linear-gradient(135deg, #ffd6ec, #ff8bc2)',
                    color: user.gender === 'male' ? '#0068ff' : '#e6007e'
                  }}
                >
                  {user.gender === 'male' ? ICONS.MALE : ICONS.FEMALE}
                </Box>
                {user.favorite && (
                  <Box
                    className="favorite-badge"
                    style={{
                      position: 'absolute',
                      top: '10px',
                      right: '10px',
                      width: '28px',
                      height: '28px',
                      backgroundColor: '#FFD700',
                      borderRadius: '50%',
                      display: 'flex',
                      alignItems: 'center',
                      justifyContent: 'center',
                      fontSize: '16px',
                      color: '#333',
                      boxShadow: '0 1px 3px rgba(0,0,0,0.2)'
                    }}
                  >
                    ★
                  </Box>
                )}
              </Box>
              <Text bold size="xLarge" style={{ marginBottom: '10px', fontSize: '22px' }}>
                {user.name}
              </Text>
              <Text style={{
                color: '#0068ff',
                marginBottom: '12px',
                fontWeight: '500',
                fontSize: '16px'
              }}>
                {user.displayRole || (user.role.includes('student') ? 'Học sinh' : 'Giáo viên')}
                {user.specificRole && user.specificRole !== 'student' && !user.displayRole && (
                  <span> - {user.specificRole === 'class_monitor' ? 'Lớp trưởng' : user.specificRole}</span>
                )}
              </Text>
              <Box style={{
                backgroundColor: '#f0f6ff',
                padding: '15px 20px',
                borderRadius: '10px',
                marginBottom: '20px',
                width: '100%',
                maxWidth: '300px'
              }}>
                {user.studentId && (
                  <Text style={{ color: '#666', marginBottom: '10px', fontSize: '14px', display: 'flex', justifyContent: 'space-between' }}>
                    <span style={{ fontWeight: '500' }}>Mã học sinh:</span>
                    <span>{user.studentId}</span>
                  </Text>
                )}
                {user.phoneNumber && (
                  <Text style={{ color: '#666', marginBottom: '10px', fontSize: '14px', display: 'flex', justifyContent: 'space-between' }}>
                    <span style={{ fontWeight: '500' }}>Số điện thoại:</span>
                    <span>{user.phoneNumber}</span>
                  </Text>
                )}
                {user.zaloId && (
                  <Text style={{ color: '#666', marginBottom: '10px', fontSize: '14px', display: 'flex', justifyContent: 'space-between' }}>
                    <span style={{ fontWeight: '500' }}>Zalo ID:</span>
                    <span>{user.zaloId}</span>
                  </Text>
                )}
                {user.group && (
                  <Text style={{ color: '#666', marginBottom: '10px', fontSize: '14px', display: 'flex', justifyContent: 'space-between' }}>
                    <span style={{ fontWeight: '500' }}>Nhóm:</span>
                    <span>{user.group}</span>
                  </Text>
                )}
                {user.class && (
                  <Text style={{ color: '#666', fontSize: '14px', display: 'flex', justifyContent: 'space-between' }}>
                    <span style={{ fontWeight: '500' }}>Lớp:</span>
                    <span>{user.class.name}</span>
                  </Text>
                )}
              </Box>
              <Button
                onClick={handleLogout}
                style={{
                  backgroundColor: '#ff4d4f',
                  color: 'white',
                  padding: '12px 20px',
                  borderRadius: '10px',
                  width: '200px',
                  fontSize: '16px',
                  fontWeight: '500',
                  boxShadow: '0 2px 8px rgba(255, 77, 79, 0.3)',
                  display: 'flex',
                  alignItems: 'center',
                  justifyContent: 'center',
                  gap: '8px'
                }}
              >
                <span style={{ fontSize: '18px' }}>🚪</span>
                <span>Đăng xuất</span>
              </Button>
            </Box>
      );
    } catch (err) {
      console.error("Error rendering profile:", err);
      return (
        <Box style={{ padding: '20px', textAlign: 'center' }}>
          <Text size="large" style={{ marginBottom: '10px', color: '#ff4d4f' }}>
            Đã xảy ra lỗi khi hiển thị trang cá nhân
          </Text>
          <Button
            onClick={() => window.location.reload()}
            style={{ backgroundColor: '#0068ff', color: 'white' }}
          >
            Tải lại trang
          </Button>
        </Box>
      );
    }
  };

  return (
    <SnackbarProvider>
      <Box style={{ paddingBottom: '60px', position: 'relative' }}>
        <HeaderEdu />
        <HeaderSpacer />
        {authLoading && <Loading />}
        {error ? (
          <Box style={{ padding: '20px', textAlign: 'center' }}>
            <Text size="large" style={{ marginBottom: '10px', color: '#ff4d4f' }}>
              Lỗi: {error}
            </Text>
          </Box>
        ) : renderSafely()}
        <BottomNavigation />
      </Box>
    </SnackbarProvider>
  );
};

export default Profile;
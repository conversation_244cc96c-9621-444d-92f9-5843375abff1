/**
 * T<PERSON><PERSON> hợp các hằng số liên quan đến vi phạm nội quy trong ứng dụng
 * Giú<PERSON> thống nhất cách sử dụng và dễ dàng thay đổi sau này
 */

// Các loại vi phạm nội quy (dựa trên MyViolations.jsx)
export const VIOLATION_TYPES = {
  ABSENT: 'absent',
  LATE: 'late', 
  UNIFORM: 'uniform',
  BEHAVIOR: 'behavior',
  HOMEWORK: 'homework',
  PHONE: 'phone',
  TALKING: 'talking',
  EATING: 'eating',
  ACTIVITY: 'activity',
  DISRUPTION: 'disruption',
  DISRESPECT: 'disrespect',
  CHEATING: 'cheating',
  SMOKING: 'smoking',
  FIGHTING: 'fighting',
  VANDALISM: 'vandalism',
  NOISE: 'noise',
  OTHER: 'other'
};

// Tên hiển thị cho các loại vi phạm (từ MyViolations.jsx)
export const VIOLATION_TYPE_LABELS = {
  [VIOLATION_TYPES.ABSENT]: 'Vắng mặt không phép',
  [VIOLATION_TYPES.LATE]: 'Đi học muộn',
  [VIOLATION_TYPES.UNIFORM]: 'Không đúng trang phục',
  [VIOLATION_TYPES.BEHAVIOR]: 'Vi phạm nội quy lớp học',
  [VIOLATION_TYPES.HOMEWORK]: 'Không làm bài tập',
  [VIOLATION_TYPES.PHONE]: 'Sử dụng điện thoại',
  [VIOLATION_TYPES.TALKING]: 'Nói chuyện riêng',
  [VIOLATION_TYPES.EATING]: 'Ăn uống trong lớp',
  [VIOLATION_TYPES.ACTIVITY]: 'Không tham gia hoạt động',
  [VIOLATION_TYPES.DISRUPTION]: 'Gây rối trật tự',
  [VIOLATION_TYPES.DISRESPECT]: 'Không tôn trọng thầy cô',
  [VIOLATION_TYPES.CHEATING]: 'Gian lận trong thi cử',
  [VIOLATION_TYPES.SMOKING]: 'Hút thuốc',
  [VIOLATION_TYPES.FIGHTING]: 'Đánh nhau',
  [VIOLATION_TYPES.VANDALISM]: 'Phá hoại tài sản',
  [VIOLATION_TYPES.NOISE]: 'Gây ồn ào',
  [VIOLATION_TYPES.OTHER]: 'Vi phạm khác'
};

// Trạng thái xử lý vi phạm (từ MyViolations.jsx và API)
export const VIOLATION_STATUS = {
  PENDING: 'pending',
  PROCESSED: 'processed',
  APPEALED: 'appealed',
  APPEAL_APPROVED: 'appeal_approved',
  APPEAL_REJECTED: 'appeal_rejected'
};

// Tên hiển thị và màu sắc cho trạng thái (từ MyViolations.jsx)
export const VIOLATION_STATUS_INFO = {
  [VIOLATION_STATUS.PENDING]: { 
    label: 'Chờ xử lý', 
    color: '#f39c12', 
    bg: '#fff3cd' 
  },
  [VIOLATION_STATUS.PROCESSED]: { 
    label: 'Đã xử lý', 
    color: '#28a745', 
    bg: '#d4edda' 
  },
  [VIOLATION_STATUS.APPEALED]: { 
    label: 'Đã khiếu nại', 
    color: '#007bff', 
    bg: '#d1ecf1' 
  },
  [VIOLATION_STATUS.APPEAL_APPROVED]: { 
    label: 'Khiếu nại được chấp nhận', 
    color: '#28a745', 
    bg: '#d4edda' 
  },
  [VIOLATION_STATUS.APPEAL_REJECTED]: { 
    label: 'Khiếu nại bị từ chối', 
    color: '#dc3545', 
    bg: '#f8d7da' 
  }
};

// Các tham số API filter (từ backend API)
export const VIOLATION_FILTERS = {
  PAGE: 'page',
  LIMIT: 'limit',
  STUDENT_ID: 'studentId',
  CLASS_ID: 'classId',
  SCHOOL_YEAR: 'schoolYear',
  VIOLATION_TYPE: 'violationType',
  STATUS: 'status',
  START_DATE: 'startDate',
  END_DATE: 'endDate',
  PERIOD: 'period' // for timeline stats: day, week, month, year
};

// Giá trị mặc định cho pagination
export const VIOLATION_PAGINATION = {
  DEFAULT_PAGE: 1,
  DEFAULT_LIMIT: 10,
  MAX_LIMIT: 50
};

// Quy tắc khiếu nại
export const APPEAL_RULES = {
  MIN_REASON_LENGTH: 10,
  ALLOWED_STATUS: [VIOLATION_STATUS.PROCESSED]
};

// Các tùy chọn period cho timeline stats
export const TIMELINE_PERIODS = {
  DAY: 'day',
  WEEK: 'week',
  MONTH: 'month',
  YEAR: 'year'
};

export const TIMELINE_PERIOD_LABELS = {
  [TIMELINE_PERIODS.DAY]: 'Theo ngày',
  [TIMELINE_PERIODS.WEEK]: 'Theo tuần',
  [TIMELINE_PERIODS.MONTH]: 'Theo tháng',
  [TIMELINE_PERIODS.YEAR]: 'Theo năm'
};

// Helper function để lấy label của violation type
export const getViolationTypeLabel = (type) => {
  return VIOLATION_TYPE_LABELS[type] || type;
};

// Helper function để lấy thông tin status
export const getViolationStatusInfo = (status) => {
  return VIOLATION_STATUS_INFO[status] || { 
    label: status, 
    color: '#6c757d', 
    bg: '#f8f9fa' 
  };
};

import { useEffect, useRef } from 'react';

/**
 * Custom hook để xử lý infinite scroll
 * @param {Object} options - <PERSON><PERSON><PERSON> tùy chọn cho infinite scroll
 * @param {number} options.currentPage - Trang hiện tại
 * @param {number} options.totalPages - <PERSON><PERSON>ng số trang
 * @param {boolean} options.loading - Trạng thái đang loading
 * @param {Function} options.onLoadMore - Callback khi cần load thêm dữ liệu
 * @param {number} options.threshold - Ngưỡng trigger (default: 0.1)
 * @param {boolean} options.enabled - Có bật infinite scroll không (default: true)
 * @returns {Object} - Tr<PERSON> về observerRef để gắn vào element cu<PERSON>i danh s<PERSON>ch
 */
const useInfiniteScroll = ({ 
    currentPage, 
    totalPages, 
    loading, 
    onLoadMore, 
    threshold = 0.1,
    enabled = true 
}) => {
    const observerRef = useRef(null);

    useEffect(() => {
        if (!enabled) return;

        const observer = new IntersectionObserver(
            (entries) => {
                if (entries[0].isIntersecting && currentPage < totalPages && !loading) {
                    onLoadMore();
                }
            },
            { threshold }
        );

        if (observerRef.current) {
            observer.observe(observerRef.current);
        }

        return () => {
            if (observerRef.current) {
                observer.unobserve(observerRef.current);
            }
        };
    }, [currentPage, totalPages, loading, onLoadMore, threshold, enabled]);

    return { observerRef };
};

export default useInfiniteScroll; 
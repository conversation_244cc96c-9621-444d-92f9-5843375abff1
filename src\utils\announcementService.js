import { authApi } from './api';

/**
 * Post announcement to specific users
 * @param {Object} params - Announcement parameters
 * @param {string} params.title - Announcement title
 * @param {string} params.content - Announcement content
 * @param {string[]} params.userIds - Array of user IDs to send to
 * @param {string} [params.type='mutual_communication'] - Announcement type
 * @returns {Promise} API response
 */
export const postAnnouncement = async ({ title, content, userIds, type = 'mutual_communication' }) => {
    try {
        const body = {
            title,
            content,
            type,
            recipients: {
                specificUsers: userIds
            }
        };

        const response = await authApi.post('/announcements', body);
        return response.data;
    } catch (error) {
        console.error('Error posting announcement:', error);
        throw error;
    }
};

/**
 * Post exam change request approval notification
 * @param {Object} params - Notification parameters
 * @param {Object} params.teacher - Teacher information
 * @param {string} params.examType - Exam type
 * @param {string} params.schoolYear - School year
 * @param {string} params.originalDate - Original exam date
 * @param {string} params.originalTimeSlot - Original time slot
 * @param {string} params.newDate - New exam date
 * @param {string} params.newTimeSlot - New time slot
 * @param {string} params.room - Exam room
 * @param {string} params.subject - Subject name
 * @param {string} params.status - Request status (APPROVED/REJECTED)
 * @param {string} [params.approvalNotes] - Approval notes
 * @returns {Promise} API response
 */
export const postExamChangeRequestNotification = async ({
    teacher,
    examType,
    schoolYear,
    originalDate,
    originalTimeSlot,
    newDate,
    newTimeSlot,
    room,
    subject,
    status,
    approvalNotes = ''
}) => {
    try {
        const isApproved = status === 'APPROVED';
        const formatDate = (dateStr) => new Date(dateStr).toLocaleDateString('vi-VN');
        const formatTime = (timeSlot) => {
            const timeSlots = {
                'morning': '08:00',
                'afternoon': '13:30'
            };
            return timeSlots[timeSlot] || timeSlot;
        };

        const title = isApproved 
            ? 'Đơn xin đổi lịch thi đã được duyệt'
            : 'Đơn xin đổi lịch thi bị từ chối';

        let content = `Đơn xin đổi lịch thi môn ${subject} của bạn từ ngày ${formatDate(originalDate)} sang ${formatDate(newDate)} đã được ${isApproved ? 'phê duyệt' : 'từ chối'}.\n\n`;

        if (isApproved) {
            content += `Thời gian thi mới: ${formatDate(newDate)} lúc ${formatTime(newTimeSlot)}\n`;
            if (room) {
                content += `Phòng thi: ${room}\n`;
            }
            content += '\nVui lòng chuẩn bị kỹ càng!';
        } else {
            content += 'Thời gian thi giữ nguyên như ban đầu.\n';
            content += `Thời gian: ${formatDate(originalDate)} lúc ${formatTime(originalTimeSlot)}\n`;
            if (room) {
                content += `Phòng thi: ${room}\n`;
            }
        }

        if (approvalNotes) {
            content += `\n\nGhi chú: ${approvalNotes}`;
        }

        return await postAnnouncement({
            title,
            content,
            userIds: [teacher._id || teacher.id]
        });
    } catch (error) {
        console.error('Error posting exam change request notification:', error);
        throw error;
    }
};

/**
 * Post general exam supervision notification
 * @param {Object} params - Notification parameters  
 * @param {string} params.title - Notification title
 * @param {Object[]} params.teachers - Array of teacher objects
 * @param {string} params.examType - Exam type
 * @param {string} params.schoolYear - School year
 * @param {string} params.examDate - Exam date
 * @param {string} params.timeSlot - Time slot
 * @param {string} [params.additionalInfo] - Additional information
 * @returns {Promise} API response
 */
export const postExamSupervisionNotification = async ({
    title,
    teachers,
    examType,
    schoolYear,
    examDate,
    timeSlot,
    additionalInfo = ''
}) => {
    try {
        const formatDate = (dateStr) => new Date(dateStr).toLocaleDateString('vi-VN');
        const formatTime = (timeSlot) => {
            const timeSlots = {
                'morning': '08:00',
                'afternoon': '13:30'
            };
            return timeSlots[timeSlot] || timeSlot;
        };

        let content = `Bạn đã được phân công coi thi ${examType} năm học ${schoolYear}.\n\n`;
        content += `Thời gian: ${formatDate(examDate)} lúc ${formatTime(timeSlot)}\n`;
        
        if (additionalInfo) {
            content += `\n${additionalInfo}`;
        }
        
        content += '\n\nVui lòng chuẩn bị và có mặt đúng giờ!';

        const userIds = teachers.map(teacher => teacher._id || teacher.id);

        return await postAnnouncement({
            title,
            content,
            userIds
        });
    } catch (error) {
        console.error('Error posting exam supervision notification:', error);
        throw error;
    }
};

/**
 * Post custom notification to specific users
 * @param {Object} params - Notification parameters
 * @param {string} params.title - Notification title
 * @param {string} params.content - Notification content  
 * @param {Object[]} params.users - Array of user objects
 * @returns {Promise} API response
 */
export const postCustomNotification = async ({ title, content, users }) => {
    try {
        const userIds = users.map(user => user._id || user.id);
        
        return await postAnnouncement({
            title,
            content,
            userIds
        });
    } catch (error) {
        console.error('Error posting custom notification:', error);
        throw error;
    }
}; 
# Refactor Violation Constants

## Tóm tắt thay đổi

Đã tạo file constants mới và refactor code để loại bỏ hardcode values trong MyViolations.jsx.

## Files đã tạo/sửa đổi

### 1. `src/constants/violations.js` (MỚI)
- Tậ<PERSON> hợp tất cả constants liên quan đến vi phạm nội quy
- Bao gồm:
  - `VIOLATION_TYPES`: <PERSON><PERSON><PERSON> loại vi phạm
  - `VIOLATION_TYPE_LABELS`: Tên hiển thị tiếng Việt
  - `VIOLATION_STATUS`: Trạng thái xử lý vi phạm
  - `VIOLATION_STATUS_INFO`: Thông tin hiển thị và màu sắc cho status
  - `VIOLATION_FILTERS`: Tham số API filter
  - `VIOLATION_PAGINATION`: Giá trị mặc định cho pagination
  - `APPEAL_RULES`: Quy tắc khiếu nại
  - Helper functions: `getViolationTypeLabel()`, `getViolationStatusInfo()`

### 2. `src/pages/students/MyViolations.jsx` (CẬP NHẬT)
- Import constants từ file mới
- Loại bỏ hardcode functions:
  - ❌ `getViolationTypeLabel()` (local function)
  - ❌ `getStatusInfo()` (local function)
  - ✅ Sử dụng functions từ constants
- Thay thế hardcode values:
  - ❌ `limit=10` → ✅ `limit=${VIOLATION_PAGINATION.DEFAULT_LIMIT}`
  - ❌ `appealReason.length < 10` → ✅ `appealReason.length < APPEAL_RULES.MIN_REASON_LENGTH`
  - ❌ `status === 'processed'` → ✅ `status === VIOLATION_STATUS.PROCESSED`
- Loại bỏ import không sử dụng: `List`

## Lợi ích

1. **Tính nhất quán**: Tất cả violation-related constants được quản lý tập trung
2. **Dễ bảo trì**: Thay đổi constants chỉ cần sửa 1 file
3. **Tái sử dụng**: Các constants có thể được sử dụng ở nhiều components khác
4. **Type safety**: Giảm lỗi typo khi sử dụng string literals
5. **Dễ đọc**: Code rõ ràng hơn với tên constants có ý nghĩa

## Constants được loại bỏ khỏi QA content

Từ QA content ban đầu, đã loại bỏ những phần không cần thiết:
- `VIOLATION_SEVERITY` và `VIOLATION_SEVERITY_LABELS` (không sử dụng trong UI)
- `VIOLATION_POINTS` (backend tự tính)
- `DEFAULT_CONDUCT_POINTS`, `MIN_CONDUCT_POINTS`, `MAX_CONDUCT_POINTS` (không dùng trong MyViolations)
- `CONDUCT_CLASSIFICATION` (không dùng trong MyViolations)
- `DEFAULT_VIOLATION_MESSAGES` (backend template)
- `VIOLATION_PERMISSIONS` (backend logic)

## Sử dụng

```javascript
import { 
    VIOLATION_STATUS, 
    APPEAL_RULES,
    getViolationTypeLabel,
    getViolationStatusInfo 
} from '../../constants/violations';

// Sử dụng
const label = getViolationTypeLabel('absent'); // "Vắng mặt không phép"
const statusInfo = getViolationStatusInfo('processed'); // {label: "Đã xử lý", color: "#28a745", bg: "#d4edda"}
```

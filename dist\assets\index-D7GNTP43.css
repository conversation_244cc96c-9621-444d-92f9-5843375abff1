@charset "UTF-8";:root{--zaui-light-color-primary: #006af5;--zaui-dark-color-primary: #006af5;--zaui-light-color-on-primary: #ffffff;--zaui-dark-color-on-primary: #ffffff;--zaui-light-text-color: #141415;--zaui-dark-text-color: #f4f5f6;--zaui-avatar-text-color: #ffffff;--zaui-avatar-background-gradient-01: linear-gradient(45deg, #006af5 0%, #8fc1ff 100%);--zaui-avatar-background-gradient-02: linear-gradient(45deg, #6a40bf 0%, #52a0ff 100%);--zaui-avatar-background-gradient-03: linear-gradient(45deg, #12aee2 0%, #66d68f 100%);--zaui-avatar-background-gradient-04: linear-gradient(45deg, #34b764 0%, #99e5b5 100%);--zaui-mask-overlay-color: #000000b3;--zaui-light-body-background-color: #e9ebed;--zaui-light-avatar-border: #ffffff;--zaui-light-avatar-status-background: #ffffff;--zaui-light-avatar-status-online: #34b764;--zaui-light-avatar-status-blocked: #dc1f18;--zaui-light-avatar-story-contour-color: linear-gradient(45deg, #006af5 0%, #5fcbf2 100%);--zaui-light-avatar-uploading: linear-gradient(90deg, rgba(95, 203, 242, 0) 0%, #006af5 100%);--zaui-light-avatar-story-seen: #d6d9dc;--zaui-light-avatar-counter-background: #e9ebed;--zaui-light-bottom-navigation-color: #767a7f;--zaui-light-bottom-navigation-active-color: #006af5;--zaui-light-bottom-navigation-divider-color: #e9ebed;--zaui-light-bottom-navigation-background-color: #ffffff;--zaui-light-button-primary-background: var(--zaui-light-color-primary);--zaui-light-button-primary-background-pressed: #0250b6;--zaui-light-button-primary-text: var(--zaui-light-color-on-primary);--zaui-light-button-primary-icon: var(--zaui-light-color-on-primary);--zaui-light-button-primary-danger-background: #dc1f18;--zaui-light-button-primary-danger-background-pressed: #a51712;--zaui-light-button-primary-danger-text: #ffffff;--zaui-light-button-secondary-background: #d6e9ff;--zaui-light-button-secondary-background-pressed: #b8d9ff;--zaui-light-button-secondary-text: #006af5;--zaui-light-button-secondary-icon: #006af5;--zaui-light-button-secondary-danger-background: #fed8d7;--zaui-light-button-secondary-danger-background-pressed: #fcbdba;--zaui-light-button-secondary-danger-text: #dc1f18;--zaui-light-button-secondary-danger-icon: #dc1f18;--zaui-light-button-secondary-neutral-background: #e9ebed;--zaui-light-button-secondary-neutral-background-pressed: #d6d9dc;--zaui-light-button-secondary-neutral-text: #141415;--zaui-light-button-secondary-neutral-icon: #141415;--zaui-light-button-tertiary-background-pressed: #ebf4ff;--zaui-light-button-tertiary-text: #006af5;--zaui-light-button-tertiary-icon: #006af5;--zaui-light-button-tertiary-danger-background-pressed: #ffebeb;--zaui-light-button-tertiary-danger-text: #dc1f18;--zaui-light-button-tertiary-danger-icon: #dc1f18;--zaui-light-button-tertiary-neutral-background-pressed: #e9ebed;--zaui-light-button-tertiary-neutral-text: #141415;--zaui-light-button-tertiary-neutral-icon: #141415;--zaui-light-button-background-disabled: #d6d9dc;--zaui-light-button-text-disabled: #b9bdc1;--zaui-light-button-icon-disabled: #b9bdc1;--zaui-light-checkbox-border-color: #b9bdc1;--zaui-light-checkbox-uncheck-background: #ffffff;--zaui-light-checkbox-checked-background: #006af5;--zaui-light-checkbox-checked-mark-color: #ffffff;--zaui-light-checkbox-disabled-checked-background: #8fc1ff;--zaui-light-checkbox-disabled-checked-mark-color: #ffffffcc;--zaui-light-checkbox-disabled-uncheck-background: #d6d9dc;--zaui-light-checkbox-disabled-label: #b9bdc1;--zaui-light-header-color: #141415;--zaui-light-header-background-color: #ffffff;--zaui-light-header-divider: #e9ebed;--zaui-light-input-text-color: var(--zaui-light-text-color);--zaui-light-input-border-color: #b9bdc1;--zaui-light-input-hover-border-color: #006af5;--zaui-light-input-placeholder-color: #b9bdc1;--zaui-light-input-background-color: #ffffff;--zaui-light-input-helper-text-color: #767a7f;--zaui-light-input-helper-icon-background-color: #767a7f;--zaui-light-input-error-text-color: #dc1f18;--zaui-light-input-error-icon-background-color: #dc1f18;--zaui-light-input-clear-icon-color: #141415;--zaui-light-input-status-success-icon-focus-visible-color: #34b764;--zaui-light-input-status-success-icon-color: #34b764;--zaui-light-input-status-error: #dc1f18;--zaui-light-input-disabled-color: #767a7f;--zaui-light-input-disabled-background-color: #0000001a;--zaui-dark-input-text-color: var(--zaui-dark-text-color);--zaui-dark-input-border-color: #767a7f;--zaui-dark-input-hover-border-color: #006af5;--zaui-dark-input-placeholder-color: #53575a;--zaui-dark-input-background-color: #141415;--zaui-dark-input-helper-text-color: #8f9499;--zaui-dark-input-helper-icon-background-color: #8f9499;--zaui-dark-input-error-text-color: #dc1f18;--zaui-dark-input-error-icon-background-color: #dc1f18;--zaui-dark-input-clear-icon-color: #f4f5f6;--zaui-dark-input-status-success-icon-focus-visible-color: #f4f5f6;--zaui-dark-input-status-success-icon-color: #34b764;--zaui-dark-input-status-error: #dc1f18;--zaui-dark-input-disabled-color: #8f9499;--zaui-dark-input-disabled-background-color: #ffffff1a;--zaui-light-list-item-brackets-color: #767a7f;--zaui-light-list-item-subtitle-color: #767a7f;--zaui-light-list-item-icon-color: #767a7f;--zaui-light-list-item-icon-link-color: #006af5;--zaui-light-list-item-divider-color: #e9ebed;--zaui-light-modal-background: #ffffff;--zaui-light-modal-divider: #e9ebed;--zaui-light-picker-title-color: #141415;--zaui-light-picker-option-selected-color: #006af5;--zaui-light-picker-option-2nd-color: #767a7f;--zaui-light-picker-option-color: #b9bdc1;--zaui-light-picker-selected-background-color: #f4f5f6;--zaui-light-progress-completed: #006af5;--zaui-light-progress-background: #ffffff;--zaui-light-radio-border-color: #b9bdc1;--zaui-light-radio-uncheck-background: #ffffff;--zaui-light-radio-checked-background: #006af5;--zaui-light-radio-checked-mark-color: #ffffff;--zaui-light-radio-disabled-checked-background: #8fc1ff;--zaui-light-radio-disabled-checked-mark-color: #ffffffcc;--zaui-light-radio-disabled-uncheck-background: #d6d9dc;--zaui-light-radio-disabled-label: #b9bdc1;--zaui-light-option-selected-color: #006af5;--zaui-light-option-divider-color: #e9ebed;--zaui-light-option-color: #141415;--zaui-light-option-group-color: #767a7f;--zaui-light-pressed-bg-color: #e9ebed;--zaui-light-select-title-color: #141415;--zaui-light-close-btn-color: #141415;--zaui-light-option-color-disabled: #767a7f;--zaui-light-sheet-title: #141415;--zaui-light-sheet-divider: #e9ebed;--zaui-light-sheet-container: #ffffff;--zaui-light-sheet-handler: #e9ebed;--zaui-light-action-sheet-title: #767a7f;--zaui-light-action-sheet-divider: #e9ebed;--zaui-light-action-sheet-group-divider: #f4f5f6;--zaui-light-slider-track-bg-color: #d6d9dc;--zaui-light-slider-track-active-bg-color: #006af5;--zaui-light-slider-thumb-bg-color: #006af5;--zaui-light-slider-mark-bg-color: #d6d9dc;--zaui-light-slider-mark-filled-bg-color: #006af5;--zaui-light-slider-label-color: #767a7f;--zaui-light-slider-value-color: #141415;--zaui-light-slider-prefix-suffix-color: #767a7f;--zaui-light-snackbar-background: #252627;--zaui-light-snackbar-text-color: #ffffff;--zaui-light-snackbar-action-color: #52a0ff;--zaui-light-snackbar-default-icon-color: #767a7f;--zaui-light-snackbar-success-color: #34b764;--zaui-light-snackbar-info-color: #52a0ff;--zaui-light-snackbar-error-color: #dc1f18;--zaui-light-snackbar-warning-color: #e8ba02;--zaui-light-snackbar-download-color: #767a7f;--zaui-light-snackbar-disconnect-color: #767a7f;--zaui-light-snackbar-connect-color: #34b764;--zaui-light-snackbar-progress-color: #52a0ff;--zaui-light-countdown-bg-color: #ffffff;--zaui-light-countdown-progress-color: #52a0ff;--zaui-light-countdown-counter-color: #ffffff;--zaui-light-spinner-border-color: #d6d9dc;--zaui-light-spinner-dot-color: #006af5;--zaui-light-switch-bg-color: #006af5;--zaui-light-switch-disabled-bg-color: #8fc1ff;--zaui-light-switch-off-bg-color: #b9bdc1;--zaui-light-switch-off-disabled-bg-color: #d6d9dc;--zaui-light-switch-handler-bg-color: #ffffff;--zaui-light-switch-handler-disabled-bg-color: #ffffff;--zaui-light-switch-label-color: #141415;--zaui-light-switch-label-disabled-color: #b9bdc1;--zaui-light-tabbar-divider: #e9ebed;--zaui-light-tabbar-background: #ffffff;--zaui-light-tabbar-label: #767a7f;--zaui-light-tabbar-label-active: #141415;--zaui-light-tabbar-active-line: #006af5;--zaui-dark-body-background-color: #000000e6;--zaui-dark-avatar-border: #141415;--zaui-dark-avatar-status-background: #141415;--zaui-dark-avatar-status-online: #34b764;--zaui-dark-avatar-status-blocked: #dc1f18;--zaui-dark-avatar-story-contour-color: linear-gradient(45deg, #006af5 0%, #5fcbf2 100%);--zaui-dark-avatar-uploading: linear-gradient(90deg, rgba(95, 203, 242, 0) 0%, #006af5 100%);--zaui-dark-avatar-story-seen: #53575a;--zaui-dark-avatar-counter-background: #36383a;--zaui-dark-bottom-navigation-color: #8f9499;--zaui-dark-bottom-navigation-active-color: #52a0ff;--zaui-dark-bottom-navigation-divider-color: #36383a;--zaui-dark-bottom-navigation-background-color: #141415;--zaui-dark-button-primary-background: var(--zaui-dark-color-primary);--zaui-dark-button-primary-background-pressed: #0250b6;--zaui-dark-button-primary-text: var(--zaui-dark-color-on-primary);--zaui-dark-button-primary-icon: var(--zaui-dark-color-on-primary);--zaui-dark-button-primary-danger-background: #dc1f18;--zaui-dark-button-primary-danger-background-pressed: #a51712;--zaui-dark-button-primary-danger-text: #ffffff;--zaui-dark-button-primary-danger-icon: #ffffff;--zaui-dark-button-secondary-background: #03316d;--zaui-dark-button-secondary-background-pressed: #03244e;--zaui-dark-button-secondary-text: #8fc1ff;--zaui-dark-button-secondary-icon: #8fc1ff;--zaui-dark-button-secondary-danger-background: #650e0b;--zaui-dark-button-secondary-danger-background-pressed: #490a08;--zaui-dark-button-secondary-danger-text: #f89996;--zaui-dark-button-secondary-danger-icon: #f89996;--zaui-dark-button-secondary-neutral-background: #36383a;--zaui-dark-button-secondary-neutral-background-pressed: #252627;--zaui-dark-button-secondary-neutral-text: #ffffff;--zaui-dark-button-secondary-neutral-icon: #ffffff;--zaui-dark-button-tertiary-background-pressed: #03316d;--zaui-dark-button-tertiary-text: #52a0ff;--zaui-dark-button-tertiary-icon: #52a0ff;--zaui-dark-button-tertiary-danger-background-pressed: #650e0b;--zaui-dark-button-tertiary-danger-text: #f1645f;--zaui-dark-button-tertiary-danger-icon: #f1645f;--zaui-dark-button-tertiary-neutral-background-pressed: #36383a;--zaui-dark-button-tertiary-neutral-text: #ffffff;--zaui-dark-button-tertiary-neutral-icon: #ffffff;--zaui-dark-button-background-disabled: #36383a;--zaui-dark-button-text-disabled: #53575a;--zaui-dark-button-icon-disabled: #53575a;--zaui-dark-checkbox-border-color: #767a7f;--zaui-dark-checkbox-uncheck-background: #141415;--zaui-dark-checkbox-checked-background: #006af5;--zaui-dark-checkbox-checked-mark-color: #ffffff;--zaui-dark-checkbox-disabled-checked-background: #033e8c;--zaui-dark-checkbox-disabled-checked-mark-color: #ffffff99;--zaui-dark-checkbox-disabled-uncheck-background: #36383a;--zaui-dark-checkbox-disabled-label: #53575a;--zaui-dark-header-color: #f4f5f6;--zaui-dark-header-background-color: #141415;--zaui-dark-header-divider: #36383a;--zaui-dark-list-item-brackets-color: #8f9499;--zaui-dark-list-item-subtitle-color: #8f9499;--zaui-dark-list-item-icon-color: #767a7f;--zaui-dark-list-item-icon-link-color: #006af5;--zaui-dark-list-item-divider-color: #36383a;--zaui-dark-modal-background: #252627;--zaui-dark-modal-divider: #36383a;--zaui-dark-picker-title-color: #f4f5f6;--zaui-dark-picker-option-selected-color: #006af5;--zaui-dark-picker-option-2nd-color: #8f9499;--zaui-dark-picker-option-color: #53575a;--zaui-dark-picker-selected-background-color: #252627;--zaui-dark-progress-completed: #52a0ff;--zaui-dark-progress-background: #ffffff33;--zaui-dark-radio-border-color: #767a7f;--zaui-dark-radio-uncheck-background: #141415;--zaui-dark-radio-checked-background: #006af5;--zaui-dark-radio-checked-mark-color: #ffffff;--zaui-dark-radio-disabled-checked-background: #033e8c;--zaui-dark-radio-disabled-checked-mark-color: #ffffff99;--zaui-dark-radio-disabled-uncheck-background: #36383a;--zaui-dark-radio-disabled-label: #53575a;--zaui-dark-option-selected-color: #006af5;--zaui-dark-option-divider-color: #36383a;--zaui-dark-option-color: #f4f5f6;--zaui-dark-option-group-color: #8f9499;--zaui-dark-pressed-bg-color: #36383a;--zaui-dark-select-title-color: #f4f5f6;--zaui-dark-close-btn-color: #f4f5f6;--zaui-dark-option-color-disabled: #8f9499;--zaui-dark-sheet-title: #f4f5f6;--zaui-dark-sheet-divider: #36383a;--zaui-dark-sheet-container: #252627;--zaui-dark-sheet-handler: #36383a;--zaui-dark-action-sheet-title: #8f9499;--zaui-dark-action-sheet-divider: #36383a;--zaui-dark-action-sheet-group-divider: #000000;--zaui-dark-slider-track-bg-color: #d6d9dc;--zaui-dark-slider-track-active-bg-color: #006af5;--zaui-dark-slider-thumb-bg-color: #006af5;--zaui-dark-slider-mark-bg-color: #d6d9dc;--zaui-dark-slider-mark-filled-bg-color: #006af5;--zaui-dark-slider-label-color: #8f9499;--zaui-dark-slider-value-color: #f4f5f6;--zaui-dark-slider-prefix-suffix-color: #d6d9dc;--zaui-dark-snackbar-background: #53575a;--zaui-dark-snackbar-text-color: #ffffff;--zaui-dark-snackbar-action-color: #52a0ff;--zaui-dark-snackbar-default-icon-color: #8f9499;--zaui-dark-snackbar-success-color: #34b764;--zaui-dark-snackbar-info-color: #52a0ff;--zaui-dark-snackbar-error-color: #dc1f18;--zaui-dark-snackbar-warning-color: #e8ba02;--zaui-dark-snackbar-download-color: #8f9499;--zaui-dark-snackbar-disconnect-color: #8f9499;--zaui-dark-snackbar-connect-color: #34b764;--zaui-dark-countdown-bg-color: #ffffff;--zaui-dark-countdown-progress-color: #52a0ff;--zaui-dark-countdown-counter-color: #ffffff;--zaui-dark-snackbar-progress-color: #52a0ff;--zaui-dark-spinner-border-color: #d6d9dc;--zaui-dark-spinner-dot-color: #006af5;--zaui-dark-switch-bg-color: #006af5;--zaui-dark-switch-disabled-bg-color: #033e8c;--zaui-dark-switch-off-bg-color: #53575a;--zaui-dark-switch-off-disabled-bg-color: #36383a;--zaui-dark-switch-handler-bg-color: #ffffff;--zaui-dark-switch-handler-disabled-bg-color: #ffffff99;--zaui-dark-switch-label-color: #f4f5f6;--zaui-dark-switch-label-disabled-color: #53575a;--zaui-dark-tabbar-divider: #36383a;--zaui-dark-tabbar-background: #141415;--zaui-dark-tabbar-label: #8f9499;--zaui-dark-tabbar-label-active: #f4f5f6;--zaui-dark-tabbar-active-line: #006af5}@keyframes loadingCircle{to{transform:rotate(360deg)}}@keyframes zoomIn{0%{opacity:0;transform:scale(0)}to{opacity:1;transform:scale(1)}}@keyframes zoomOut{0%{transform:scale(1)}50%{opacity:.1;transform:scale(.5)}to{opacity:0;transform:scale(0)}}@keyframes fadeIn{0%{opacity:0}to{opacity:1}}@keyframes fadeOut{0%{opacity:1}to{opacity:0}}@keyframes slideUp{0%{transform:translateY(100%)}to{transform:translateY(0)}}@keyframes slideDown{0%{transform:translateY(0)}to{transform:translateY(100%)}}@keyframes cursorAnimate{0%{opacity:0}50%{opacity:1}to{opacity:0}}.zaui-mask{width:100%;height:100%;position:fixed;z-index:1000;top:0;bottom:0;left:0;right:0;background-color:var(--zaui-mask-overlay-color, #000000b3);-webkit-overflow-scrolling:touch;display:none}.zaui-mask-hidden{visibility:visible}.zaui-mask-no-mask{background:transparent;background-color:transparent}.zaui-mask-enter,.zaui-mask-appear,.zaui-mask-exit{animation-duration:.3s;animation-fill-mode:both;animation-timing-function:cubic-bezier(.645,.045,.355,1);animation-play-state:paused;display:unset}.zaui-mask-enter.zaui-mask-enter-active,.zaui-mask-appear.zaui-mask-appear-active{animation-name:fadeIn;animation-play-state:running;display:unset}.zaui-mask-enter-done{display:unset}.zaui-mask-exit.zaui-mask-exit-active{animation-name:fadeOut;animation-play-state:running}.zaui-mask-exit-done{display:none}.zaui-modal-wrapper{position:fixed;top:0;bottom:0;left:0;right:0;overflow:auto;outline:0;z-index:1000;display:flex;justify-content:center;align-items:center;-webkit-overflow-scrolling:touch}.zaui-btn{padding:12px 24px;position:relative;display:inline-block;white-space:nowrap;text-align:center;background-image:none;border:none;cursor:pointer;border-radius:48px;user-select:none;touch-action:manipulation;-moz-appearance:button;appearance:button;-webkit-appearance:button;text-decoration:none;box-shadow:none;-webkit-user-select:none;-moz-user-select:none;-ms-user-select:none;margin:0;background-color:var(--zaui-light-button-primary-background, #006af5);color:var(--zaui-light-button-primary-text, #ffffff);min-width:120px;height:48px;font-size:15px;line-height:20px;font-weight:500}.zaui-btn,.zaui-btn:hover,.zaui-btn:active,.zaui-btn:focus{outline:0;box-shadow:none}.zaui-btn:not([disabled]):hover{text-decoration:none}.zaui-btn:not([disabled]):active{outline:0;box-shadow:none}.zaui-btn[disabled]{cursor:not-allowed;pointer-events:none}.zaui-btn[disabled]>*{pointer-events:none}.zaui-btn:active,.zaui-btn:focus-visible{background-color:var(--zaui-light-button-primary-background-pressed, #0250b6)}.zaui-btn-disabled[disabled],.zaui-btn-primary.zaui-btn-disabled[disabled],.zaui-btn-secondary.zaui-btn-disabled[disabled]{background-color:var(--zaui-light-button-background-disabled, #d6d9dc);color:var(--zaui-light-button-text-disabled, #b9bdc1)}.zaui-btn .zaui-btn-container{display:inline-flex;flex-direction:row;flex-wrap:nowrap;align-items:center}.zaui-btn-tertiary.zaui-btn-disabled[disabled]{background-color:transparent;color:var(--zaui-light-button-text-disabled, #b9bdc1)}.zaui-btn-small{min-width:72px;height:32px;font-size:14px;line-height:18px;font-weight:500;padding:8px 16px}.zaui-btn-large{min-width:120px;height:48px}.zaui-btn-medium{min-width:96px;height:40px;padding:8px 24px}.zaui-btn-full-width{width:100%;min-width:unset}.zaui-btn-primary,.zaui-btn-primary.zaui-btn-highlight{background-color:var(--zaui-light-button-primary-background, #006af5);color:var(--zaui-light-button-primary-text, #ffffff)}.zaui-btn-primary:active,.zaui-btn-primary.zaui-btn-highlight:active,.zaui-btn-primary:focus-visible,.zaui-btn-primary.zaui-btn-highlight:focus-visible{background-color:var(--zaui-light-button-primary-background-pressed, #0250b6)}.zaui-btn-secondary,.zaui-btn-secondary.zaui-btn-highlight{background-color:var(--zaui-light-button-secondary-background, #d6e9ff);color:var(--zaui-light-button-secondary-text, #006af5)}.zaui-btn-secondary:active,.zaui-btn-secondary.zaui-btn-highlight:active,.zaui-btn-secondary:focus-visible,.zaui-btn-secondary.zaui-btn-highlight:focus-visible{background-color:var(--zaui-light-button-secondary-background-pressed, #b8d9ff)}.zaui-btn-tertiary,.zaui-btn-tertiary.zaui-btn-highlight{background-color:transparent;color:var(--zaui-light-button-tertiary-text, #006af5)}.zaui-btn-tertiary:active,.zaui-btn-tertiary.zaui-btn-highlight:active,.zaui-btn-tertiary:focus-visible,.zaui-btn-tertiary.zaui-btn-highlight:focus-visible{background-color:var(--zaui-light-button-tertiary-background-pressed, #ebf4ff)}.zaui-btn-danger,.zaui-btn-primary.zaui-btn-danger{background-color:var(--zaui-light-button-primary-danger-background, #dc1f18);color:var(--zaui-light-button-primary-danger-text, #ffffff)}.zaui-btn-danger:active,.zaui-btn-primary.zaui-btn-danger:active,.zaui-btn-danger:focus-visible,.zaui-btn-primary.zaui-btn-danger:focus-visible{background-color:var(--zaui-light-button-primary-danger-background-pressed, #a51712)}.zaui-btn-secondary.zaui-btn-danger{background-color:var(--zaui-light-button-secondary-danger-background, #fed8d7);color:var(--zaui-light-button-secondary-danger-text, #dc1f18)}.zaui-btn-secondary.zaui-btn-danger:active,.zaui-btn-secondary.zaui-btn-danger:focus-visible{background-color:var(--zaui-light-button-secondary-danger-background-pressed, #fcbdba)}.zaui-btn-tertiary.zaui-btn-danger{background-color:transparent;color:var(--zaui-light-button-tertiary-danger-text, #dc1f18)}.zaui-btn-tertiary.zaui-btn-danger:active,.zaui-btn-tertiary.zaui-btn-danger:focus-visible{background-color:var(--zaui-light-button-tertiary-danger-background-pressed, #ffebeb)}.zaui-btn-secondary.zaui-btn-neutral{background-color:var(--zaui-light-button-secondary-neutral-background, #e9ebed);color:var(--zaui-light-button-secondary-neutral-text, #141415)}.zaui-btn-secondary.zaui-btn-neutral:active,.zaui-btn-secondary.zaui-btn-neutral:focus-visible{background-color:var(--zaui-light-button-secondary-neutral-background-pressed, #d6d9dc)}.zaui-btn-tertiary.zaui-btn-neutral{background-color:transparent;color:var(--zaui-light-button-tertiary-neutral-text, #141415)}.zaui-btn-tertiary.zaui-btn-neutral:active,.zaui-btn-tertiary.zaui-btn-neutral:focus-visible{background-color:var(--zaui-light-button-tertiary-neutral-background-pressed, #e9ebed)}.zaui-btn-loading{position:relative;pointer-events:none}.zaui-btn-loading>*{visibility:hidden}.zaui-btn-loading-container{position:absolute;left:0;top:0;width:100%;height:100%;visibility:visible;display:flex;align-items:center;justify-content:center}.zaui-btn-loading .zaui-btn-loading-icon{width:24px;height:24px;display:inline-block;animation:loadingCircle 1s infinite linear}.zaui-btn-small.zaui-btn-loading .zaui-btn-loading-icon{width:16px;height:16px}.zaui-btn-small .zaui-btn-icon .zaui-icon{font-size:16px}.zaui-btn-icon-only{min-width:unset;width:48px;height:48px;padding:12px;line-height:1}.zaui-btn-icon-only .zaui-btn-icon{position:absolute;top:50%;left:50%;transform:translate(-50%,-50%);width:24px;height:24px;display:inline-block}.zaui-btn-icon-only .zaui-btn-icon .zaui-icon{font-size:24px}.zaui-btn-icon-only.zaui-btn-small{width:32px;height:32px;padding:8px}.zaui-btn-icon-only.zaui-btn-small .zaui-btn-icon{width:16px;height:16px}.zaui-btn-icon-only.zaui-btn-small .zaui-icon{font-size:16px}.zaui-btn-icon-only.zaui-btn-medium{width:40px;height:40px;padding:8px}.zaui-btn-icon-only.zaui-btn-medium .zaui-icon{font-size:24px}.zaui-btn-suffix-icon .zaui-btn-icon{width:24px;height:24px;margin-left:8px}.zaui-btn-suffix-icon.zaui-btn-small .zaui-btn-icon{width:16px;height:16px;margin-left:8px}.zaui-btn-prefix-icon .zaui-btn-icon{width:24px;height:24px;margin-right:8px}.zaui-btn-prefix-icon.zaui-btn-small .zaui-btn-icon{width:16px;height:16px;margin-left:4px}body[zaui-theme=dark] .zaui-btn{background-color:var(--zaui-light-button-primary-background, #006af5);color:var(--zaui-light-button-primary-text, #ffffff);background-color:var(--zaui-dark-button-primary-background, #006af5);background:var(--zaui-dark-button-primary-background, #006af5);color:var(--zaui-dark-button-primary-text, #ffffff)}body[zaui-theme=dark] .zaui-btn:active,body[zaui-theme=dark] .zaui-btn:focus-visible{background-color:var(--zaui-light-button-primary-background-pressed, #0250b6);background-color:var(--zaui-dark-button-primary-background-pressed, #0250b6);background:var(--zaui-dark-button-primary-background-pressed, #0250b6)}body[zaui-theme=dark] .zaui-btn-disabled[disabled],body[zaui-theme=dark] .zaui-btn-primary.zaui-btn-disabled[disabled],body[zaui-theme=dark] .zaui-btn-secondary.zaui-btn-disabled[disabled]{background-color:var(--zaui-dark-button-background-disabled, #36383a);color:var(--zaui-dark-button-text-disabled, #53575a)}body[zaui-theme=dark] .zaui-btn-tertiary.zaui-btn-disabled[disabled]{background-color:transparent;color:var(--zaui-dark-button-text-disabled, #53575a)}body[zaui-theme=dark] .zaui-btn-primary,body[zaui-theme=dark] .zaui-btn-primary.zaui-btn-highlight{background-color:var(--zaui-light-button-primary-background, #006af5);background-color:var(--zaui-dark-button-primary-background, #006af5);color:var(--zaui-light-button-primary-text, #ffffff)}body[zaui-theme=dark] .zaui-btn-primary:active,body[zaui-theme=dark] .zaui-btn-primary.zaui-btn-highlight:active,body[zaui-theme=dark] .zaui-btn-primary:focus-visible,body[zaui-theme=dark] .zaui-btn-primary.zaui-btn-highlight:focus-visible{background-color:var(--zaui-light-button-primary-background-pressed, #0250b6);background-color:var(--zaui-dark-button-primary-background-pressed, #0250b6)}body[zaui-theme=dark] .zaui-btn-secondary,body[zaui-theme=dark] .zaui-btn-secondary.zaui-btn-highlight{background-color:var(--zaui-light-button-secondary-background, #d6e9ff);background-color:var(--zaui-dark-button-secondary-background, #03316d);color:var(--zaui-light-button-secondary-text, #006af5)}body[zaui-theme=dark] .zaui-btn-secondary:active,body[zaui-theme=dark] .zaui-btn-secondary.zaui-btn-highlight:active,body[zaui-theme=dark] .zaui-btn-secondary:focus-visible,body[zaui-theme=dark] .zaui-btn-secondary.zaui-btn-highlight:focus-visible{background-color:var(--zaui-light-button-secondary-background-pressed, #b8d9ff)}body[zaui-theme=dark] .zaui-btn-tertiary,body[zaui-theme=dark] .zaui-btn-tertiary.zaui-btn-highlight{color:var(--zaui-light-button-tertiary-text, #006af5);background-color:transparent;color:var(--zaui-dark-button-tertiary-text, #52a0ff)}body[zaui-theme=dark] .zaui-btn-tertiary:active,body[zaui-theme=dark] .zaui-btn-tertiary.zaui-btn-highlight:active,body[zaui-theme=dark] .zaui-btn-tertiary:focus-visible,body[zaui-theme=dark] .zaui-btn-tertiary.zaui-btn-highlight:focus-visible{background-color:var(--zaui-light-button-tertiary-background-pressed, #ebf4ff);background-color:var(--zaui-dark-button-tertiary-background-pressed, #03316d)}body[zaui-theme=dark] .zaui-btn-danger,body[zaui-theme=dark] .zaui-btn-primary.zaui-btn-danger{background-color:var(--zaui-dark-button-primary-danger-background, #dc1f18);color:var(--zaui-dark-button-primary-danger-text, #ffffff)}body[zaui-theme=dark] .zaui-btn-danger:active,body[zaui-theme=dark] .zaui-btn-primary.zaui-btn-danger:active,body[zaui-theme=dark] .zaui-btn-danger:focus-visible,body[zaui-theme=dark] .zaui-btn-primary.zaui-btn-danger:focus-visible{background-color:var(--zaui-dark-button-primary-danger-background-pressed, #a51712)}body[zaui-theme=dark] .zaui-btn-secondary.zaui-btn-danger{background-color:var(--zaui-dark-button-secondary-danger-background, #650e0b);color:var(--zaui-dark-button-secondary-danger-text, #f89996)}body[zaui-theme=dark] .zaui-btn-secondary.zaui-btn-danger:active,body[zaui-theme=dark] .zaui-btn-secondary.zaui-btn-danger:focus-visible{background-color:var(--zaui-dark-button-secondary-danger-background-pressed, #490a08)}body[zaui-theme=dark] .zaui-btn-tertiary.zaui-btn-danger{background-color:transparent;color:var(--zaui-dark-button-tertiary-danger-text, #f1645f)}body[zaui-theme=dark] .zaui-btn-tertiary.zaui-btn-danger:active,body[zaui-theme=dark] .zaui-btn-tertiary.zaui-btn-danger:focus-visible{background-color:var(--zaui-dark-button-tertiary-danger-background-pressed, #650e0b)}body[zaui-theme=dark] .zaui-btn-secondary.zaui-btn-neutral{background-color:var(--zaui-dark-button-secondary-neutral-background, #36383a);color:var(--zaui-dark-button-secondary-neutral-text, #ffffff)}body[zaui-theme=dark] .zaui-btn-secondary.zaui-btn-neutral:active,body[zaui-theme=dark] .zaui-btn-secondary.zaui-btn-neutral:focus-visible{background-color:var(--zaui-dark-button-secondary-neutral-background-pressed, #252627)}body[zaui-theme=dark] .zaui-btn-tertiary.zaui-btn-neutral{background-color:transparent;color:var(--zaui-dark-button-tertiary-neutral-text, #ffffff)}body[zaui-theme=dark] .zaui-btn-tertiary.zaui-btn-neutral:active,body[zaui-theme=dark] .zaui-btn-tertiary.zaui-btn-neutral:focus-visible{background-color:var(--zaui-dark-button-tertiary-neutral-background-pressed, #36383a)}:root{--zaui-safe-area-inset-top: env(safe-area-inset-top, 0px);--zaui-safe-area-inset-bottom: env(safe-area-inset-bottom, 0px);--zaui-safe-area-inset-left: env(safe-area-inset-left, 0px);--zaui-safe-area-inset-right: env(safe-area-inset-right, 0px)}*{box-sizing:border-box}html{height:100%;overflow:hidden;position:relative;-webkit-tap-highlight-color:rgba(0,0,0,0)}body{height:100%;overflow:auto;position:relative;margin:0;padding:0;width:100%;-webkit-text-size-adjust:100%;-webkit-font-smoothing:antialiased;font-size:15px;font-weight:400;line-height:20px;font-family:-apple-system,system-ui,BlinkMacSystemFont,Segoe UI,Roboto,Helvetica Neue,Arial,sans-serif;background-color:var(--zaui-light-body-background-color, #e9ebed);color:var(--zaui-light-text-color, #141415)}body[zaui-theme=dark]{background-color:var(--zaui-light-body-background-color, #e9ebed);color:var(--zaui-light-text-color, #141415);background-color:var(--zaui-dark-body-background-color, #000000e6);color:var(--zaui-dark-text-color, #f4f5f6)}@font-face{font-family:zaui-icons;font-style:normal;font-weight:400;src:url(https://h5.zadn.vn/static/fonts/ZMPIcons-Regular-v3.woff2);src:url(https://h5.zadn.vn/static/fonts/ZMPIcons-Regular-v3.woff2) format("woff2"),url(https://h5.zadn.vn/static/fonts/ZMPIcons-Regular-v3.woff) format("woff"),url(https://h5.zadn.vn/static/fonts/ZMPIcons-Regular-v3.ttf) format("truetype")}.zaui-icon{vertical-align:middle;background-size:100% auto;background-position:center;background-repeat:no-repeat;position:relative;font-family:zaui-icons;font-weight:400;font-style:normal;font-size:24px;width:1em;height:1em;line-height:1;letter-spacing:normal;text-transform:none;display:inline-flex;white-space:nowrap;word-wrap:normal;-webkit-font-smoothing:antialiased;text-rendering:optimizeLegibility;-moz-osx-font-smoothing:grayscale;-moz-font-feature-settings:"liga";font-feature-settings:"liga";text-align:center}.zaui-list{box-sizing:border-box;margin:0;padding:0;list-style:none}.zaui-list-no-spacing.zaui-list .zaui-list-item:not(:last-child){margin-bottom:0}.zaui-list .zaui-list-item:not(:last-child){margin-bottom:24px}.zaui-list .zaui-list-item:not(:last-child) .zaui-list-item-right:after{content:"";height:.5px;background-color:var(--zaui-light-list-item-divider-color, #e9ebed);width:100%;position:absolute;bottom:-16px;left:0}.zaui-list-no-divider>.zaui-list-item>.zaui-list-item-right:after{display:none}.zaui-list-item{padding:16px;position:relative;display:flex;flex-direction:row}.zaui-list-item-subtitle{color:var(--zaui-light-list-item-subtitle-color, #767a7f);font-size:14px;line-height:18px}.zaui-list-item-brackets{color:var(--zaui-light-list-item-brackets-color, #767a7f);display:inline-block;margin-left:4px;font-size:15px;line-height:20px}.zaui-list-item-title{display:inline-block;font-size:16px;line-height:22px}.zaui-list-item-title-container{white-space:nowrap;overflow:hidden}.zaui-list-item-content{flex:1;overflow-wrap:anywhere}.zaui-list-item-prefix{margin-right:16px}.zaui-list-item-suffix{margin-left:16px}.zaui-list-item-right{position:relative;flex:1;display:flex;flex-direction:row;flex-wrap:nowrap}body[zaui-theme=dark] .zaui-list{box-sizing:border-box;margin:0;padding:0;list-style:none}body[zaui-theme=dark] .zaui-list .zaui-list-item-subtitle{color:var(--zaui-dark-list-item-subtitle-color, #8f9499)}body[zaui-theme=dark] .zaui-list .zaui-list-item-brackets{color:var(--zaui-dark-list-item-brackets-color, #8f9499)}body[zaui-theme=dark] .zaui-list .zaui-list-item:not(:last-child) .zaui-list-item-right:after{background-color:var(--zaui-dark-list-item-divider-color, #36383a)}.zaui-input{font-size:16px;font-weight:400;line-height:22px;font-family:inherit;background-color:transparent;color:var(--zaui-light-input-text-color, #141415);border-radius:8px;height:48px;width:100%;border:1px solid var(--zaui-light-input-border-color, #b9bdc1);padding:0 12px;margin:4px 0;box-sizing:border-box;-webkit-tap-highlight-color:transparent;outline:none}.zaui-input::-moz-placeholder{color:var(--zaui-light-input-placeholder-color, #b9bdc1)}.zaui-input::-webkit-input-placeholder{color:var(--zaui-light-input-placeholder-color, #b9bdc1)}.zaui-input::placeholder{color:var(--zaui-light-input-placeholder-color, #b9bdc1)}.zaui-input:-ms-input-placeholder{color:var(--zaui-light-input-placeholder-color, #b9bdc1)}.zaui-input::-ms-input-placeholder{color:var(--zaui-light-input-placeholder-color, #b9bdc1)}.zaui-input-wrapper{display:flex;flex-direction:column;flex-wrap:nowrap;align-items:flex-start;justify-content:center;color:var(--zaui-light-input-text-color, #141415)}.zaui-input-label{font-size:14px;line-height:18px;white-space:nowrap;overflow:hidden;display:inline-block}.zaui-input-helper-text{display:flex;flex-direction:row;flex-wrap:nowrap;align-items:flex-start;justify-content:flex-start;font-size:13px;line-height:18px;color:var(--zaui-light-input-helper-text-color, #767a7f)}.zaui-input-helper-text-icon{padding-right:0;margin-right:4px;margin-top:1px}.zaui-input-helper-text-content{flex:1;display:inline-flex;line-height:18px}.zaui-input-helper-text-has-error{color:var(--zaui-light-input-error-text-color, #dc1f18)}.zaui-input-helper-text-hidden{display:none}.zaui-input-clear-btn{display:flex;flex-direction:row;flex-wrap:nowrap;align-items:center;justify-content:center;margin-right:12px}.zaui-input-clear-btn-hidden{display:none}.zaui-input-clear-icon{color:var(--zaui-light-input-clear-icon-color, #141415)}.zaui-input:not(:focus)~.zaui-input-suffix .zaui-input-clear-icon-display-mode-focus{visibility:hidden}.zaui-input-status-success{display:flex;flex-direction:row;flex-wrap:nowrap;align-items:center;justify-content:center;padding-right:12px}.zaui-input-status-success-icon{color:var(--zaui-light-input-status-success-icon-color, #34b764)}.zaui-input-password-icon{margin-right:12px}.zaui-input-small{min-width:unset;height:32px}.zaui-input-medium{min-width:unset;height:48px}.zaui-input-large{min-width:unset;height:56px}.zaui-input:hover,.zaui-input:focus,.zaui-input:focus-visible{border-color:var(--zaui-light-input-hover-border-color, #006af5)}.zaui-input:hover.zaui-input-status-error,.zaui-input:focus.zaui-input-status-error,.zaui-input:focus-visible.zaui-input-status-error{border-color:var(--zaui-light-input-status-error, #dc1f18)}.zaui-input-status-error{border-color:var(--zaui-light-input-status-error, #dc1f18)}.zaui-input-disabled{color:var(--zaui-light-input-disabled-color, #767a7f);background-color:var(--zaui-light-input-disabled-background-color, #0000001a);border:none}.zaui-input-affix-wrapper{background-color:var(--zaui-light-input-background-color, #ffffff);display:flex;flex-direction:row;flex-wrap:nowrap;align-items:center;justify-content:flex-start;position:relative;width:100%;border:1px solid var(--zaui-light-input-border-color, #b9bdc1);border-radius:8px;margin:4px 0}.zaui-input-affix-wrapper-focused{border-color:var(--zaui-light-input-hover-border-color, #006af5)}.zaui-input-affix-wrapper .zaui-input{border:none;margin:0}.zaui-input-affix-wrapper .zaui-input-suffix{display:flex;flex-direction:row;flex-wrap:nowrap;align-items:center;justify-content:center}.zaui-input-affix-wrapper-status-error{border-color:var(--zaui-light-input-status-error, #dc1f18)}.zaui-input-affix-wrapper-disabled{border:none;color:var(--zaui-light-input-disabled-color, #767a7f);background-color:var(--zaui-light-input-disabled-background-color, #0000001a)}.zaui-input-affix-wrapper-disabled .zaui-input-disabled,.zaui-input-affix-wrapper-disabled .zaui-input-suffix{background-color:transparent}.zaui-input-affix-wrapper:hover,.zaui-input-affix-wrapper:focus,.zaui-input-affix-wrapper:focus-visible{border-color:var(--zaui-light-input-hover-border-color, #006af5)}.zaui-input-affix-wrapper:hover.zaui-input-affix-wrapper-status-error,.zaui-input-affix-wrapper:focus.zaui-input-affix-wrapper-status-error,.zaui-input-affix-wrapper:focus-visible.zaui-input-affix-wrapper-status-error{border-color:var(--zaui-light-input-status-error, #dc1f18)}.zaui-input-textarea{resize:none;padding:12px;font-family:inherit}.zaui-input-textarea::-webkit-resizer{-webkit-appearance:none}.zaui-input-textarea-min-height{min-height:120px}.zaui-input-textarea::-webkit-scrollbar{display:none}.zaui-input-textarea-affix-wrapper{align-items:flex-start;padding:12px 0}.zaui-input-textarea-affix-wrapper .zaui-input-textarea{padding-top:0;padding-bottom:0}.zaui-input-textarea~.zaui-input-suffix .zaui-input-show-count-suffix{position:absolute;bottom:12px;right:12px}.zaui-input-textarea-has-show-count{margin-bottom:24px!important}.zaui-input-otp{position:relative;box-sizing:border-box;display:inline-flex;justify-content:center;align-items:center;width:52px;height:56px;padding:11px 16px;font-weight:500;font-size:27px;line-height:34px;-webkit-user-select:none;-moz-user-select:none;-ms-user-select:none;user-select:none}.zaui-input-otp:hover,.zaui-input-otp:focus,.zaui-input-otp:focus-visible{border-color:var(--zaui-light-input-border-color, #b9bdc1)}.zaui-input-otp-wrapper{position:relative}.zaui-input-otp-hidden-input{position:absolute;width:100%;opacity:0;height:0}.zaui-input-otp-list{display:flex}.zaui-input-otp-list .zaui-input-otp:not(:last-child){margin-right:8px}.zaui-input-otp-active{border-color:var(--zaui-light-input-hover-border-color, #006af5);border-width:2px}.zaui-input-otp-active:hover,.zaui-input-otp-active:focus,.zaui-input-otp-active:focus-visible{border-color:var(--zaui-light-input-hover-border-color, #006af5)}.zaui-input-otp-cursor:after{position:absolute;content:"";width:1px;background:var(--zaui-light-input-hover-border-color, #006af5);height:32px;animation:cursorAnimate .9s infinite}.zaui-input-otp-dot{width:12px;height:12px;border-radius:50%;background:var(--zaui-light-input-text-color, #141415)}body[zaui-theme=dark] .zaui-input{font-size:16px;font-weight:400;line-height:22px;font-family:inherit;background-color:transparent;color:var(--zaui-light-input-text-color, #141415);border-radius:8px;height:48px;width:100%;border:1px solid var(--zaui-light-input-border-color, #b9bdc1);padding:0 12px;margin:4px 0;box-sizing:border-box;-webkit-tap-highlight-color:transparent;outline:none;color:var(--zaui-dark-input-text-color, #f4f5f6);border-color:var(--zaui-dark-input-border-color, #767a7f)}body[zaui-theme=dark] .zaui-input::-moz-placeholder{color:var(--zaui-light-input-placeholder-color, #b9bdc1)}body[zaui-theme=dark] .zaui-input::-webkit-input-placeholder{color:var(--zaui-light-input-placeholder-color, #b9bdc1)}body[zaui-theme=dark] .zaui-input::-ms-input-placeholder{color:var(--zaui-light-input-placeholder-color, #b9bdc1)}body[zaui-theme=dark] .zaui-input::placeholder{color:var(--zaui-light-input-placeholder-color, #b9bdc1)}body[zaui-theme=dark] .zaui-input::-moz-placeholder{color:var(--zaui-dark-input-placeholder-color, #53575a)}body[zaui-theme=dark] .zaui-input::-webkit-input-placeholder{color:var(--zaui-dark-input-placeholder-color, #53575a)}body[zaui-theme=dark] .zaui-input::placeholder{color:var(--zaui-dark-input-placeholder-color, #53575a)}body[zaui-theme=dark] .zaui-input:-ms-input-placeholder{color:var(--zaui-dark-input-placeholder-color, #53575a)}body[zaui-theme=dark] .zaui-input::-ms-input-placeholder{color:var(--zaui-dark-input-placeholder-color, #53575a)}body[zaui-theme=dark] .zaui-input-wrapper{color:var(--zaui-dark-input-text-color, #f4f5f6)}body[zaui-theme=dark] .zaui-input-affix-wrapper{background-color:var(--zaui-dark-input-background-color, #141415)}body[zaui-theme=dark] .zaui-input-affix-wrapper .zaui-input{border:none;margin:0}body[zaui-theme=dark] .zaui-input-affix-wrapper-focused{border-color:var(--zaui-dark-input-hover-border-color, #006af5)}body[zaui-theme=dark] .zaui-input-affix-wrapper-status-error{border-color:var(--zaui-dark-input-status-error, #dc1f18)}body[zaui-theme=dark] .zaui-input-affix-wrapper-disabled{color:var(--zaui-dark-input-disabled-color, #8f9499);background-color:var(--zaui-dark-input-disabled-background-color, #ffffff1a)}body[zaui-theme=dark] .zaui-input-affix-wrapper-disabled .zaui-input-disabled,body[zaui-theme=dark] .zaui-input-affix-wrapper-disabled .zaui-input-suffix{background-color:transparent}body[zaui-theme=dark] .zaui-input-suffix{background-color:var(--zaui-dark-input-background-color, #141415)}body[zaui-theme=dark] .zaui-input-helper-text{color:var(--zaui-dark-input-helper-text-color, #8f9499)}body[zaui-theme=dark] .zaui-input-helper-text-has-error{color:var(--zaui-dark-input-error-text-color, #dc1f18)}body[zaui-theme=dark] .zaui-input-clear-icon{color:var(--zaui-dark-input-clear-icon-color, #f4f5f6)}body[zaui-theme=dark] .zaui-input-status-success-icon{color:var(--zaui-dark-input-status-success-icon-color, #34b764)}body[zaui-theme=dark] .zaui-input-status-error{border-color:var(--zaui-dark-input-status-error, #dc1f18)}body[zaui-theme=dark] .zaui-input-disabled{color:var(--zaui-dark-input-disabled-color, #8f9499);background-color:var(--zaui-dark-input-disabled-background-color, #ffffff1a);border:none}body[zaui-theme=dark] .zaui-input:hover,body[zaui-theme=dark] .zaui-input:focus,body[zaui-theme=dark] .zaui-input:focus-visible{border-color:var(--zaui-dark-input-hover-border-color, #006af5)}body[zaui-theme=dark] .zaui-input:hover.zaui-input-affix-wrapper-status-error,body[zaui-theme=dark] .zaui-input:focus.zaui-input-affix-wrapper-status-error,body[zaui-theme=dark] .zaui-input:focus-visible.zaui-input-affix-wrapper-status-error{border-color:var(--zaui-dark-input-status-error, #dc1f18)}body[zaui-theme=dark] .zaui-input:hover~.zaui-input-suffix .zaui-input-status-success-icon,body[zaui-theme=dark] .zaui-input:focus-visible~.zaui-input-suffix .zaui-input-status-success-icon{color:var(--zaui-dark-input-status-success-icon-focus-visible-color, #f4f5f6)}body[zaui-theme=dark] .zaui-input-otp{width:52px;height:56px;padding:11px 16px;font-weight:500;font-size:27px;line-height:34px}body[zaui-theme=dark] .zaui-input-otp:hover,body[zaui-theme=dark] .zaui-input-otp:focus,body[zaui-theme=dark] .zaui-input-otp:focus-visible{border-color:var(--zaui-dark-input-border-color, #767a7f)}body[zaui-theme=dark] .zaui-input-otp-active{border-color:var(--zaui-dark-input-hover-border-color, #006af5);border-width:2px}body[zaui-theme=dark] .zaui-input-otp-active:hover,body[zaui-theme=dark] .zaui-input-otp-active:focus,body[zaui-theme=dark] .zaui-input-otp-active:focus-visible{border-color:var(--zaui-dark-input-hover-border-color, #006af5)}body[zaui-theme=dark] .zaui-input-otp-cursor:after{background:var(--zaui-dark-input-hover-border-color, #006af5)}body[zaui-theme=dark] .zaui-input-otp-dot{background:var(--zaui-dark-input-text-color, #f4f5f6)}.zaui-avatar{width:var(--zaui-avatar-size, 48px);height:var(--zaui-avatar-size, 48px);position:relative;border-radius:100%;display:inline-block;background:linear-gradient(45deg,#34b764,#99e5b5);vertical-align:middle;text-align:center;color:var(--zaui-avatar-text-color, #ffffff);font-size:15px;line-height:20px;font-weight:500}.zaui-avatar-online{-moz-box-sizing:content-box;-webkit-box-sizing:content-box;width:10px;height:10px;content:"";background-color:var(--zaui-light-avatar-status-online, #34b764);border:solid 2px var(--zaui-light-avatar-status-background, #ffffff);border-radius:100%;position:absolute;box-sizing:content-box;bottom:calc(calc(var(--zaui-avatar-size, 48px) * .1464) - 7.5px);right:calc(calc(var(--zaui-avatar-size, 48px) * .1464) - 7.5px)}.zaui-avatar-image{width:100%;height:100%;object-fit:cover;border-radius:100%;overflow:hidden;position:absolute;top:0;left:0}.zaui-avatar-inner{width:100%;height:100%;border-radius:100%;overflow:hidden;position:absolute;top:0;left:0;display:inline-flex;align-items:center;justify-content:center;background:inherit}.zaui-avatar-blocked{-moz-box-sizing:content-box;-webkit-box-sizing:content-box;width:12px;height:12px;border:solid 2px var(--zaui-light-avatar-status-background, #ffffff);border-radius:100%;position:absolute;box-sizing:content-box;bottom:calc(calc(var(--zaui-avatar-size, 48px) * .1464) - 9px);right:calc(calc(var(--zaui-avatar-size, 48px) * .1464) - 9px)}.zaui-avatar-blocked svg{position:absolute;left:0;top:0}.zaui-avatar-blocked path.zaui-avatar-block-icon-bg{fill:var(--zaui-light-avatar-status-background, #ffffff)}.zaui-avatar-story,.zaui-avatar-story-seen{z-index:1;cursor:pointer}.zaui-avatar-story .zaui-avatar-image,.zaui-avatar-story-seen .zaui-avatar-image,.zaui-avatar-story .zaui-avatar-inner,.zaui-avatar-story-seen .zaui-avatar-inner{width:calc(var(--zaui-avatar-size, 48px) - 8px);height:calc(var(--zaui-avatar-size, 48px) - 8px);left:4px;top:4px}.zaui-avatar-story:before,.zaui-avatar-story-seen:before{box-sizing:border-box;-moz-box-sizing:border-box;-webkit-box-sizing:border-box;position:absolute;content:"";border-radius:100%;z-index:-1;width:calc(var(--zaui-avatar-size, 48px) - 4px);height:calc(var(--zaui-avatar-size, 48px) - 4px);border:solid 2px var(--zaui-light-avatar-border, #ffffff);background:transparent;top:2px;left:2px}.zaui-avatar-story:after,.zaui-avatar-story-seen:after{position:absolute;box-sizing:border-box;-moz-box-sizing:border-box;-webkit-box-sizing:border-box;content:"";border-radius:100%;z-index:-2;top:0;left:0;width:var(--zaui-avatar-size, 48px);height:var(--zaui-avatar-size, 48px);background:var(--zaui-light-avatar-story-contour-color, linear-gradient(45deg, #006af5 0%, #5fcbf2 100%))}.zaui-avatar-story-seen:after{background:var(--zaui-light-avatar-story-seen, #d6d9dc)}.zaui-avatar-text-small{font-size:13px;line-height:18px}.zaui-avatar-text-xsmall{font-size:10px;line-height:14px}.zaui-avatar-text-medium{font-size:15px;line-height:20px}.zaui-avatar-text-large{font-size:18px;line-height:24px}.zaui-avatar-color-01{background:var(--zaui-avatar-background-gradient-01, linear-gradient(45deg, #006af5 0%, #8fc1ff 100%))}.zaui-avatar-color-02{background:var(--zaui-avatar-background-gradient-02, linear-gradient(45deg, #6a40bf 0%, #52a0ff 100%))}.zaui-avatar-color-03{background:var(--zaui-avatar-background-gradient-03, linear-gradient(45deg, #12aee2 0%, #66d68f 100%))}.zaui-avatar-color-04{background:var(--zaui-avatar-background-gradient-04, linear-gradient(45deg, #34b764 0%, #99e5b5 100%))}.zaui-avatar-group{display:flex;flex-direction:row;flex-wrap:wrap;max-width:56px;width:-moz-max-content;width:-webkit-max-content;width:max-content;justify-content:center;align-items:center;padding:2px;box-sizing:content-box;-moz-box-sizing:content-box;-webkit-box-sizing:content-box}.zaui-avatar-group.zaui-avatar-group-horizontal{flex-wrap:nowrap;max-width:unset;width:-moz-max-content;width:-webkit-max-content;width:max-content}.zaui-avatar-group.zaui-avatar-group-horizontal .zaui-avatar-group-item:not(:first-child){margin-left:-8px}.zaui-avatar-group.zaui-avatar-group-horizontal .zaui-avatar-group-item:nth-child(1){z-index:1;margin-top:0}.zaui-avatar-group.zaui-avatar-group-horizontal .zaui-avatar-group-item:nth-child(2){z-index:2;margin-top:0}.zaui-avatar-group.zaui-avatar-group-horizontal .zaui-avatar-group-item:nth-child(3),.zaui-avatar-group.zaui-avatar-group-horizontal .zaui-avatar-group-item:nth-child(3):last-child{z-index:3;margin-top:0}.zaui-avatar-group.zaui-avatar-group-horizontal .zaui-avatar-group-item:nth-child(4){z-index:4;margin-top:0}.zaui-avatar-group .zaui-avatar-group-item{border:solid 2px var(--zaui-light-avatar-border, #ffffff);flex-wrap:wrap}.zaui-avatar-group .zaui-avatar-group-item .zaui-avatar-inner{border:solid var(2px) var(--zaui-light-avatar-border, #ffffff)}.zaui-avatar-group .zaui-avatar-group-item:nth-child(1){z-index:2}.zaui-avatar-group .zaui-avatar-group-item:nth-child(2){z-index:3;margin-left:-4px}.zaui-avatar-group .zaui-avatar-group-item:nth-child(3){z-index:1;margin-top:-4px}.zaui-avatar-group .zaui-avatar-group-item:nth-child(3):last-child{margin-top:-8px}.zaui-avatar-group .zaui-avatar-group-item:nth-child(4){z-index:4;margin-left:-4px;margin-top:-4px}body[zaui-theme=dark] .zaui-avatar-online{background-color:var(--zaui-dark-avatar-status-online, #34b764);border:solid 2px var(--zaui-dark-avatar-status-background, #141415)}body[zaui-theme=dark] .zaui-avatar-blocked{border:solid 2px var(--zaui-dark-avatar-status-background, #141415)}body[zaui-theme=dark] .zaui-avatar-blocked path.zaui-avatar-block-icon-bg{fill:var(--zaui-dark-avatar-status-background, #141415)}body[zaui-theme=dark] .zaui-avatar-story:before,body[zaui-theme=dark] .zaui-avatar-story-seen:before{border:solid 2px var(--zaui-dark-avatar-border, #141415)}body[zaui-theme=dark] .zaui-avatar-story:after,body[zaui-theme=dark] .zaui-avatar-story-seen:after{background:var(--zaui-dark-avatar-story-contour-color, linear-gradient(45deg, #006af5 0%, #5fcbf2 100%))}body[zaui-theme=dark] .zaui-avatar-story-seen:after{background:var(--zaui-dark-avatar-story-seen, #53575a)}body[zaui-theme=dark] .zaui-avatar-group .zaui-avatar-group-item .zaui-avatar-inner{border:solid var(2px) var(--zaui-dark-avatar-border, #141415)}.zaui-modal-content{background:var(--zaui-light-modal-background, #ffffff);background-color:var(--zaui-light-modal-background, #ffffff);border-radius:12px;position:relative;margin:auto;width:calc(100vw - 64px);max-width:480px;max-height:80%;box-sizing:border-box;font-size:15px;font-weight:400;line-height:20px;overflow:hidden;display:flex;flex-direction:column}.zaui-modal-content .zaui-modal-content-title{font-size:20px;font-weight:500;line-height:26px;text-align:center;margin-bottom:12px}.zaui-modal-content-main{padding:24px;overflow-y:auto}.zaui-modal-content-cover{width:100%;height:-moz-fit-content;height:-webkit-fit-content;height:fit-content}.zaui-modal-content-cover img{width:100%;height:auto;display:block}.zaui-modal-content-actions{display:flex;flex-direction:row;justify-content:flex-end;padding:12px 16px;position:relative}.zaui-modal-content-actions:before{width:calc(100% - 48px);height:1px;position:absolute;content:"";left:24px;top:0;background:var(--zaui-light-modal-divider, #e9ebed)}.zaui-modal-content-actions-no-divider:before{display:none}.zaui-modal-content-actions .zaui-modal-content-action{min-width:unset;padding:8px}.zaui-modal-content-actions .zaui-modal-content-action:active,.zaui-modal-content-actions .zaui-modal-content-action:focus,.zaui-modal-content-actions .zaui-modal-content-action:hover{background-color:transparent}.zaui-modal-content-actions .zaui-modal-content-action:not(:last-child){margin-right:8px;width:-moz-max-content;width:-webkit-max-content;width:max-content}.zaui-modal-content-actions-vertical{flex-direction:column;align-items:flex-end;justify-content:center}.zaui-modal-content-actions-vertical .zaui-modal-content-action:not(:last-child){margin-right:0}.zaui-modal-content-enter,.zaui-modal-content-appear{opacity:0;animation-fill-mode:both;transition-duration:.3s;animation-duration:.3s;animation-timing-function:cubic-bezier(.08,.82,.17,1);animation-play-state:paused}.zaui-modal-content-exit{animation-fill-mode:both;transition-duration:.3s;animation-duration:.3s;animation-timing-function:cubic-bezier(.6,.04,.98,.34);animation-play-state:paused}.zaui-modal-content-enter-active,.zaui-modal-content-appear-active{animation-name:zoomIn;animation-play-state:running}.zaui-modal-content-exit-active{animation-name:zoomOut;animation-play-state:running}body[zaui-theme=dark] .zaui-modal-content{background:var(--zaui-dark-modal-background, #252627);background-color:var(--zaui-dark-modal-background, #252627)}body[zaui-theme=dark] .zaui-modal-content-actions:before{background:var(--zaui-dark-modal-divider, #36383a)}.zaui-sheet-wrapper{overflow:hidden;display:block;height:100%;width:100%}.zaui-sheet-content.zaui-sheet-content-hug-content{height:auto;min-height:unset}.zaui-sheet-content{background:var(--zaui-light-sheet-container, #ffffff);background-color:var(--zaui-light-sheet-container, #ffffff);border-radius:16px 16px 0 0;position:absolute;bottom:0;left:0;width:100%;min-height:50%;max-height:100vh;box-sizing:border-box;font-size:15px;font-weight:400;line-height:20px;overflow:hidden;display:flex;flex-direction:column;box-shadow:0 -2px 6px #14141524;touch-action:none;padding-bottom:var(--zaui-safe-area-inset-bottom)}.zaui-sheet-content .zaui-sheet-content-handler-wrapper{height:24px;display:flex;justify-content:center;align-items:center}.zaui-sheet-content .zaui-sheet-content-handler-wrapper .zaui-sheet-content-handler{display:inline-block;width:48px;height:6px;border-radius:3px;background-color:var(--zaui-light-sheet-handler, #e9ebed);margin:9px 0}.zaui-sheet-content .zaui-sheet-content-header{display:flex;flex-direction:row;height:48px;width:100%;align-items:center}.zaui-sheet-content .zaui-sheet-content-header .zaui-sheet-content-title{font-size:20px;font-weight:500;line-height:26px;text-align:center;flex:1}.zaui-sheet-content-main{padding:24px;overflow-y:auto}.zaui-sheet-content-actions{display:flex;flex-direction:row;justify-content:flex-end;padding:12px 16px;position:relative}.zaui-sheet-content-actions:before{width:calc(100% - 48px);height:1px;position:absolute;content:"";left:24px;top:0;background:var(--zaui-light-sheet-divider, #e9ebed)}.zaui-sheet-content-actions-no-divider:before{display:none}.zaui-sheet-content-actions .zaui-sheet-content-action{min-width:unset;padding:8px}.zaui-sheet-content-actions .zaui-sheet-content-action:not(:last-child){margin-right:8px;width:-moz-max-content;width:-webkit-max-content;width:max-content}.zaui-sheet-content-actions-vertical{flex-direction:column;align-items:flex-end;justify-content:center}.zaui-sheet-content-actions-vertical .zaui-sheet-content-action:not(:last-child){margin-right:0}.zaui-sheet-content-enter,.zaui-sheet-content-appear{transform:translateY(100%);animation-duration:.3s;animation-fill-mode:both;animation-timing-function:ease-in;animation-play-state:paused}.zaui-sheet-content-exit{animation-duration:.3s;animation-fill-mode:both;transition-duration:.2s;animation-timing-function:ease-out;animation-play-state:paused}.zaui-sheet-content-enter-active,.zaui-sheet-content-appear-active{animation-name:slideUp;animation-play-state:running}.zaui-sheet-content-exit-active{animation-name:slideDown;animation-play-state:running}.zaui-action-sheet .zaui-sheet-content{height:-moz-max-content;height:-webkit-max-content;height:max-content;min-height:unset;padding-bottom:var(--zaui-safe-area-inset-bottom)}.zaui-action-sheet-title-wrapper{padding:16px}.zaui-action-sheet-title-wrapper .zaui-action-sheet-title{color:var(--zaui-light-action-sheet-title, #767a7f);font-size:14px;line-height:18px;padding:0;text-align:center;overflow:hidden;text-overflow:ellipsis;white-space:initial;display:-webkit-box;-webkit-box-orient:vertical;-webkit-line-clamp:2}.zaui-action-sheet-actions-groups{flex:1}.zaui-action-sheet-actions-group{list-style-type:none;margin:0;padding:0}.zaui-action-sheet-actions-group:first-child{border-top:1px solid var(--zaui-light-action-sheet-divider, #e9ebed)}.zaui-action-sheet-actions-group:not(:first-child){border-top:4px solid var(--zaui-light-action-sheet-divider, #e9ebed)}.zaui-action-sheet-action{list-style-type:none;height:56px}.zaui-action-sheet-action:not(:last-child){border-bottom:1px solid var(--zaui-light-action-sheet-divider, #e9ebed)}.zaui-action-sheet-action-button.zaui-btn{border-radius:0;height:100%;width:100%;font-size:16px;font-weight:500;line-height:22px}body[zaui-theme=dark] .zaui-sheet-content{background:var(--zaui-dark-sheet-container, #252627);background-color:var(--zaui-dark-sheet-container, #252627)}body[zaui-theme=dark] .zaui-sheet-content-actions:before{background:var(--zaui-dark-sheet-divider, #36383a)}body[zaui-theme=dark] .zaui-sheet-content .zaui-sheet-content-handler-wrapper .zaui-sheet-content-handler{background-color:var(--zaui-dark-sheet-handler, #36383a)}.zaui-tabs{overflow:hidden;position:relative;width:100%}.zaui-tabs-content{display:flex;width:100%}.zaui-tabs-content-holder{flex:auto}.zaui-tabs-tabpane{width:100%;flex:none}.zaui-tabs-tabbar{display:flex;justify-content:flex-start;align-items:center;width:100%;background-color:var(--zaui-light-tabbar-background, #ffffff);position:relative}.zaui-tabs-tabbar.zaui-tabs-tabbar-scrollable{overflow-x:auto}.zaui-tabs-tabbar.zaui-tabs-tabbar-scrollable .zaui-tabs-tabbar-item{text-overflow:unset;overflow:unset;max-width:unset;min-width:unset}.zaui-tabs-tabbar:after{height:.5px;width:var(--zaui-tabbar-width, 100%);position:absolute;content:"";background-color:var(--zaui-light-tabbar-divider, #e9ebed);bottom:0;left:0;z-index:1}.zaui-tabs-tabbar::-webkit-scrollbar{display:none}.zaui-tabs-tabbar-center{justify-content:center}.zaui-tabs-tabbar-item{padding:12px 16px;font-size:15px;font-weight:400;line-height:20px;color:var(--zaui-light-tabbar-label, #767a7f);white-space:nowrap;display:block;text-overflow:ellipsis;overflow:hidden;max-width:200px;min-width:72px}.zaui-tabs-tabbar-item:before{display:block;font-weight:500;height:1px;font-size:15px;line-height:20px;white-space:nowrap;color:transparent;overflow:hidden;visibility:hidden}.zaui-tabs-tabbar-item.zaui-tabs-tabbar-item-active{font-weight:500;color:var(--zaui-light-tabbar-label-active, #141415);position:relative}.zaui-tabs-tabbar-item:focus,.zaui-tabs-tabbar-item:active{outline:none}.zaui-tabs-tabbar-active-line{bottom:0;height:2px;border-radius:2px 2px 0 0;content:"";position:absolute;background-color:var(--zaui-light-tabbar-active-line, #006af5);z-index:2;transition:left .3s ease-in-out,width .3s .1s}.zaui-tabs-bottom .zaui-tabs-tabbar .zaui-tabs-tabbar-active-line{border-radius:0 0 2px 2px;bottom:unset;top:0}body[zaui-theme=dark] .zaui-tabs-tabbar{background-color:var(--zaui-dark-tabbar-background, #141415)}body[zaui-theme=dark] .zaui-tabs-tabbar:after{background-color:var(--zaui-dark-tabbar-divider, #36383a)}body[zaui-theme=dark] .zaui-tabs-tabbar-item{color:var(--zaui-dark-tabbar-label, #8f9499)}body[zaui-theme=dark] .zaui-tabs-tabbar-item.zaui-tabs-tabbar-item-active{color:var(--zaui-dark-tabbar-label-active, #f4f5f6)}body[zaui-theme=dark] .zaui-tabs-tabbar-active-line{background-color:var(--zaui-dark-tabbar-active-line, #006af5)}.zaui-routes{display:grid;overflow:hidden}.zaui-routes .zaui-routes-item{grid-area:1 / 1 / 2 / 2;max-width:100vw;max-height:100vh;box-sizing:border-box;overflow-x:hidden;overflow-y:auto}.zaui-routes .zaui-routes-item:not(:only-child).zaui-routes-forward-enter-active,.zaui-routes .zaui-routes-item:not(:only-child).zaui-routes-forward-exit-active,.zaui-routes .zaui-routes-item:not(:only-child).zaui-routes-backward-enter-active,.zaui-routes .zaui-routes-item:not(:only-child).zaui-routes-backward-exit-active{transition:transform .4s ease}.zaui-routes .zaui-routes-item:not(:only-child).zaui-routes-no-animation-enter-active,.zaui-routes .zaui-routes-item:not(:only-child).zaui-routes-no-animation-exit-active{transition:transform .2s ease}.zaui-routes .zaui-routes-no-animation-enter{transform:translate(0);opacity:0}.zaui-routes .zaui-routes-no-animation-enter-active{opacity:1}.zaui-routes .zaui-routes-no-animation-exit{transform:translate(0);opacity:1}.zaui-routes .zaui-routes-no-animation-exit-active{opacity:0}.zaui-routes .zaui-routes-backward-enter{transform:translate(-100%)}.zaui-routes .zaui-routes-backward-enter-active,.zaui-routes .zaui-routes-backward-exit{transform:translate(0)}.zaui-routes .zaui-routes-backward-exit-active,.zaui-routes .zaui-routes-forward-enter{transform:translate(100%)}.zaui-routes .zaui-routes-forward-enter-active,.zaui-routes .zaui-routes-forward-exit{transform:translate(0)}.zaui-routes .zaui-routes-forward-exit-active{transform:translate(-100%)}.zaui-page{overflow:auto;-webkit-overflow-scrolling:touch;min-height:100vh;box-sizing:border-box;height:100%;width:100%}.zaui-page-hide-scrollbar{-ms-overflow-style:none;scrollbar-width:none}.zaui-page-hide-scrollbar::-webkit-scrollbar{display:none}.zaui-page.disable-scrolling{overflow:hidden}.zaui-text{display:block;font-weight:400;font-size:15px;line-height:20px}.zaui-text-bold{font-weight:500}.zaui-text-xLarge{font-size:18px;line-height:24px}.zaui-text-large{font-size:16px;line-height:22px}.zaui-text-normal{font-size:15px;line-height:20px}.zaui-text-small{font-size:14px;line-height:18px}.zaui-text-xSmall{font-size:13px;line-height:18px}.zaui-text-xxSmall{font-size:12px;line-height:16px}.zaui-text-xxxSmall{font-size:11px;line-height:16px}.zaui-text-xxxxSmall{font-size:10px;line-height:14px}.zaui-text-header{font-weight:500;display:block;font-size:16px;line-height:22px}.zaui-text-header-normal{font-size:16px;line-height:22px}.zaui-text-header-small{font-size:15px;line-height:22px}.zaui-text-title{display:block;font-size:18px;line-height:24px;font-weight:500}.zaui-text-title-normal{font-size:18px;line-height:24px}.zaui-text-title-xLarge{font-size:22px;line-height:26px}.zaui-text-title-large{font-size:20px;line-height:26px}.zaui-text-title-small{font-size:15px;line-height:20px}.zaui-box{padding:0;margin:0;display:block}.zaui-box-justify-flex-start{justify-content:flex-start}.zaui-box-justify-flex-end{justify-content:flex-end}.zaui-box-justify-center{justify-content:center}.zaui-box-justify-space-between{justify-content:space-between}.zaui-box-justify-space-around{justify-content:space-around}.zaui-box-justify-space-evenly{justify-content:space-evenly}.zaui-box-justify-initial{justify-content:initial}.zaui-box-align-items-flex-start{align-items:flex-start}.zaui-box-align-items-flex-end{align-items:flex-end}.zaui-box-align-items-stretch{align-items:stretch}.zaui-box-align-items-baseline{align-items:baseline}.zaui-box-align-items-center{align-items:center}.zaui-box-align-items-initial{align-items:initial}.zaui-box-align-content-stretch{align-items:stretch}.zaui-box-align-content-center{align-items:center}.zaui-box-align-content-flex-start{align-items:flex-start}.zaui-box-align-content-flex-end{align-items:flex-end}.zaui-box-align-content-space-between{align-items:space-between}.zaui-box-align-content-space-around{align-items:space-around}.zaui-box-align-content-space-evenly{align-items:space-evenly}.zaui-box-align-content-initial{align-items:initial}.zaui-box-text-align-left{text-align:left}.zaui-box-text-align-right{text-align:right}.zaui-box-text-align-center{text-align:center}.zaui-box-text-align-justify{text-align:justify}.zaui-box-text-align-initial{text-align:initial}.zaui-box-text-align-inherit{text-align:inherit}.zaui-box-vertical-align-baseline{vertical-align:baseline}.zaui-box-vertical-align-sub{vertical-align:sub}.zaui-box-vertical-align-super{vertical-align:super}.zaui-box-vertical-align-top{vertical-align:top}.zaui-box-vertical-align-text-top{vertical-align:text-top}.zaui-box-vertical-align-middle{vertical-align:middle}.zaui-box-vertical-align-bottom{vertical-align:bottom}.zaui-box-vertical-align-text-bottom{vertical-align:text-bottom}.zaui-box-vertical-align-initial{vertical-align:initial}.zaui-box-vertical-align-inherit{vertical-align:inherit}.zaui-box-m-0{margin:0}.zaui-box-mt-0{margin-top:0}.zaui-box-ml-0{margin-left:0}.zaui-box-mb-0{margin-bottom:0}.zaui-box-mr-0{margin-right:0}.zaui-box-mx-0{margin-left:0;margin-right:0}.zaui-box-my-0{margin-top:0;margin-bottom:0}.zaui-box-p-0{padding:0}.zaui-box-pt-0{padding-top:0}.zaui-box-pl-0{padding-left:0}.zaui-box-pb-0{padding-bottom:0}.zaui-box-pr-0{padding-right:0}.zaui-box-px-0{padding-left:0;padding-right:0}.zaui-box-py-0{padding-top:0;padding-bottom:0}.zaui-box-m-1{margin:4px}.zaui-box-mt-1{margin-top:4px}.zaui-box-ml-1{margin-left:4px}.zaui-box-mb-1{margin-bottom:4px}.zaui-box-mr-1{margin-right:4px}.zaui-box-mx-1{margin-left:4px;margin-right:4px}.zaui-box-my-1{margin-top:4px;margin-bottom:4px}.zaui-box-p-1{padding:4px}.zaui-box-pt-1{padding-top:4px}.zaui-box-pl-1{padding-left:4px}.zaui-box-pb-1{padding-bottom:4px}.zaui-box-pr-1{padding-right:4px}.zaui-box-px-1{padding-left:4px;padding-right:4px}.zaui-box-py-1{padding-top:4px;padding-bottom:4px}.zaui-box-m-2{margin:8px}.zaui-box-mt-2{margin-top:8px}.zaui-box-ml-2{margin-left:8px}.zaui-box-mb-2{margin-bottom:8px}.zaui-box-mr-2{margin-right:8px}.zaui-box-mx-2{margin-left:8px;margin-right:8px}.zaui-box-my-2{margin-top:8px;margin-bottom:8px}.zaui-box-p-2{padding:8px}.zaui-box-pt-2{padding-top:8px}.zaui-box-pl-2{padding-left:8px}.zaui-box-pb-2{padding-bottom:8px}.zaui-box-pr-2{padding-right:8px}.zaui-box-px-2{padding-left:8px;padding-right:8px}.zaui-box-py-2{padding-top:8px;padding-bottom:8px}.zaui-box-m-3{margin:12px}.zaui-box-mt-3{margin-top:12px}.zaui-box-ml-3{margin-left:12px}.zaui-box-mb-3{margin-bottom:12px}.zaui-box-mr-3{margin-right:12px}.zaui-box-mx-3{margin-left:12px;margin-right:12px}.zaui-box-my-3{margin-top:12px;margin-bottom:12px}.zaui-box-p-3{padding:12px}.zaui-box-pt-3{padding-top:12px}.zaui-box-pl-3{padding-left:12px}.zaui-box-pb-3{padding-bottom:12px}.zaui-box-pr-3{padding-right:12px}.zaui-box-px-3{padding-left:12px;padding-right:12px}.zaui-box-py-3{padding-top:12px;padding-bottom:12px}.zaui-box-m-4{margin:16px}.zaui-box-mt-4{margin-top:16px}.zaui-box-ml-4{margin-left:16px}.zaui-box-mb-4{margin-bottom:16px}.zaui-box-mr-4{margin-right:16px}.zaui-box-mx-4{margin-left:16px;margin-right:16px}.zaui-box-my-4{margin-top:16px;margin-bottom:16px}.zaui-box-p-4{padding:16px}.zaui-box-pt-4{padding-top:16px}.zaui-box-pl-4{padding-left:16px}.zaui-box-pb-4{padding-bottom:16px}.zaui-box-pr-4{padding-right:16px}.zaui-box-px-4{padding-left:16px;padding-right:16px}.zaui-box-py-4{padding-top:16px;padding-bottom:16px}.zaui-box-m-5{margin:20px}.zaui-box-mt-5{margin-top:20px}.zaui-box-ml-5{margin-left:20px}.zaui-box-mb-5{margin-bottom:20px}.zaui-box-mr-5{margin-right:20px}.zaui-box-mx-5{margin-left:20px;margin-right:20px}.zaui-box-my-5{margin-top:20px;margin-bottom:20px}.zaui-box-p-5{padding:20px}.zaui-box-pt-5{padding-top:20px}.zaui-box-pl-5{padding-left:20px}.zaui-box-pb-5{padding-bottom:20px}.zaui-box-pr-5{padding-right:20px}.zaui-box-px-5{padding-left:20px;padding-right:20px}.zaui-box-py-5{padding-top:20px;padding-bottom:20px}.zaui-box-m-6{margin:24px}.zaui-box-mt-6{margin-top:24px}.zaui-box-ml-6{margin-left:24px}.zaui-box-mb-6{margin-bottom:24px}.zaui-box-mr-6{margin-right:24px}.zaui-box-mx-6{margin-left:24px;margin-right:24px}.zaui-box-my-6{margin-top:24px;margin-bottom:24px}.zaui-box-p-6{padding:24px}.zaui-box-pt-6{padding-top:24px}.zaui-box-pl-6{padding-left:24px}.zaui-box-pb-6{padding-bottom:24px}.zaui-box-pr-6{padding-right:24px}.zaui-box-px-6{padding-left:24px;padding-right:24px}.zaui-box-py-6{padding-top:24px;padding-bottom:24px}.zaui-box-m-7{margin:28px}.zaui-box-mt-7{margin-top:28px}.zaui-box-ml-7{margin-left:28px}.zaui-box-mb-7{margin-bottom:28px}.zaui-box-mr-7{margin-right:28px}.zaui-box-mx-7{margin-left:28px;margin-right:28px}.zaui-box-my-7{margin-top:28px;margin-bottom:28px}.zaui-box-p-7{padding:28px}.zaui-box-pt-7{padding-top:28px}.zaui-box-pl-7{padding-left:28px}.zaui-box-pb-7{padding-bottom:28px}.zaui-box-pr-7{padding-right:28px}.zaui-box-px-7{padding-left:28px;padding-right:28px}.zaui-box-py-7{padding-top:28px;padding-bottom:28px}.zaui-box-m-8{margin:32px}.zaui-box-mt-8{margin-top:32px}.zaui-box-ml-8{margin-left:32px}.zaui-box-mb-8{margin-bottom:32px}.zaui-box-mr-8{margin-right:32px}.zaui-box-mx-8{margin-left:32px;margin-right:32px}.zaui-box-my-8{margin-top:32px;margin-bottom:32px}.zaui-box-p-8{padding:32px}.zaui-box-pt-8{padding-top:32px}.zaui-box-pl-8{padding-left:32px}.zaui-box-pb-8{padding-bottom:32px}.zaui-box-pr-8{padding-right:32px}.zaui-box-px-8{padding-left:32px;padding-right:32px}.zaui-box-py-8{padding-top:32px;padding-bottom:32px}.zaui-box-m-9{margin:36px}.zaui-box-mt-9{margin-top:36px}.zaui-box-ml-9{margin-left:36px}.zaui-box-mb-9{margin-bottom:36px}.zaui-box-mr-9{margin-right:36px}.zaui-box-mx-9{margin-left:36px;margin-right:36px}.zaui-box-my-9{margin-top:36px;margin-bottom:36px}.zaui-box-p-9{padding:36px}.zaui-box-pt-9{padding-top:36px}.zaui-box-pl-9{padding-left:36px}.zaui-box-pb-9{padding-bottom:36px}.zaui-box-pr-9{padding-right:36px}.zaui-box-px-9{padding-left:36px;padding-right:36px}.zaui-box-py-9{padding-top:36px;padding-bottom:36px}.zaui-box-m-10{margin:40px}.zaui-box-mt-10{margin-top:40px}.zaui-box-ml-10{margin-left:40px}.zaui-box-mb-10{margin-bottom:40px}.zaui-box-mr-10{margin-right:40px}.zaui-box-mx-10{margin-left:40px;margin-right:40px}.zaui-box-my-10{margin-top:40px;margin-bottom:40px}.zaui-box-p-10{padding:40px}.zaui-box-pt-10{padding-top:40px}.zaui-box-pl-10{padding-left:40px}.zaui-box-pb-10{padding-bottom:40px}.zaui-box-pr-10{padding-right:40px}.zaui-box-px-10{padding-left:40px;padding-right:40px}.zaui-box-py-10{padding-top:40px;padding-bottom:40px}.zaui-box-inline{display:inline-block}.zaui-box-no-space{margin:0;padding:0}.zaui-box-flex{display:flex}.zaui-box-flex-row{display:flex;flex-direction:row}.zaui-box-flex-column{display:flex;flex-direction:column}.zaui-box-flex-row-reverse{display:flex;flex-direction:row-reverse}.zaui-box-flex-column-reverse{display:flex;flex-direction:column-reverse}.zaui-box-flex-wrap,.zaui-box-flex-nowrap{display:flex;flex-wrap:wrap}.zaui-checkbox-group{box-sizing:border-box;margin:0;padding:0;display:inline-block}.zaui-checkbox-group-item:not(:last-child){margin-right:8px}.zaui-checkbox-group .zaui-checkbox-wrapper:not(:last-child){margin-right:8px}.zaui-checkbox{box-sizing:border-box;margin:0;padding:0;cursor:pointer;position:relative;width:24px;height:24px;flex-shrink:0}.zaui-checkbox-inner{box-sizing:border-box;width:100%;height:100%;display:block;position:relative;background-color:var(--zaui-light-checkbox-uncheck-background, #ffffff);border-radius:8px;border:2px solid var(--zaui-light-checkbox-border-color, #b9bdc1)}.zaui-checkbox-disabled .zaui-checkbox-inner{background-color:var(--zaui-light-checkbox-disabled-uncheck-background, #d6d9dc)}.zaui-checkbox-checked .zaui-checkbox-inner{background-color:var(--zaui-light-checkbox-checked-background, #006af5);border:none!important;color:var(--zaui-light-checkbox-checked-mark-color, #ffffff)}.zaui-checkbox-checked .zaui-checkbox-inner-check-icon{position:absolute;left:50%;top:50%;transform:translate(-50%,-50%);width:14px;height:11px;display:inline-flex;justify-content:center;align-items:center}.zaui-checkbox-checked.zaui-checkbox-disabled .zaui-checkbox-inner{background-color:var(--zaui-light-checkbox-disabled-checked-background, #8fc1ff);color:var(--zaui-light-checkbox-disabled-checked-mark-color, #ffffffcc)}.zaui-checkbox-indeterminate .zaui-checkbox-inner-check-icon{display:none}.zaui-checkbox-indeterminate .zaui-checkbox-inner:after{position:absolute;width:60%;height:60%;content:"";border-radius:2px;background-color:var(--zaui-light-checkbox-checked-background, #006af5);top:50%;left:50%;transform:translate(-50%,-50%)}.zaui-checkbox-indeterminate.zaui-checkbox-disabled .zaui-checkbox-inner:after{background-color:var(--zaui-light-checkbox-disabled-checked-background, #8fc1ff)}.zaui-checkbox-small .zaui-checkbox{width:16px;height:16px}.zaui-checkbox-small .zaui-checkbox-inner{border:1.5px solid var(--zaui-light-checkbox-border-color, #b9bdc1);border-radius:4px}.zaui-checkbox-small .zaui-checkbox-inner-check-icon{width:10px;height:8px}.zaui-checkbox-wrapper{position:relative;display:inline-flex;align-items:center;font-size:14px;line-height:18px;font-weight:400}.zaui-checkbox-wrapper.zaui-checkbox-small{font-size:13px;line-height:18px;font-weight:400}.zaui-checkbox-input{position:absolute;box-sizing:border-box;padding:0;margin:0;outline:none;border:none;z-index:1;width:100%;height:100%;top:0;bottom:0;left:0;right:0;cursor:pointer;opacity:0;display:block}.zaui-checkbox+span{margin-left:8px;display:inline-block;flex:1}body[zaui-theme=dark] .zaui-checkbox-inner{background-color:var(--zaui-dark-checkbox-uncheck-background, #141415);border:2px solid var(--zaui-dark-checkbox-border-color, #767a7f)}body[zaui-theme=dark] .zaui-checkbox-disabled .zaui-checkbox-inner{background-color:var(--zaui-dark-checkbox-disabled-uncheck-background, #36383a)}body[zaui-theme=dark] .zaui-checkbox-checked .zaui-checkbox-inner{background-color:var(--zaui-dark-checkbox-checked-background, #006af5);border:none!important;color:var(--zaui-dark-checkbox-checked-mark-color, #ffffff)}body[zaui-theme=dark] .zaui-checkbox-checked.zaui-checkbox-disabled .zaui-checkbox-inner{background-color:var(--zaui-dark-checkbox-disabled-checked-background, #033e8c);color:var(--zaui-dark-checkbox-disabled-checked-mark-color, #ffffff99)}body[zaui-theme=dark] .zaui-checkbox-indeterminate .zaui-checkbox-inner:after{background-color:var(--zaui-dark-checkbox-checked-background, #006af5)}body[zaui-theme=dark] .zaui-checkbox-indeterminate.zaui-checkbox-disabled .zaui-checkbox-inner:after{background-color:var(--zaui-dark-checkbox-disabled-checked-background, #033e8c)}body[zaui-theme=dark] .zaui-checkbox-small .zaui-checkbox-inner{border:1.5px solid var(--zaui-dark-checkbox-border-color, #767a7f)}.zaui-radio-group{box-sizing:border-box;margin:0;padding:0;display:inline-block}.zaui-radio-group-item:not(:last-child){margin-right:8px}.zaui-radio-group .zaui-radio-wrapper:not(:last-child){margin-right:8px}.zaui-radio{box-sizing:border-box;margin:0;padding:0;cursor:pointer;position:relative;width:24px;height:24px}.zaui-radio-checkmark{width:100%;height:100%;display:block;position:relative;background-color:var(--zaui-light-radio-uncheck-background, #ffffff);border-radius:12px;border:2px solid var(--zaui-light-radio-border-color, #b9bdc1)}.zaui-radio-disabled .zaui-radio-checkmark{background-color:var(--zaui-light-radio-disabled-uncheck-background, #d6d9dc)}.zaui-radio-checked .zaui-radio-checkmark{background-color:var(--zaui-light-radio-checked-background, #006af5);border:none!important;color:var(--zaui-light-radio-checked-mark-color, #ffffff)}.zaui-radio-checked .zaui-radio-checkmark:after{position:absolute;width:8px;height:8px;border-radius:100%;left:50%;top:50%;transform:translate(-50%,-50%);background-color:var(--zaui-light-radio-checked-mark-color, #ffffff);content:""}.zaui-radio-checked.zaui-radio-disabled .zaui-radio-checkmark{background-color:var(--zaui-light-radio-disabled-checked-background, #8fc1ff)}.zaui-radio-checked.zaui-radio-disabled .zaui-radio-checkmark:after{background-color:var(--zaui-light-radio-disabled-checked-mark-color, #ffffffcc)}.zaui-radio-small .zaui-radio{width:16px;height:16px}.zaui-radio-small .zaui-radio-checkmark{border:1.5px solid var(--zaui-light-radio-border-color, #b9bdc1);border-radius:8px}.zaui-radio-small .zaui-radio-checkmark:after{width:6px;height:6px}.zaui-radio-wrapper{position:relative;display:inline-flex;align-items:center;font-size:14px;line-height:18px;font-weight:400}.zaui-radio-wrapper.zaui-radio-small{font-size:13px;line-height:18px;font-weight:400}.zaui-radio-input{position:absolute;box-sizing:border-box;padding:0;margin:0;outline:none;border:none;z-index:1;width:100%;height:100%;top:0;bottom:0;left:0;right:0;cursor:pointer;opacity:0;display:block}.zaui-radio+span{margin-left:8px;display:inline-block;flex:1}body[zaui-theme=dark] .zaui-radio-checkmark{background-color:var(--zaui-dark-radio-uncheck-background, #141415);border:2px solid var(--zaui-dark-radio-border-color, #767a7f)}body[zaui-theme=dark] .zaui-radio-disabled .zaui-radio-checkmark{background-color:var(--zaui-dark-radio-disabled-uncheck-background, #36383a)}body[zaui-theme=dark] .zaui-radio-checked .zaui-radio-checkmark{background-color:var(--zaui-dark-radio-checked-background, #006af5);border:none!important}body[zaui-theme=dark] .zaui-radio-checked .zaui-radio-checkmark:after{background-color:var(--zaui-dark-radio-checked-mark-color, #ffffff)}body[zaui-theme=dark] .zaui-radio-checked.zaui-radio-disabled .zaui-radio-checkmark{background-color:var(--zaui-dark-radio-disabled-checked-background, #033e8c)}body[zaui-theme=dark] .zaui-radio-checked.zaui-radio-disabled .zaui-radio-checkmark:after{background-color:var(--zaui-dark-radio-disabled-checked-mark-color, #ffffff99)}body[zaui-theme=dark] .zaui-radio-small .zaui-radio-checkmark{border:1.5px solid var(--zaui-dark-radio-border-color, #767a7f)}.zaui-switch{flex:none;position:relative;box-sizing:border-box;appearance:none;-webkit-appearance:none;-moz-appearance:none;-ms-appearance:none;border-radius:20px;width:40px;height:24px;background-color:var(--zaui-light-switch-off-bg-color, #b9bdc1);margin:0}.zaui-switch:before{content:"";position:absolute;top:2px;left:2px;width:20px;height:20px;border-radius:50%;background-color:var(--zaui-light-switch-handler-bg-color, #ffffff);transition:transform .15s ease}.zaui-switch:disabled{background-color:var(--zaui-light-switch-off-disabled-bg-color, #d6d9dc)}.zaui-switch:disabled:before{background-color:var(--zaui-light-switch-handler-disabled-bg-color, #ffffff)}.zaui-switch:checked{background-color:var(--zaui-light-switch-bg-color, #006af5)}.zaui-switch:checked:disabled{background-color:var(--zaui-light-switch-disabled-bg-color, #8fc1ff)}.zaui-switch:checked:before{transform:translate(16px)}.zaui-switch-wrapper{display:inline-flex;align-items:center}.zaui-switch-small{width:28px;height:16px}.zaui-switch-small:before{width:12px;height:12px}.zaui-switch-small:checked:before{transform:translate(12px)}.zaui-switch-label{line-height:18px;flex:auto;font-size:14px;margin-left:8px}.zaui-switch-label-small{font-size:13px;margin-left:4px}.zaui-switch:disabled~.zaui-switch-label{color:var(--zaui-light-switch-label-disabled-color, #b9bdc1)}body[zaui-theme=dark] .zaui-switch{background-color:var(--zaui-dark-switch-off-bg-color, #53575a)}body[zaui-theme=dark] .zaui-switch:before{background-color:var(--zaui-dark-switch-handler-bg-color, #ffffff)}body[zaui-theme=dark] .zaui-switch:disabled{background-color:var(--zaui-dark-switch-off-bg-color, #53575a)}body[zaui-theme=dark] .zaui-switch:disabled:before{background-color:var(--zaui-dark-switch-handler-disabled-bg-color, #ffffff99)}body[zaui-theme=dark] .zaui-switch:checked{background-color:var(--zaui-dark-switch-bg-color, #006af5)}body[zaui-theme=dark] .zaui-switch:checked:disabled{background-color:var(--zaui-dark-switch-disabled-bg-color, #033e8c)}body[zaui-theme=dark] .zaui-switch:disabled~body[zaui-theme=dark] .zaui-switch-label{color:var(--zaui-dark-switch-label-disabled-color, #53575a)}.zaui-slider-header{display:flex;justify-content:space-between;margin:0 0 12px}.zaui-slider-label{flex-grow:1;word-break:break-word;color:var(--zaui-light-slider-label-color, #767a7f)}.zaui-slider-value{word-break:break-word;color:var(--zaui-light-slider-value-color, #141415)}.zaui-slider-handler{height:16px;display:flex;flex:auto;align-items:center;-webkit-user-select:none;-moz-user-select:none;-ms-user-select:none;user-select:none}.zaui-slider-track{position:relative;width:100%;height:4px;background-color:var(--zaui-light-slider-track-bg-color, #d6d9dc);border-radius:8px}.zaui-slider-track-bar{content:"";position:absolute;top:0;height:100%;background-color:var(--zaui-light-slider-track-active-bg-color, #006af5);border-radius:8px}.zaui-slider-thumb{position:absolute;top:50%;transform:translate(-50%,-50%);width:16px;height:16px;border-radius:16px;background-color:var(--zaui-light-slider-thumb-bg-color, #006af5)}.zaui-slider-mark{position:absolute;top:50%;transform:translate(-50%,-50%);width:4px;height:8px;border-radius:8px;background-color:var(--zaui-light-slider-mark-bg-color, #d6d9dc)}.zaui-slider-mark-filled{background-color:var(--zaui-light-slider-mark-filled-bg-color, #006af5)}.zaui-slider-content{display:flex;justify-content:space-between;align-items:center}.zaui-slider-prefix,.zaui-slider-suffix{display:flex;flex-direction:column;justify-content:center;align-items:center;color:var(--zaui-light-slider-prefix-suffix-color, #767a7f)}.zaui-slider-prefix{margin-right:16px}.zaui-slider-suffix{margin-left:16px}body[zaui-theme=dark] .zaui-slider-label{color:var(--zaui-dark-slider-label-color, #8f9499)}body[zaui-theme=dark] .zaui-slider-value{color:var(--zaui-dark-slider-value-color, #f4f5f6)}body[zaui-theme=dark] .zaui-slider-track{background-color:var(--zaui-dark-slider-track-bg-color, #d6d9dc)}body[zaui-theme=dark] .zaui-slider-thumb{background-color:var(--zaui-dark-slider-thumb-bg-color, #006af5)}body[zaui-theme=dark] .zaui-slider-mark{background-color:var(--zaui-dark-slider-mark-bg-color, #d6d9dc)}body[zaui-theme=dark] .zaui-slider-mark-filled{background-color:var(--zaui-dark-slider-mark-filled-bg-color, #006af5)}body[zaui-theme=dark] .zaui-slider-prefix,body[zaui-theme=dark] .zaui-slider-suffix{color:var(--zaui-dark-slider-prefix-suffix-color, #d6d9dc)}.zaui-progress{display:inline-block;align-items:center;width:100%;box-sizing:border-box}.zaui-progress-outer{width:100%;border-radius:4px;overflow:hidden;display:inline-block}.zaui-progress-show-label{display:inline-flex}.zaui-progress-show-label-outer{margin-right:calc(-2em - 8px);padding-right:calc(2em + 8px)}.zaui-progress-inner{background-color:var(--zaui-light-progress-background, #ffffff);width:100%;height:4px;border-radius:4px}.zaui-progress-completed{background-color:var(--zaui-light-progress-completed, #006af5);height:100%;border-radius:4px;transition:all .5s linear}.zaui-progress-label{display:inline-block;line-height:1;white-space:nowrap;margin-left:8px;width:2em}body[zaui-theme=dark] .zaui-progress-inner{background-color:var(--zaui-dark-progress-background, #ffffff33)}body[zaui-theme=dark] .zaui-progress-completed{background-color:var(--zaui-dark-progress-completed, #52a0ff)}.zaui-spinner{position:relative;width:62px;height:62px}.zaui-spinner:after{position:absolute;width:calc(100% - 4px);height:calc(100% - 4px);left:50%;top:50%;transform:translate(-50%,-50%);border-radius:100%;content:"";border:1px solid var(--zaui-light-spinner-border-color, #d6d9dc)}.zaui-spinner-ring{position:absolute;z-index:1;width:100%;height:100%;top:0;left:0;border-radius:50%;-webkit-mask:radial-gradient(farthest-side,#0000 calc(100% - 4px),#000 calc(100% - 3px));mask:radial-gradient(farthest-side,#0000 calc(100% - 4px),#000 calc(100% - 3px));background:conic-gradient(from 90deg at 50% 50%,#fff0 -89.96deg,#fff0 180.44deg,#08f 258.75deg,#08f 270deg,#fff0 270.04deg,#fff0 540.44deg);animation:loadingCircle 1.2s infinite linear}.zaui-spinner-dot{z-index:2;position:absolute;width:100%;height:100%;animation:loadingCircle 1.2s infinite linear}.zaui-spinner-dot:after{content:"";width:4px;height:4px;background-color:var(--zaui-light-spinner-dot-color, #006af5);top:0;left:50%;transform:translate(-50%);position:absolute;border-radius:100%}.zaui-spinner-logo{position:absolute;width:calc(100% - 14px);height:calc(100% - 14px);left:50%;top:50%;transform:translate(-50%,-50%);border-radius:100%;overflow:hidden}.zaui-spinner-logo img{width:100%;height:100%;object-fit:cover}body[zaui-theme=dark] .zaui-spinner:after{border:1px solid var(--zaui-dark-spinner-border-color, #d6d9dc)}body[zaui-theme=dark] .zaui-spinner-ring{background:conic-gradient(from 90deg at 50% 50%,#fff0 -89.96deg,#fff0 180.44deg,#08f 258.75deg,#08f 270deg,#fff0 270.04deg,#fff0 540.44deg)}body[zaui-theme=dark] .zaui-spinner-dot:after{background-color:var(--zaui-dark-spinner-dot-color, #006af5)}.zaui-header{position:fixed;top:0;left:0;height:calc(var(--zaui-safe-area-inset-top, 0px) + 44px);width:100%;max-width:100vw;min-width:100vw;overflow:hidden;box-sizing:border-box;background:var(--zaui-light-header-background-color, #ffffff);color:var(--zaui-light-header-color, #141415);padding:calc(var(--zaui-safe-area-inset-top, 0px) + 16px) 12px 16px 12px;display:flex;align-items:center;margin-bottom:1px;z-index:999}.zaui-header:after{position:absolute;width:100%;height:1px;background:var(--zaui-light-header-divider, #e9ebed);content:"";left:0;bottom:0}.zaui-header-title{flex:1;overflow:hidden;text-overflow:ellipsis;font-size:18px;line-height:24px;font-weight:500;white-space:nowrap;display:inline-block}.zaui-header-back{width:24px;height:24px;position:relative;display:inline-block}.zaui-header-back-btn{padding:0;margin:0;width:100%;height:100%}.zaui-header>*:not(:first-child){margin-left:8px}body[zaui-theme=dark] .zaui-header{background:var(--zaui-dark-header-background-color, #141415);color:var(--zaui-dark-header-color, #f4f5f6)}body[zaui-theme=dark] .zaui-header:after{background:var(--zaui-dark-header-divider, #36383a)}.zaui-select{text-overflow:ellipsis}.zaui-select-icon{margin-right:12px}.zaui-select-options{overflow-y:auto;overflow-x:hidden}.zaui-select-panel{display:flex;flex-direction:column;overflow:hidden}.zaui-select-group{color:var(--zaui-light-option-group-color, #767a7f);font-size:14px;line-height:18px;cursor:default;padding:8px 16px}.zaui-select-option+.zaui-select-group{margin-top:16px}.zaui-select-header{display:flex;flex-direction:row;min-height:56px}.zaui-select-close-icon,.zaui-select .zaui-btn.zaui-btn-icon-only.zaui-btn-medium.zaui-select-close-icon{position:absolute;right:16px;top:16px;padding:0;width:24px;height:24px;font-size:24px;color:var(--zaui-light-select-title-color, #141415)}.zaui-select-title{font-size:18px;line-height:18px;font-weight:500;color:var(--zaui-light-select-title-color, #141415);text-align:center;flex:1;padding:16px 32px;text-overflow:ellipsis;white-space:nowrap;overflow:hidden;display:block}.zaui-select-option{padding:16px 32px 16px 16px;font-size:16px;line-height:22px;color:var(--zaui-light-option-color, #141415);position:relative;text-overflow:ellipsis;white-space:nowrap;overflow:hidden;display:block}.zaui-select-option:after{width:100%;height:.5px;content:"";background-color:var(--zaui-light-option-divider-color, #e9ebed);bottom:0;left:0;position:absolute}.zaui-select-option-disabled{color:var(--zaui-light-option-color-disabled, #767a7f);pointer-events:none}.zaui-select-option-check-icon{position:absolute;right:16px;top:50%;transform:translateY(-50%)}.zaui-select-option-selected{color:var(--zaui-light-option-selected-color, #006af5)}.zaui-select-option:focus,.zaui-select-option:active{background-color:var(--zaui-light-pressed-bg-color, #e9ebed)}body[zaui-theme=dark] .zaui-select-group{color:var(--zaui-dark-option-group-color, #8f9499)}body[zaui-theme=dark] .zaui-select-close-icon,body[zaui-theme=dark] .zaui-select .zaui-btn.zaui-btn-icon-only.zaui-btn-medium.zaui-select-close-icon,body[zaui-theme=dark] .zaui-select-title{color:var(--zaui-dark-select-title-color, #f4f5f6)}body[zaui-theme=dark] .zaui-select-option{color:var(--zaui-dark-option-color, #f4f5f6)}body[zaui-theme=dark] .zaui-select-option:after{background-color:var(--zaui-dark-option-divider-color, #36383a)}body[zaui-theme=dark] .zaui-select-option-disabled{color:var(--zaui-dark-option-color-disabled, #8f9499)}body[zaui-theme=dark] .zaui-select-option-selected{color:var(--zaui-dark-option-selected-color, #006af5)}body[zaui-theme=dark] .zaui-select-option:focus,body[zaui-theme=dark] .zaui-select-option:active{background-color:var(--zaui-dark-pressed-bg-color, #36383a)}.zaui-snackbar{box-sizing:border-box;display:flex;align-items:center;position:fixed;z-index:1001;transition:opacity .15s,transform .15s;width:calc(100vw - 24px);background-color:var(--zaui-light-snackbar-background, #252627);color:var(--zaui-light-snackbar-text-color, #ffffff);padding:12px 16px;border-radius:8px;flex-direction:row;justify-content:space-between;font-size:15px;line-height:20px;font-weight:400}.zaui-snackbar>*:not(:last-child){margin-right:8px}.zaui-snackbar-desc-wrapper{display:flex;flex-direction:row;align-items:center}.zaui-snackbar-desc-wrapper>*:not(:last-child){margin-right:8px}.zaui-snackbar-prefix-icon{width:24px;height:24px}.zaui-snackbar-content{flex:1}.zaui-snackbar-content>*:not(:last-child){margin-bottom:8px}.zaui-snackbar-text-only{width:-moz-max-content;width:-webkit-max-content;width:max-content;max-width:calc(100vw - 24px)}.zaui-snackbar-text-only .zaui-snackbar-desc{text-align:center}.zaui-snackbar-vertical-action{display:flex;flex-direction:column;align-items:flex-end}.zaui-snackbar-vertical-action>*{margin-right:0}.zaui-snackbar-vertical-action>*:not(:last-child){margin-right:0;margin-bottom:8px}.zaui-snackbar-vertical-action .zaui-snackbar-content{width:100%}.zaui-snackbar-success{color:var(--zaui-light-snackbar-success-color, #34b764)}.zaui-snackbar-error{color:var(--zaui-light-snackbar-error-color, #dc1f18)}.zaui-snackbar-warning{color:var(--zaui-light-snackbar-warning-color, #e8ba02)}.zaui-snackbar-info{color:var(--zaui-light-snackbar-info-color, #52a0ff)}.zaui-snackbar-action{height:40px;font-weight:500;color:var(--zaui-light-snackbar-action-color, #52a0ff);padding:10px 8px}.zaui-snackbar-loading{animation:loadingCircle 1s infinite linear}.zaui-snackbar-progress .zaui-progress-completed{background-color:var(--zaui-light-snackbar-progress-color, #52a0ff)}.zaui-snackbar-wifi-connected{color:var(--zaui-light-snackbar-connect-color, #34b764)}.zaui-snackbar-wifi-disconnected{color:var(--zaui-light-snackbar-disconnect-color, #767a7f)}.zaui-snackbar-top{left:50%;transform:translate(-50%) translateY(-12px);top:12px}.zaui-snackbar-top-visible{transform:translate(-50%) translateY(0)}.zaui-snackbar-bottom{left:50%;transform:translate(-50%) translateY(12px);bottom:12px}.zaui-snackbar-bottom-visible{transform:translate(-50%) translateY(0)}.zaui-snackbar-appear,.zaui-snackbar-exit-active{opacity:0}.zaui-snackbar-enter{opacity:1}.zaui-snackbar-enter-top,.zaui-snackbar-exit-active-top{transform:translate(-50%,-12px)}.zaui-snackbar-enter-bottom,.zaui-snackbar-exit-active-bottom{transform:translate(-50%,12px)}.zaui-snackbar-enter-active-top,.zaui-snackbar-enter-active-bottom{transform:translate(-50%)}.zaui-snackbar-countdown-background,.zaui-snackbar-countdown-progress{fill:none}.zaui-snackbar-countdown-background{stroke:var(--zaui-light-countdown-bg-color, #ffffff)}.zaui-snackbar-countdown-progress{transition:stroke-dashoffset .5s ease-in-out;stroke:var(--zaui-light-countdown-progress-color, #52a0ff);stroke-linecap:round;transform-origin:center}.zaui-snackbar-countdown-counter{font-size:12px;fill:var(--zaui-light-countdown-counter-color, #ffffff);transform:translate(0 50%)}body[zaui-theme=dark] .zaui-snackbar{background-color:var(--zaui-dark-snackbar-background, #53575a);color:var(--zaui-dark-snackbar-text-color, #ffffff)}body[zaui-theme=dark] .zaui-snackbar-success{color:var(--zaui-dark-snackbar-success-color, #34b764)}body[zaui-theme=dark] .zaui-snackbar-error{color:var(--zaui-dark-snackbar-error-color, #dc1f18)}body[zaui-theme=dark] .zaui-snackbar-warning{color:var(--zaui-dark-snackbar-warning-color, #e8ba02)}body[zaui-theme=dark] .zaui-snackbar-info{color:var(--zaui-dark-snackbar-info-color, #52a0ff)}body[zaui-theme=dark] .zaui-snackbar-action{color:var(--zaui-dark-snackbar-action-color, #52a0ff)}body[zaui-theme=dark] .zaui-snackbar-progress .zaui-progress-completed{background-color:var(--zaui-dark-snackbar-progress-color, #52a0ff)}body[zaui-theme=dark] .zaui-snackbar-wifi-connected{color:var(--zaui-dark-snackbar-connect-color, #34b764)}body[zaui-theme=dark] .zaui-snackbar-wifi-disconnected{color:var(--zaui-dark-snackbar-disconnect-color, #8f9499)}body[zaui-theme=dark] .zaui-snackbar-countdown-background{stroke:var(--zaui-dark-countdown-bg-color, #ffffff)}body[zaui-theme=dark] .zaui-snackbar-countdown-progress{stroke:var(--zaui-dark-countdown-progress-color, #52a0ff)}body[zaui-theme=dark] .zaui-snackbar-countdown-counter{fill:var(--zaui-dark-countdown-counter-color, #ffffff)}.zaui-picker{padding:0 16px 24px}.zaui-picker *,.zaui-picker *:before,.zaui-picker *:after{box-sizing:border-box}.zaui-picker-header{display:flex;flex-direction:row;min-height:56px;margin-bottom:16px}.zaui-picker-close-icon,.zaui-picker .zaui-btn.zaui-btn-icon-only.zaui-btn-medium.zaui-picker-close-icon{position:absolute;right:16px;top:16px;padding:0;width:24px;height:24px;font-size:24px;color:var(--zaui-light-picker-title-color, #141415)}.zaui-picker-title{font-size:18px;line-height:18px;font-weight:500;color:var(--zaui-light-picker-title-color, #141415);text-align:center;flex:1;padding:16px 32px;text-overflow:ellipsis;white-space:nowrap;overflow:hidden;display:block}.zaui-picker-inner{position:relative;display:flex;justify-content:center;height:100%}.zaui-picker-column{position:relative;flex:1;max-height:100%;overflow:hidden}.zaui-picker-column:not(:last-child){margin-right:8px}.zaui-picker-scroller{transition:.3s;transition-timing-function:ease-out}.zaui-picker-item{position:relative;padding:0 6px;white-space:nowrap;overflow:hidden;text-overflow:ellipsis;text-align:center;width:100%;font-size:13px;line-height:18px;font-weight:400;color:var(--zaui-light-picker-option-color, #b9bdc1);height:52px}.zaui-picker-item-selected{color:var(--zaui-light-picker-option-selected-color, #006af5);background:var(--zaui-light-picker-selected-background-color, #f4f5f6);border-radius:8px;font-size:15px;line-height:20px;height:56px;transition:all .2s ease-out}.zaui-picker-item-2nd{font-size:14px;line-height:18px;color:var(--zaui-light-picker-option-2nd-color, #767a7f);height:54px}.zaui-picker-action{margin-top:16px}.zaui-date-picker-suffix{margin-right:12px}.zaui-bottom-navigation{width:100%;height:auto}.no-safe-area .zaui-bottom-navigation-content{padding-bottom:unset}.zaui-bottom-navigation-content{position:relative;display:flex;height:calc(var(--zaui-safe-area-inset-bottom) + 48px);width:100%;justify-content:center;align-items:center;background-color:var(--zaui-light-bottom-navigation-background-color, #ffffff);padding-bottom:var(--zaui-safe-area-inset-bottom)}.zaui-bottom-navigation-content:after{content:"";top:-.5px;left:0;position:absolute;width:100%;height:.5px;background-color:var(--zaui-light-bottom-navigation-divider-color, #e9ebed)}.zaui-bottom-navigation-fixed{position:fixed;bottom:0;left:0;z-index:999}.zaui-bottom-navigation-item{display:inline-flex;flex-direction:column;align-items:center;border:none;background:transparent;box-shadow:none;outline:none;cursor:pointer;color:var(--zaui-light-bottom-navigation-color, #767a7f);flex:1;padding:0}.zaui-bottom-navigation-item .zaui-bottom-navigation-item-icon{font-size:24px;width:24px;height:24px;display:inline-flex}.zaui-bottom-navigation-item .zaui-bottom-navigation-item-icon~.zaui-bottom-navigation-item-label{margin-top:2px}.zaui-bottom-navigation-item .zaui-bottom-navigation-item-label{font-size:12px;font-weight:400;line-height:16px}.zaui-bottom-navigation-item-active{color:var(--zaui-light-bottom-navigation-active-color, #006af5)}body[zaui-theme=dark] .zaui-bottom-navigation-content{background-color:var(--zaui-dark-bottom-navigation-background-color, #141415)}body[zaui-theme=dark] .zaui-bottom-navigation-content:after{background-color:var(--zaui-dark-bottom-navigation-divider-color, #36383a)}body[zaui-theme=dark] .zaui-bottom-navigation-item{color:var(--zaui-dark-bottom-navigation-color, #8f9499);flex:1}body[zaui-theme=dark] .zaui-bottom-navigation-item-active{color:var(--zaui-dark-bottom-navigation-active-color, #52a0ff)}.zaui-swiper{position:relative;width:100%;white-space:nowrap;overflow:hidden;box-sizing:border-box;border-radius:12px}.zaui-swiper-wrapper{width:auto;width:100%;display:flex;flex-direction:row;flex-wrap:nowrap;touch-action:none}.zaui-swiper-item{display:block;width:100%;min-width:100%;overflow:hidden;font-size:unset}.zaui-swiper-dots{position:absolute;bottom:8px;left:50%;transform:translate(-50%);background-color:#0003;display:flex;padding:4px;border-radius:100px;align-items:center;transition:all .3s ease}.zaui-swiper-dots-item{display:inline-block;width:4px;height:4px;background-color:#fff9;border-radius:100%}.zaui-swiper-dots-item:not(:last-child){margin-right:4px}.zaui-swiper-dots-item-active{width:6px;height:6px;background-color:#fff}.zaui-image-viewer .zaui-image-container{width:100%;height:100%;background-color:transparent}.zaui-image-viewer .zaui-image-container .zaui-image{width:100%;height:100%;object-fit:scale-down;overflow:hidden;object-position:center;touch-action:none;user-select:none;-moz-user-select:none;-webkit-user-select:none;-ms-user-select:none;-webkit-touch-callout:none}.zaui-image-viewer-container{width:100vw;height:100vh;position:absolute;top:0;left:0;z-index:9999}.zaui-image-viewer-images{width:100%;height:100%;overflow:hidden}.zaui-image-viewer-swiper{position:absolute;z-index:9999;width:100vw;height:100vh;left:0;top:0}.zaui-image-viewer-swiper .zaui-swiper-wrapper{height:100%}.zaui-image-viewer-header{position:absolute;top:0;left:0;width:100%;height:auto;z-index:10000;padding:calc(var(--zaui-safe-area-inset-top, 0px) + 16px) 12px 16px 12px;color:#fff;background-color:#00000080}.zaui-image-viewer-header .zaui-image-viewer-close-button{color:#fff;min-width:unset;width:auto;padding:8px;cursor:pointer;font-size:14px;line-height:18px;font-weight:500}.zaui-image-viewer-nav-btn{position:absolute;z-index:10000;top:50%;transform:translateY(-50%);background-color:#00000080;color:#fff}.zaui-image-viewer-nav-btn:focus,.zaui-image-viewer-nav-btn:active,.zaui-image-viewer-nav-btn:hover{background-color:#00000080}.zaui-image-viewer-nav-btn:disabled,.zaui-image-viewer-nav-btn.zaui-btn-disabled:disabled{background-color:#0003;color:#ffffff80}.zaui-image-viewer-nav-btn-next{right:8px}.zaui-image-viewer-nav-btn-prev{left:8px}body[zaui-theme=dark] .zaui-image-viewer-nav-btn{background-color:#00000080;color:#fff}body[zaui-theme=dark] .zaui-image-viewer-nav-btn:focus,body[zaui-theme=dark] .zaui-image-viewer-nav-btn:active,body[zaui-theme=dark] .zaui-image-viewer-nav-btn:hover{background-color:#00000080}body[zaui-theme=dark] .zaui-image-viewer-nav-btn:disabled{background-color:#0003;color:#ffffff80}.zaui-stack{display:flex;flex-direction:column;justify-content:flex-start}.zaui-stack>*+*{-webkit-margin-before:var(--s1);margin-block-start:var(--s1)}.zaui-zbox{display:block;padding:var(--s1);border-width:var(--border-thin);outline:var(--border-thin) solid transparent;outline-offset:calc(var(--border-thin) * -1)}.zaui-center{display:block;box-sizing:content-box;margin-inline:auto;max-inline-size:var(--measure)}.zaui-cluster{display:flex;flex-wrap:wrap;justify-content:flex-start;align-items:flex-start}.zaui-grid{display:grid;grid-gap:var(--s1);align-content:start;grid-template-columns:100%}.zaui-calendar .item-enter{opacity:0}.zaui-calendar .item-enter-active{opacity:1;transition:opacity .5s ease-in}.zaui-calendar .item-exit{opacity:1}.zaui-calendar .item-exit-active{opacity:0;transition:opacity .5s ease-in}.zaui-calendar-header{display:flex;justify-content:space-between;align-items:center}.zaui-calendar-header-title{cursor:pointer;color:var(--zaui-light-calendar-title-color, #141415)}.zaui-calendar-header-title:hover{color:var(--zaui-light-calendar-title-hover-color, #006af5)}.zaui-calendar-panel{text-align:center;background:var(--zaui-light-calendar-bg-color, #ffffff);border-radius:8px}.zaui-calendar-full .zaui-calendar-panel{display:block;width:100%;text-align:right}.zaui-calendar-panel-month tr,.zaui-calendar-panel-year tr,.zaui-calendar-panel-decade tr{display:flex;flex-wrap:wrap;justify-content:center;max-width:480px;margin:auto}.zaui-calendar-panel-month td,.zaui-calendar-panel-year td,.zaui-calendar-panel-decade td{width:120px}.zaui-calendar-panel-body th{height:auto}.zaui-calendar-full .zaui-calendar-panel-body th{-webkit-padding-end:12px;-moz-padding-end:12px;padding-inline-end:12px;padding-bottom:4px;line-height:18px}.zaui-calendar-panel-content{width:100%;table-layout:fixed;border-collapse:collapse}.zaui-calendar-panel-cell{padding:6px 0;color:var(--zaui-light-calendar-cell-text-color, #0000004d);cursor:pointer}.zaui-calendar-full .zaui-calendar-panel-cell{padding:0}.zaui-calendar-panel-cell-in-view{color:var(--zaui-light-calendar-cell-text-color, #000000e6)}.zaui-calendar-panel-cell-in-view.zaui-calendar-panel-cell-selected:not(.zaui-calendar-panel-cell-disabled) .zaui-calendar-cell-inner{color:var(--zaui-light-calendar-cell-selected-text-color, #ffffff);background:var(--zaui-light-calendar-cell-selected-background, #006af5)}.zaui-calendar-panel-cell-in-view.zaui-calendar-panel-cell-selected:not(.zaui-calendar-panel-cell-disabled) .zaui-calendar-cell-inner-content{color:var(--zaui-light-calendar-cell-selected-background, #006af5)}.zaui-calendar-full .zaui-calendar-panel-cell-in-view.zaui-calendar-panel-cell-selected:not(.zaui-calendar-panel-cell-disabled) .zaui-calendar-cell-inner{background:var(--zaui-light-calendar-cell-selected-background, #ebf4ff)}.zaui-calendar-full .zaui-calendar-panel-cell-in-view.zaui-calendar-panel-cell-selected:not(.zaui-calendar-panel-cell-disabled) .zaui-calendar-cell-inner-label{color:var(--zaui-light-calendar-cell-selected-text-color, #006af5)}.zaui-calendar-panel-cell-disabled{color:var(--zaui-light-calendar-cell-text-color, #0000004d);pointer-events:none}.zaui-calendar-panel-cell-disabled .zaui-calendar-cell-inner{background:transparent}.zaui-calendar-cell-inner{position:relative;z-index:2;display:inline-block;min-width:24px;height:24px;line-height:24px;border-radius:4px;transition:background .2s}.zaui-calendar-cell-inner:before{display:block;width:100%;box-sizing:border-box}.zaui-calendar-full .zaui-calendar-cell-inner:before{display:none}.zaui-calendar-panel-cell-in-view.zaui-calendar-panel-cell-today:not(.zaui-calendar-panel-cell-disabled) .zaui-calendar-cell-inner:before{position:absolute;top:0;left:0;inset-inline-end:0;bottom:0;inset-inline-start:0;z-index:1;border:1px solid var(--zaui-light-calendar-selected-background, #006af5);border-radius:4px;content:""}.zaui-calendar-full .zaui-calendar-panel-cell-in-view.zaui-calendar-panel-cell-today:not(.zaui-calendar-panel-cell-disabled) .zaui-calendar-cell-inner{border-color:var(--zaui-light-calendar-selected-background, #006af5)}.zaui-calendar-full .zaui-calendar-cell-inner{display:block;width:auto;height:auto;margin:0 4px;padding:4px 8px 0;border:0;border-top:2px solid rgba(5,5,5,.06);border-radius:0;transition:background .3s}.zaui-calendar-full .zaui-calendar-cell-inner-content{position:static;width:auto;height:86px;overflow-y:auto;color:var(--zaui-light-calendar-cell-text-color, #000000e6);line-height:1.57142857;text-align:start;scrollbar-width:thin}.zaui-calendar-cell-inner-label{padding:0 8px}body[zaui-theme=dark] .zaui-calendar-header-title{color:var(--zaui-dark-calendar-title-color, #f4f5f6)}body[zaui-theme=dark] .zaui-calendar-header-title:hover{color:var(--zaui-light-calendar-title-hover-color, #006af5)}body[zaui-theme=dark] .zaui-calendar-panel{background:var(--zaui-dark-calendar-bg-color, #252627)}body[zaui-theme=dark] .zaui-calendar-panel-cell{color:var(--zaui-dark-calendar-cell-text-color, rgba(185, 189, 193, .25))}body[zaui-theme=dark] .zaui-calendar-panel-cell-in-view{color:var(--zaui-dark-calendar-cell-text-color, rgba(185, 189, 193, .85))}body[zaui-theme=dark] .zaui-calendar-panel-cell-disabled{color:var(--zaui-dark-calendar-cell-text-color, rgba(185, 189, 193, .25))}body[zaui-theme=dark] .zaui-calendar-full .zaui-calendar-cell-inner{border-top-color:#36383a}body[zaui-theme=dark] .zaui-calendar-full .zaui-calendar-cell-inner-content{color:var(--zaui-dark-calendar-cell-text-color, rgba(185, 189, 193, .85))}*,:before,:after{--tw-border-spacing-x: 0;--tw-border-spacing-y: 0;--tw-translate-x: 0;--tw-translate-y: 0;--tw-rotate: 0;--tw-skew-x: 0;--tw-skew-y: 0;--tw-scale-x: 1;--tw-scale-y: 1;--tw-pan-x: ;--tw-pan-y: ;--tw-pinch-zoom: ;--tw-scroll-snap-strictness: proximity;--tw-gradient-from-position: ;--tw-gradient-via-position: ;--tw-gradient-to-position: ;--tw-ordinal: ;--tw-slashed-zero: ;--tw-numeric-figure: ;--tw-numeric-spacing: ;--tw-numeric-fraction: ;--tw-ring-inset: ;--tw-ring-offset-width: 0px;--tw-ring-offset-color: #fff;--tw-ring-color: rgb(59 130 246 / .5);--tw-ring-offset-shadow: 0 0 #0000;--tw-ring-shadow: 0 0 #0000;--tw-shadow: 0 0 #0000;--tw-shadow-colored: 0 0 #0000;--tw-blur: ;--tw-brightness: ;--tw-contrast: ;--tw-grayscale: ;--tw-hue-rotate: ;--tw-invert: ;--tw-saturate: ;--tw-sepia: ;--tw-drop-shadow: ;--tw-backdrop-blur: ;--tw-backdrop-brightness: ;--tw-backdrop-contrast: ;--tw-backdrop-grayscale: ;--tw-backdrop-hue-rotate: ;--tw-backdrop-invert: ;--tw-backdrop-opacity: ;--tw-backdrop-saturate: ;--tw-backdrop-sepia: ;--tw-contain-size: ;--tw-contain-layout: ;--tw-contain-paint: ;--tw-contain-style: }::-ms-backdrop{--tw-border-spacing-x: 0;--tw-border-spacing-y: 0;--tw-translate-x: 0;--tw-translate-y: 0;--tw-rotate: 0;--tw-skew-x: 0;--tw-skew-y: 0;--tw-scale-x: 1;--tw-scale-y: 1;--tw-pan-x: ;--tw-pan-y: ;--tw-pinch-zoom: ;--tw-scroll-snap-strictness: proximity;--tw-gradient-from-position: ;--tw-gradient-via-position: ;--tw-gradient-to-position: ;--tw-ordinal: ;--tw-slashed-zero: ;--tw-numeric-figure: ;--tw-numeric-spacing: ;--tw-numeric-fraction: ;--tw-ring-inset: ;--tw-ring-offset-width: 0px;--tw-ring-offset-color: #fff;--tw-ring-color: rgb(59 130 246 / .5);--tw-ring-offset-shadow: 0 0 #0000;--tw-ring-shadow: 0 0 #0000;--tw-shadow: 0 0 #0000;--tw-shadow-colored: 0 0 #0000;--tw-blur: ;--tw-brightness: ;--tw-contrast: ;--tw-grayscale: ;--tw-hue-rotate: ;--tw-invert: ;--tw-saturate: ;--tw-sepia: ;--tw-drop-shadow: ;--tw-backdrop-blur: ;--tw-backdrop-brightness: ;--tw-backdrop-contrast: ;--tw-backdrop-grayscale: ;--tw-backdrop-hue-rotate: ;--tw-backdrop-invert: ;--tw-backdrop-opacity: ;--tw-backdrop-saturate: ;--tw-backdrop-sepia: ;--tw-contain-size: ;--tw-contain-layout: ;--tw-contain-paint: ;--tw-contain-style: }::backdrop{--tw-border-spacing-x: 0;--tw-border-spacing-y: 0;--tw-translate-x: 0;--tw-translate-y: 0;--tw-rotate: 0;--tw-skew-x: 0;--tw-skew-y: 0;--tw-scale-x: 1;--tw-scale-y: 1;--tw-pan-x: ;--tw-pan-y: ;--tw-pinch-zoom: ;--tw-scroll-snap-strictness: proximity;--tw-gradient-from-position: ;--tw-gradient-via-position: ;--tw-gradient-to-position: ;--tw-ordinal: ;--tw-slashed-zero: ;--tw-numeric-figure: ;--tw-numeric-spacing: ;--tw-numeric-fraction: ;--tw-ring-inset: ;--tw-ring-offset-width: 0px;--tw-ring-offset-color: #fff;--tw-ring-color: rgb(59 130 246 / .5);--tw-ring-offset-shadow: 0 0 #0000;--tw-ring-shadow: 0 0 #0000;--tw-shadow: 0 0 #0000;--tw-shadow-colored: 0 0 #0000;--tw-blur: ;--tw-brightness: ;--tw-contrast: ;--tw-grayscale: ;--tw-hue-rotate: ;--tw-invert: ;--tw-saturate: ;--tw-sepia: ;--tw-drop-shadow: ;--tw-backdrop-blur: ;--tw-backdrop-brightness: ;--tw-backdrop-contrast: ;--tw-backdrop-grayscale: ;--tw-backdrop-hue-rotate: ;--tw-backdrop-invert: ;--tw-backdrop-opacity: ;--tw-backdrop-saturate: ;--tw-backdrop-sepia: ;--tw-contain-size: ;--tw-contain-layout: ;--tw-contain-paint: ;--tw-contain-style: }*,:before,:after{box-sizing:border-box;border-width:0;border-style:solid;border-color:#e5e7eb}:before,:after{--tw-content: ""}html,:host{line-height:1.5;-webkit-text-size-adjust:100%;-moz-tab-size:4;tab-size:4;font-family:ui-sans-serif,system-ui,sans-serif,"Apple Color Emoji","Segoe UI Emoji",Segoe UI Symbol,"Noto Color Emoji";-moz-font-feature-settings:normal;font-feature-settings:normal;font-variation-settings:normal;-webkit-tap-highlight-color:transparent}body{margin:0;line-height:inherit}hr{height:0;color:inherit;border-top-width:1px}abbr:where([title]){-webkit-text-decoration:underline dotted;text-decoration:underline dotted}h1,h2,h3,h4,h5,h6{font-size:inherit;font-weight:inherit}a{color:inherit;text-decoration:inherit}b,strong{font-weight:bolder}code,kbd,samp,pre{font-family:Roboto Mono,monospace;-moz-font-feature-settings:normal;font-feature-settings:normal;font-variation-settings:normal;font-size:1em}small{font-size:80%}sub,sup{font-size:75%;line-height:0;position:relative;vertical-align:baseline}sub{bottom:-.25em}sup{top:-.5em}table{text-indent:0;border-color:inherit;border-collapse:collapse}button,input,optgroup,select,textarea{font-family:inherit;-moz-font-feature-settings:inherit;font-feature-settings:inherit;font-variation-settings:inherit;font-size:100%;font-weight:inherit;line-height:inherit;letter-spacing:inherit;color:inherit;margin:0;padding:0}button,select{text-transform:none}button,input:where([type=button]),input:where([type=reset]),input:where([type=submit]){-webkit-appearance:button;background-color:transparent;background-image:none}:-moz-focusring{outline:auto}:-moz-ui-invalid{box-shadow:none}progress{vertical-align:baseline}::-webkit-inner-spin-button,::-webkit-outer-spin-button{height:auto}[type=search]{-webkit-appearance:textfield;outline-offset:-2px}::-webkit-search-decoration{-webkit-appearance:none}::-webkit-file-upload-button{-webkit-appearance:button;font:inherit}summary{display:list-item}blockquote,dl,dd,h1,h2,h3,h4,h5,h6,hr,figure,p,pre{margin:0}fieldset{margin:0;padding:0}legend{padding:0}ol,ul,menu{list-style:none;margin:0;padding:0}dialog{padding:0}textarea{resize:vertical}input::-webkit-input-placeholder,textarea::-webkit-input-placeholder{opacity:1;color:#9ca3af}input::-moz-placeholder,textarea::-moz-placeholder{opacity:1;color:#9ca3af}input::-ms-input-placeholder,textarea::-ms-input-placeholder{opacity:1;color:#9ca3af}input::placeholder,textarea::placeholder{opacity:1;color:#9ca3af}button,[role=button]{cursor:pointer}:disabled{cursor:default}img,svg,video,canvas,audio,iframe,embed,object{display:block;vertical-align:middle}img,video{max-width:100%;height:auto}[hidden]:where(:not([hidden=until-found])){display:none}.\!container{width:100%!important}.container{width:100%}@media (min-width: 640px){.\!container{max-width:640px!important}.container{max-width:640px}}@media (min-width: 768px){.\!container{max-width:768px!important}.container{max-width:768px}}@media (min-width: 1024px){.\!container{max-width:1024px!important}.container{max-width:1024px}}@media (min-width: 1280px){.\!container{max-width:1280px!important}.container{max-width:1280px}}@media (min-width: 1536px){.\!container{max-width:1536px!important}.container{max-width:1536px}}.sr-only{position:absolute;width:1px;height:1px;padding:0;margin:-1px;overflow:hidden;clip:rect(0,0,0,0);white-space:nowrap;border-width:0}.not-sr-only{position:static;width:auto;height:auto;padding:0;margin:0;overflow:visible;clip:auto;white-space:normal}.pointer-events-none{pointer-events:none}.pointer-events-auto{pointer-events:auto}.\!visible{visibility:visible!important}.visible{visibility:visible}.invisible{visibility:hidden}.collapse{visibility:collapse}.static{position:static}.\!fixed{position:fixed!important}.fixed{position:fixed}.absolute{position:absolute}.\!relative{position:relative!important}.relative{position:relative}.sticky{position:-webkit-sticky;position:sticky}.-inset-1{top:-.25rem;right:-.25rem;bottom:-.25rem;left:-.25rem}.end-1{inset-inline-end:.25rem}.start-1{inset-inline-start:.25rem}.isolate{isolation:isolate}.isolation-auto{isolation:auto}.float-start{float:inline-start}.float-end{float:inline-end}.float-right{float:right}.float-left{float:left}.float-none{float:none}.clear-start{clear:inline-start}.clear-end{clear:inline-end}.clear-left{clear:left}.clear-right{clear:right}.clear-both{clear:both}.clear-none{clear:none}.mt-2{margin-top:.5rem}.box-border{box-sizing:border-box}.box-content{box-sizing:content-box}.line-clamp-none{overflow:visible;display:block;-webkit-box-orient:horizontal;-webkit-line-clamp:none}.\!block{display:block!important}.block{display:block}.inline-block{display:inline-block}.inline{display:inline}.flex{display:flex}.inline-flex{display:inline-flex}.\!table{display:table!important}.table{display:table}.inline-table{display:inline-table}.table-caption{display:table-caption}.table-cell{display:table-cell}.table-column{display:table-column}.table-column-group{display:table-column-group}.table-footer-group{display:table-footer-group}.table-header-group{display:table-header-group}.table-row-group{display:table-row-group}.table-row{display:table-row}.flow-root{display:flow-root}.\!grid{display:grid!important}.grid{display:grid}.inline-grid{display:inline-grid}.contents{display:contents}.list-item{display:list-item}.hidden{display:none}.w-\[this-is\\\\\]{width:this-is\\}.w-\[this-is\]{width:this-is}.w-\[weird-and-invalid\]{width:weird-and-invalid}.flex-shrink,.shrink{flex-shrink:1}.flex-grow,.grow{flex-grow:1}.table-auto{table-layout:auto}.table-fixed{table-layout:fixed}.caption-top{caption-side:top}.caption-bottom{caption-side:bottom}.border-collapse{border-collapse:collapse}.border-separate{border-collapse:separate}.\!transform{transform:translate(var(--tw-translate-x),var(--tw-translate-y)) rotate(var(--tw-rotate)) skew(var(--tw-skew-x)) skewY(var(--tw-skew-y)) scaleX(var(--tw-scale-x)) scaleY(var(--tw-scale-y))!important}.transform,.transform-cpu{transform:translate(var(--tw-translate-x),var(--tw-translate-y)) rotate(var(--tw-rotate)) skew(var(--tw-skew-x)) skewY(var(--tw-skew-y)) scaleX(var(--tw-scale-x)) scaleY(var(--tw-scale-y))}.transform-gpu{transform:translate3d(var(--tw-translate-x),var(--tw-translate-y),0) rotate(var(--tw-rotate)) skew(var(--tw-skew-x)) skewY(var(--tw-skew-y)) scaleX(var(--tw-scale-x)) scaleY(var(--tw-scale-y))}.transform-none{transform:none}.touch-auto{touch-action:auto}.touch-none{touch-action:none}.touch-pan-x{--tw-pan-x: pan-x;touch-action:var(--tw-pan-x) var(--tw-pan-y) var(--tw-pinch-zoom)}.touch-pan-left{--tw-pan-x: pan-left;touch-action:var(--tw-pan-x) var(--tw-pan-y) var(--tw-pinch-zoom)}.touch-pan-right{--tw-pan-x: pan-right;touch-action:var(--tw-pan-x) var(--tw-pan-y) var(--tw-pinch-zoom)}.touch-pan-y{--tw-pan-y: pan-y;touch-action:var(--tw-pan-x) var(--tw-pan-y) var(--tw-pinch-zoom)}.touch-pan-up{--tw-pan-y: pan-up;touch-action:var(--tw-pan-x) var(--tw-pan-y) var(--tw-pinch-zoom)}.touch-pan-down{--tw-pan-y: pan-down;touch-action:var(--tw-pan-x) var(--tw-pan-y) var(--tw-pinch-zoom)}.touch-pinch-zoom{--tw-pinch-zoom: pinch-zoom;touch-action:var(--tw-pan-x) var(--tw-pan-y) var(--tw-pinch-zoom)}.touch-manipulation{touch-action:manipulation}.select-none{-webkit-user-select:none;-moz-user-select:none;-ms-user-select:none;user-select:none}.select-text{-webkit-user-select:text;-moz-user-select:text;-ms-user-select:text;user-select:text}.select-all{-webkit-user-select:all;-moz-user-select:all;user-select:all}.select-auto{-webkit-user-select:auto;-moz-user-select:auto;-ms-user-select:auto;user-select:auto}.resize-none{resize:none}.resize-y{resize:vertical}.resize-x{resize:horizontal}.resize{resize:both}.snap-none{-webkit-scroll-snap-type:none;-ms-scroll-snap-type:none;scroll-snap-type:none}.snap-x{-webkit-scroll-snap-type:x var(--tw-scroll-snap-strictness);-ms-scroll-snap-type:x var(--tw-scroll-snap-strictness);scroll-snap-type:x var(--tw-scroll-snap-strictness)}.snap-y{-webkit-scroll-snap-type:y var(--tw-scroll-snap-strictness);-ms-scroll-snap-type:y var(--tw-scroll-snap-strictness);scroll-snap-type:y var(--tw-scroll-snap-strictness)}.snap-both{-webkit-scroll-snap-type:both var(--tw-scroll-snap-strictness);-ms-scroll-snap-type:both var(--tw-scroll-snap-strictness);scroll-snap-type:both var(--tw-scroll-snap-strictness)}.snap-mandatory{--tw-scroll-snap-strictness: mandatory}.snap-proximity{--tw-scroll-snap-strictness: proximity}.snap-start{scroll-snap-align:start}.snap-end{scroll-snap-align:end}.snap-center{scroll-snap-align:center}.snap-align-none{scroll-snap-align:none}.snap-normal{scroll-snap-stop:normal}.snap-always{scroll-snap-stop:always}.list-inside{list-style-position:inside}.list-outside{list-style-position:outside}.appearance-none{-webkit-appearance:none;-moz-appearance:none;appearance:none}.appearance-auto{-webkit-appearance:auto;-moz-appearance:auto;appearance:auto}.break-before-auto{-webkit-column-break-before:auto;break-before:auto}.break-before-avoid{-webkit-column-break-before:avoid;break-before:avoid}.break-before-all{-webkit-column-break-before:all;break-before:all}.break-before-avoid-page{-webkit-column-break-before:avoid;break-before:avoid-page}.break-before-page{-webkit-column-break-before:page;break-before:page}.break-before-left{-webkit-column-break-before:left;break-before:left}.break-before-right{-webkit-column-break-before:right;break-before:right}.break-before-column{-webkit-column-break-before:column;break-before:column}.break-inside-auto{-webkit-column-break-inside:auto;break-inside:auto}.break-inside-avoid{-webkit-column-break-inside:avoid;break-inside:avoid}.break-inside-avoid-page{break-inside:avoid-page}.break-inside-avoid-column{-webkit-column-break-inside:avoid;break-inside:avoid-column}.break-after-auto{-webkit-column-break-after:auto;break-after:auto}.break-after-avoid{-webkit-column-break-after:avoid;break-after:avoid}.break-after-all{-webkit-column-break-after:all;break-after:all}.break-after-avoid-page{-webkit-column-break-after:avoid;break-after:avoid-page}.break-after-page{-webkit-column-break-after:page;break-after:page}.break-after-left{-webkit-column-break-after:left;break-after:left}.break-after-right{-webkit-column-break-after:right;break-after:right}.break-after-column{-webkit-column-break-after:column;break-after:column}.grid-flow-row{grid-auto-flow:row}.grid-flow-col{grid-auto-flow:column}.grid-flow-dense{grid-auto-flow:dense}.grid-flow-row-dense{grid-auto-flow:row dense}.grid-flow-col-dense{grid-auto-flow:column dense}.flex-row{flex-direction:row}.flex-row-reverse{flex-direction:row-reverse}.flex-col{flex-direction:column}.flex-col-reverse{flex-direction:column-reverse}.flex-wrap{flex-wrap:wrap}.flex-wrap-reverse{flex-wrap:wrap-reverse}.flex-nowrap{flex-wrap:nowrap}.place-content-center{place-content:center}.place-content-start{place-content:start}.place-content-end{place-content:end}.place-content-between{place-content:space-between}.place-content-around{place-content:space-around}.place-content-evenly{place-content:space-evenly}.place-content-baseline{place-content:baseline}.place-content-stretch{place-content:stretch}.place-items-start{place-items:start}.place-items-end{place-items:end}.place-items-center{place-items:center}.place-items-baseline{place-items:baseline}.place-items-stretch{place-items:stretch}.content-normal{align-content:normal}.content-center{align-content:center}.content-start{align-content:flex-start}.content-end{align-content:flex-end}.content-between{align-content:space-between}.content-around{align-content:space-around}.content-evenly{align-content:space-evenly}.content-baseline{align-content:baseline}.content-stretch{align-content:stretch}.items-start{align-items:flex-start}.items-end{align-items:flex-end}.items-center{align-items:center}.items-baseline{align-items:baseline}.items-stretch{align-items:stretch}.justify-normal{justify-content:normal}.justify-start{justify-content:flex-start}.justify-end{justify-content:flex-end}.justify-center{justify-content:center}.justify-between{justify-content:space-between}.justify-around{justify-content:space-around}.justify-evenly{justify-content:space-evenly}.justify-stretch{justify-content:stretch}.justify-items-start{justify-items:start}.justify-items-end{justify-items:end}.justify-items-center{justify-items:center}.justify-items-stretch{justify-items:stretch}.space-x-2>:not([hidden])~:not([hidden]){--tw-space-x-reverse: 0;margin-right:calc(.5rem * var(--tw-space-x-reverse));margin-left:calc(.5rem * calc(1 - var(--tw-space-x-reverse)))}.space-y-reverse>:not([hidden])~:not([hidden]){--tw-space-y-reverse: 1}.space-x-reverse>:not([hidden])~:not([hidden]){--tw-space-x-reverse: 1}.divide-x>:not([hidden])~:not([hidden]){--tw-divide-x-reverse: 0;border-right-width:calc(1px * var(--tw-divide-x-reverse));border-left-width:calc(1px * calc(1 - var(--tw-divide-x-reverse)))}.divide-y>:not([hidden])~:not([hidden]){--tw-divide-y-reverse: 0;border-top-width:calc(1px * calc(1 - var(--tw-divide-y-reverse)));border-bottom-width:calc(1px * var(--tw-divide-y-reverse))}.divide-y-reverse>:not([hidden])~:not([hidden]){--tw-divide-y-reverse: 1}.divide-x-reverse>:not([hidden])~:not([hidden]){--tw-divide-x-reverse: 1}.divide-solid>:not([hidden])~:not([hidden]){border-style:solid}.divide-dashed>:not([hidden])~:not([hidden]){border-style:dashed}.divide-dotted>:not([hidden])~:not([hidden]){border-style:dotted}.divide-double>:not([hidden])~:not([hidden]){border-style:double}.divide-none>:not([hidden])~:not([hidden]){border-style:none}.place-self-auto{place-self:auto}.place-self-start{place-self:start}.place-self-end{place-self:end}.place-self-center{place-self:center}.place-self-stretch{place-self:stretch}.self-auto{align-self:auto}.self-start{align-self:flex-start}.self-end{align-self:flex-end}.self-center{align-self:center}.self-stretch{align-self:stretch}.self-baseline{align-self:baseline}.justify-self-auto{justify-self:auto}.justify-self-start{justify-self:start}.justify-self-end{justify-self:end}.justify-self-center{justify-self:center}.justify-self-stretch{justify-self:stretch}.overflow-auto{overflow:auto}.overflow-hidden{overflow:hidden}.overflow-clip{overflow:clip}.overflow-visible{overflow:visible}.overflow-scroll{overflow:scroll}.overflow-x-auto{overflow-x:auto}.overflow-y-auto{overflow-y:auto}.overflow-x-hidden{overflow-x:hidden}.overflow-y-hidden{overflow-y:hidden}.overflow-x-clip{overflow-x:clip}.overflow-y-clip{overflow-y:clip}.overflow-x-visible{overflow-x:visible}.overflow-y-visible{overflow-y:visible}.overflow-x-scroll{overflow-x:scroll}.overflow-y-scroll{overflow-y:scroll}.overscroll-auto{-ms-scroll-chaining:chained;overscroll-behavior:auto}.overscroll-contain{-ms-scroll-chaining:none;overscroll-behavior:contain}.overscroll-none{-ms-scroll-chaining:none;overscroll-behavior:none}.overscroll-y-auto{overscroll-behavior-y:auto}.overscroll-y-contain{overscroll-behavior-y:contain}.overscroll-y-none{overscroll-behavior-y:none}.overscroll-x-auto{overscroll-behavior-x:auto}.overscroll-x-contain{overscroll-behavior-x:contain}.overscroll-x-none{overscroll-behavior-x:none}.scroll-auto{scroll-behavior:auto}.scroll-smooth{scroll-behavior:smooth}.truncate{overflow:hidden;text-overflow:ellipsis;white-space:nowrap}.overflow-ellipsis,.text-ellipsis{text-overflow:ellipsis}.text-clip{text-overflow:clip}.hyphens-none{-webkit-hyphens:none;-moz-hyphens:none;-ms-hyphens:none;hyphens:none}.hyphens-manual{-webkit-hyphens:manual;-moz-hyphens:manual;-ms-hyphens:manual;hyphens:manual}.hyphens-auto{-webkit-hyphens:auto;-moz-hyphens:auto;-ms-hyphens:auto;hyphens:auto}.whitespace-normal{white-space:normal}.whitespace-nowrap{white-space:nowrap}.whitespace-pre{white-space:pre}.whitespace-pre-line{white-space:pre-line}.whitespace-pre-wrap{white-space:pre-wrap}.whitespace-break-spaces{white-space:break-spaces}.text-wrap{text-wrap:wrap}.text-nowrap{text-wrap:nowrap}.text-balance{text-wrap:balance}.text-pretty{text-wrap:pretty}.break-normal{overflow-wrap:normal;word-break:normal}.break-words{overflow-wrap:break-word}.break-all{word-break:break-all}.break-keep{word-break:keep-all}.rounded{border-radius:.25rem}.rounded-b{border-bottom-right-radius:.25rem;border-bottom-left-radius:.25rem}.rounded-e{border-start-end-radius:.25rem;border-end-end-radius:.25rem}.rounded-l{border-top-left-radius:.25rem;border-bottom-left-radius:.25rem}.rounded-r{border-top-right-radius:.25rem;border-bottom-right-radius:.25rem}.rounded-s{border-start-start-radius:.25rem;border-end-start-radius:.25rem}.rounded-t{border-top-left-radius:.25rem;border-top-right-radius:.25rem}.rounded-bl{border-bottom-left-radius:.25rem}.rounded-br{border-bottom-right-radius:.25rem}.rounded-ee{border-end-end-radius:.25rem}.rounded-es{border-end-start-radius:.25rem}.rounded-se{border-start-end-radius:.25rem}.rounded-ss{border-start-start-radius:.25rem}.rounded-tl{border-top-left-radius:.25rem}.rounded-tr{border-top-right-radius:.25rem}.border{border-width:1px}.border-x{border-left-width:1px;border-right-width:1px}.border-y{border-top-width:1px;border-bottom-width:1px}.border-b{border-bottom-width:1px}.border-e{border-inline-end-width:1px}.border-l{border-left-width:1px}.border-r{border-right-width:1px}.border-s{border-inline-start-width:1px}.border-t{border-top-width:1px}.border-solid{border-style:solid}.border-dashed{border-style:dashed}.border-dotted{border-style:dotted}.border-double{border-style:double}.border-hidden{border-style:hidden}.border-none{border-style:none}.bg-\[rgb\(255\,0\,0\)\]{--tw-bg-opacity: 1;background-color:rgb(255 0 0 / var(--tw-bg-opacity, 1))}.decoration-slice{-webkit-box-decoration-break:slice;box-decoration-break:slice}.decoration-clone{-webkit-box-decoration-break:clone;box-decoration-break:clone}.box-decoration-slice{-webkit-box-decoration-break:slice;box-decoration-break:slice}.box-decoration-clone{-webkit-box-decoration-break:clone;box-decoration-break:clone}.bg-fixed{background-attachment:fixed}.bg-local{background-attachment:local}.bg-scroll{background-attachment:scroll}.bg-clip-border{background-clip:border-box}.bg-clip-padding{background-clip:padding-box}.bg-clip-content{background-clip:content-box}.bg-clip-text{-webkit-background-clip:text;background-clip:text}.bg-repeat{background-repeat:repeat}.bg-no-repeat{background-repeat:no-repeat}.bg-repeat-x{background-repeat:repeat-x}.bg-repeat-y{background-repeat:repeat-y}.bg-repeat-round{background-repeat:round}.bg-repeat-space{background-repeat:space}.bg-origin-border{background-origin:border-box}.bg-origin-padding{background-origin:padding-box}.bg-origin-content{background-origin:content-box}.object-contain{object-fit:contain}.object-cover{object-fit:cover}.object-fill{object-fit:fill}.object-none{object-fit:none}.object-scale-down{object-fit:scale-down}.p-1{padding:.25rem}.px-1{padding-left:.25rem;padding-right:.25rem}.px-1\.5{padding-left:.375rem;padding-right:.375rem}.px-5{padding-left:1.25rem;padding-right:1.25rem}.text-left{text-align:left}.text-center{text-align:center}.text-right{text-align:right}.text-justify{text-align:justify}.text-start{text-align:start}.text-end{text-align:end}.align-baseline{vertical-align:baseline}.align-top{vertical-align:top}.align-middle{vertical-align:middle}.align-bottom{vertical-align:bottom}.align-text-top{vertical-align:text-top}.align-text-bottom{vertical-align:text-bottom}.align-sub{vertical-align:sub}.align-super{vertical-align:super}.align-\[alignment\]{vertical-align:alignment}.font-mono{font-family:Roboto Mono,monospace}.font-bold{font-weight:700}.uppercase{text-transform:uppercase}.lowercase{text-transform:lowercase}.capitalize{text-transform:capitalize}.normal-case{text-transform:none}.italic{font-style:italic}.not-italic{font-style:normal}.normal-nums{font-variant-numeric:normal}.ordinal{--tw-ordinal: ordinal;font-variant-numeric:var(--tw-ordinal) var(--tw-slashed-zero) var(--tw-numeric-figure) var(--tw-numeric-spacing) var(--tw-numeric-fraction)}.slashed-zero{--tw-slashed-zero: slashed-zero;font-variant-numeric:var(--tw-ordinal) var(--tw-slashed-zero) var(--tw-numeric-figure) var(--tw-numeric-spacing) var(--tw-numeric-fraction)}.lining-nums{--tw-numeric-figure: lining-nums;font-variant-numeric:var(--tw-ordinal) var(--tw-slashed-zero) var(--tw-numeric-figure) var(--tw-numeric-spacing) var(--tw-numeric-fraction)}.oldstyle-nums{--tw-numeric-figure: oldstyle-nums;font-variant-numeric:var(--tw-ordinal) var(--tw-slashed-zero) var(--tw-numeric-figure) var(--tw-numeric-spacing) var(--tw-numeric-fraction)}.proportional-nums{--tw-numeric-spacing: proportional-nums;font-variant-numeric:var(--tw-ordinal) var(--tw-slashed-zero) var(--tw-numeric-figure) var(--tw-numeric-spacing) var(--tw-numeric-fraction)}.tabular-nums{--tw-numeric-spacing: tabular-nums;font-variant-numeric:var(--tw-ordinal) var(--tw-slashed-zero) var(--tw-numeric-figure) var(--tw-numeric-spacing) var(--tw-numeric-fraction)}.diagonal-fractions{--tw-numeric-fraction: diagonal-fractions;font-variant-numeric:var(--tw-ordinal) var(--tw-slashed-zero) var(--tw-numeric-figure) var(--tw-numeric-spacing) var(--tw-numeric-fraction)}.stacked-fractions{--tw-numeric-fraction: stacked-fractions;font-variant-numeric:var(--tw-ordinal) var(--tw-slashed-zero) var(--tw-numeric-figure) var(--tw-numeric-spacing) var(--tw-numeric-fraction)}.text-\[\#336699\]\/\[\.35\]{color:#33669959}.text-blue-500{--tw-text-opacity: 1;color:rgb(59 130 246 / var(--tw-text-opacity, 1))}.text-white{--tw-text-opacity: 1;color:rgb(255 255 255 / var(--tw-text-opacity, 1))}.underline{-webkit-text-decoration-line:underline;-moz-text-decoration-line:underline;text-decoration-line:underline}.overline{-webkit-text-decoration-line:overline;-moz-text-decoration-line:overline;text-decoration-line:overline}.line-through{-webkit-text-decoration-line:line-through;-moz-text-decoration-line:line-through;text-decoration-line:line-through}.no-underline{-webkit-text-decoration-line:none;-moz-text-decoration-line:none;text-decoration-line:none}.decoration-solid{-webkit-text-decoration-style:solid;-moz-text-decoration-style:solid;text-decoration-style:solid}.decoration-double{-webkit-text-decoration-style:double;-moz-text-decoration-style:double;text-decoration-style:double}.decoration-dotted{-webkit-text-decoration-style:dotted;-moz-text-decoration-style:dotted;text-decoration-style:dotted}.decoration-dashed{-webkit-text-decoration-style:dashed;-moz-text-decoration-style:dashed;text-decoration-style:dashed}.decoration-wavy{-webkit-text-decoration-style:wavy;-moz-text-decoration-style:wavy;text-decoration-style:wavy}.antialiased{-webkit-font-smoothing:antialiased;-moz-osx-font-smoothing:grayscale}.subpixel-antialiased{-webkit-font-smoothing:auto;-moz-osx-font-smoothing:auto}.bg-blend-normal{background-blend-mode:normal}.bg-blend-multiply{background-blend-mode:multiply}.bg-blend-screen{background-blend-mode:screen}.bg-blend-overlay{background-blend-mode:overlay}.bg-blend-darken{background-blend-mode:darken}.bg-blend-lighten{background-blend-mode:lighten}.bg-blend-color-dodge{background-blend-mode:color-dodge}.bg-blend-color-burn{background-blend-mode:color-burn}.bg-blend-hard-light{background-blend-mode:hard-light}.bg-blend-soft-light{background-blend-mode:soft-light}.bg-blend-difference{background-blend-mode:difference}.bg-blend-exclusion{background-blend-mode:exclusion}.bg-blend-hue{background-blend-mode:hue}.bg-blend-saturation{background-blend-mode:saturation}.bg-blend-color{background-blend-mode:color}.bg-blend-luminosity{background-blend-mode:luminosity}.mix-blend-normal{mix-blend-mode:normal}.mix-blend-multiply{mix-blend-mode:multiply}.mix-blend-screen{mix-blend-mode:screen}.mix-blend-overlay{mix-blend-mode:overlay}.mix-blend-darken{mix-blend-mode:darken}.mix-blend-lighten{mix-blend-mode:lighten}.mix-blend-color-dodge{mix-blend-mode:color-dodge}.mix-blend-color-burn{mix-blend-mode:color-burn}.mix-blend-hard-light{mix-blend-mode:hard-light}.mix-blend-soft-light{mix-blend-mode:soft-light}.mix-blend-difference{mix-blend-mode:difference}.mix-blend-exclusion{mix-blend-mode:exclusion}.mix-blend-hue{mix-blend-mode:hue}.mix-blend-saturation{mix-blend-mode:saturation}.mix-blend-color{mix-blend-mode:color}.mix-blend-luminosity{mix-blend-mode:luminosity}.mix-blend-plus-darker{mix-blend-mode:plus-darker}.mix-blend-plus-lighter{mix-blend-mode:plus-lighter}.\!shadow{--tw-shadow: 0 1px 3px 0 rgb(0 0 0 / .1), 0 1px 2px -1px rgb(0 0 0 / .1) !important;--tw-shadow-colored: 0 1px 3px 0 var(--tw-shadow-color), 0 1px 2px -1px var(--tw-shadow-color) !important;box-shadow:var(--tw-ring-offset-shadow, 0 0 #0000),var(--tw-ring-shadow, 0 0 #0000),var(--tw-shadow)!important}.shadow{--tw-shadow: 0 1px 3px 0 rgb(0 0 0 / .1), 0 1px 2px -1px rgb(0 0 0 / .1);--tw-shadow-colored: 0 1px 3px 0 var(--tw-shadow-color), 0 1px 2px -1px var(--tw-shadow-color);box-shadow:var(--tw-ring-offset-shadow, 0 0 #0000),var(--tw-ring-shadow, 0 0 #0000),var(--tw-shadow)}.outline-none{outline:2px solid transparent;outline-offset:2px}.outline{outline-style:solid}.outline-dashed{outline-style:dashed}.outline-dotted{outline-style:dotted}.outline-double{outline-style:double}.ring{--tw-ring-offset-shadow: var(--tw-ring-inset) 0 0 0 var(--tw-ring-offset-width) var(--tw-ring-offset-color);--tw-ring-shadow: var(--tw-ring-inset) 0 0 0 calc(3px + var(--tw-ring-offset-width)) var(--tw-ring-color);box-shadow:var(--tw-ring-offset-shadow),var(--tw-ring-shadow),var(--tw-shadow, 0 0 #0000)}.ring-inset{--tw-ring-inset: inset}.blur{--tw-blur: blur(8px);-webkit-filter:var(--tw-blur) var(--tw-brightness) var(--tw-contrast) var(--tw-grayscale) var(--tw-hue-rotate) var(--tw-invert) var(--tw-saturate) var(--tw-sepia) var(--tw-drop-shadow);filter:var(--tw-blur) var(--tw-brightness) var(--tw-contrast) var(--tw-grayscale) var(--tw-hue-rotate) var(--tw-invert) var(--tw-saturate) var(--tw-sepia) var(--tw-drop-shadow)}.drop-shadow{--tw-drop-shadow: drop-shadow(0 1px 2px rgb(0 0 0 / .1)) drop-shadow(0 1px 1px rgb(0 0 0 / .06));-webkit-filter:var(--tw-blur) var(--tw-brightness) var(--tw-contrast) var(--tw-grayscale) var(--tw-hue-rotate) var(--tw-invert) var(--tw-saturate) var(--tw-sepia) var(--tw-drop-shadow);filter:var(--tw-blur) var(--tw-brightness) var(--tw-contrast) var(--tw-grayscale) var(--tw-hue-rotate) var(--tw-invert) var(--tw-saturate) var(--tw-sepia) var(--tw-drop-shadow)}.grayscale{--tw-grayscale: grayscale(100%);-webkit-filter:var(--tw-blur) var(--tw-brightness) var(--tw-contrast) var(--tw-grayscale) var(--tw-hue-rotate) var(--tw-invert) var(--tw-saturate) var(--tw-sepia) var(--tw-drop-shadow);filter:var(--tw-blur) var(--tw-brightness) var(--tw-contrast) var(--tw-grayscale) var(--tw-hue-rotate) var(--tw-invert) var(--tw-saturate) var(--tw-sepia) var(--tw-drop-shadow)}.invert{--tw-invert: invert(100%);-webkit-filter:var(--tw-blur) var(--tw-brightness) var(--tw-contrast) var(--tw-grayscale) var(--tw-hue-rotate) var(--tw-invert) var(--tw-saturate) var(--tw-sepia) var(--tw-drop-shadow);filter:var(--tw-blur) var(--tw-brightness) var(--tw-contrast) var(--tw-grayscale) var(--tw-hue-rotate) var(--tw-invert) var(--tw-saturate) var(--tw-sepia) var(--tw-drop-shadow)}.sepia{--tw-sepia: sepia(100%);-webkit-filter:var(--tw-blur) var(--tw-brightness) var(--tw-contrast) var(--tw-grayscale) var(--tw-hue-rotate) var(--tw-invert) var(--tw-saturate) var(--tw-sepia) var(--tw-drop-shadow);filter:var(--tw-blur) var(--tw-brightness) var(--tw-contrast) var(--tw-grayscale) var(--tw-hue-rotate) var(--tw-invert) var(--tw-saturate) var(--tw-sepia) var(--tw-drop-shadow)}.\!filter{-webkit-filter:var(--tw-blur) var(--tw-brightness) var(--tw-contrast) var(--tw-grayscale) var(--tw-hue-rotate) var(--tw-invert) var(--tw-saturate) var(--tw-sepia) var(--tw-drop-shadow)!important;filter:var(--tw-blur) var(--tw-brightness) var(--tw-contrast) var(--tw-grayscale) var(--tw-hue-rotate) var(--tw-invert) var(--tw-saturate) var(--tw-sepia) var(--tw-drop-shadow)!important}.filter{-webkit-filter:var(--tw-blur) var(--tw-brightness) var(--tw-contrast) var(--tw-grayscale) var(--tw-hue-rotate) var(--tw-invert) var(--tw-saturate) var(--tw-sepia) var(--tw-drop-shadow);filter:var(--tw-blur) var(--tw-brightness) var(--tw-contrast) var(--tw-grayscale) var(--tw-hue-rotate) var(--tw-invert) var(--tw-saturate) var(--tw-sepia) var(--tw-drop-shadow)}.filter-none{-webkit-filter:none;filter:none}.backdrop-blur{--tw-backdrop-blur: blur(8px);-webkit-backdrop-filter:var(--tw-backdrop-blur) var(--tw-backdrop-brightness) var(--tw-backdrop-contrast) var(--tw-backdrop-grayscale) var(--tw-backdrop-hue-rotate) var(--tw-backdrop-invert) var(--tw-backdrop-opacity) var(--tw-backdrop-saturate) var(--tw-backdrop-sepia);backdrop-filter:var(--tw-backdrop-blur) var(--tw-backdrop-brightness) var(--tw-backdrop-contrast) var(--tw-backdrop-grayscale) var(--tw-backdrop-hue-rotate) var(--tw-backdrop-invert) var(--tw-backdrop-opacity) var(--tw-backdrop-saturate) var(--tw-backdrop-sepia)}.backdrop-grayscale{--tw-backdrop-grayscale: grayscale(100%);-webkit-backdrop-filter:var(--tw-backdrop-blur) var(--tw-backdrop-brightness) var(--tw-backdrop-contrast) var(--tw-backdrop-grayscale) var(--tw-backdrop-hue-rotate) var(--tw-backdrop-invert) var(--tw-backdrop-opacity) var(--tw-backdrop-saturate) var(--tw-backdrop-sepia);backdrop-filter:var(--tw-backdrop-blur) var(--tw-backdrop-brightness) var(--tw-backdrop-contrast) var(--tw-backdrop-grayscale) var(--tw-backdrop-hue-rotate) var(--tw-backdrop-invert) var(--tw-backdrop-opacity) var(--tw-backdrop-saturate) var(--tw-backdrop-sepia)}.backdrop-invert{--tw-backdrop-invert: invert(100%);-webkit-backdrop-filter:var(--tw-backdrop-blur) var(--tw-backdrop-brightness) var(--tw-backdrop-contrast) var(--tw-backdrop-grayscale) var(--tw-backdrop-hue-rotate) var(--tw-backdrop-invert) var(--tw-backdrop-opacity) var(--tw-backdrop-saturate) var(--tw-backdrop-sepia);backdrop-filter:var(--tw-backdrop-blur) var(--tw-backdrop-brightness) var(--tw-backdrop-contrast) var(--tw-backdrop-grayscale) var(--tw-backdrop-hue-rotate) var(--tw-backdrop-invert) var(--tw-backdrop-opacity) var(--tw-backdrop-saturate) var(--tw-backdrop-sepia)}.backdrop-sepia{--tw-backdrop-sepia: sepia(100%);-webkit-backdrop-filter:var(--tw-backdrop-blur) var(--tw-backdrop-brightness) var(--tw-backdrop-contrast) var(--tw-backdrop-grayscale) var(--tw-backdrop-hue-rotate) var(--tw-backdrop-invert) var(--tw-backdrop-opacity) var(--tw-backdrop-saturate) var(--tw-backdrop-sepia);backdrop-filter:var(--tw-backdrop-blur) var(--tw-backdrop-brightness) var(--tw-backdrop-contrast) var(--tw-backdrop-grayscale) var(--tw-backdrop-hue-rotate) var(--tw-backdrop-invert) var(--tw-backdrop-opacity) var(--tw-backdrop-saturate) var(--tw-backdrop-sepia)}.backdrop-filter{-webkit-backdrop-filter:var(--tw-backdrop-blur) var(--tw-backdrop-brightness) var(--tw-backdrop-contrast) var(--tw-backdrop-grayscale) var(--tw-backdrop-hue-rotate) var(--tw-backdrop-invert) var(--tw-backdrop-opacity) var(--tw-backdrop-saturate) var(--tw-backdrop-sepia);backdrop-filter:var(--tw-backdrop-blur) var(--tw-backdrop-brightness) var(--tw-backdrop-contrast) var(--tw-backdrop-grayscale) var(--tw-backdrop-hue-rotate) var(--tw-backdrop-invert) var(--tw-backdrop-opacity) var(--tw-backdrop-saturate) var(--tw-backdrop-sepia)}.backdrop-filter-none{-webkit-backdrop-filter:none;backdrop-filter:none}.\!transition{transition-property:color,background-color,border-color,fill,stroke,opacity,box-shadow,transform,-webkit-text-decoration-color,-webkit-filter,-webkit-backdrop-filter!important;transition-property:color,background-color,border-color,text-decoration-color,fill,stroke,opacity,box-shadow,transform,filter,backdrop-filter!important;transition-property:color,background-color,border-color,text-decoration-color,fill,stroke,opacity,box-shadow,transform,filter,backdrop-filter,-webkit-text-decoration-color,-moz-text-decoration-color,-webkit-filter,-webkit-backdrop-filter!important;transition-timing-function:cubic-bezier(.4,0,.2,1)!important;transition-duration:.15s!important}.transition{transition-property:color,background-color,border-color,fill,stroke,opacity,box-shadow,transform,-webkit-text-decoration-color,-webkit-filter,-webkit-backdrop-filter;transition-property:color,background-color,border-color,text-decoration-color,fill,stroke,opacity,box-shadow,transform,filter,backdrop-filter;transition-property:color,background-color,border-color,text-decoration-color,fill,stroke,opacity,box-shadow,transform,filter,backdrop-filter,-webkit-text-decoration-color,-moz-text-decoration-color,-webkit-filter,-webkit-backdrop-filter;transition-timing-function:cubic-bezier(.4,0,.2,1);transition-duration:.15s}.ease-in{transition-timing-function:cubic-bezier(.4,0,1,1)}.ease-in-out{transition-timing-function:cubic-bezier(.4,0,.2,1)}.ease-out{transition-timing-function:cubic-bezier(0,0,.2,1)}.contain-none{contain:none}.contain-content{contain:content}.contain-strict{contain:strict}.contain-size{--tw-contain-size: size;contain:var(--tw-contain-size) var(--tw-contain-layout) var(--tw-contain-paint) var(--tw-contain-style)}.contain-inline-size{--tw-contain-size: inline-size;contain:var(--tw-contain-size) var(--tw-contain-layout) var(--tw-contain-paint) var(--tw-contain-style)}.contain-layout{--tw-contain-layout: layout;contain:var(--tw-contain-size) var(--tw-contain-layout) var(--tw-contain-paint) var(--tw-contain-style)}.contain-paint{--tw-contain-paint: paint;contain:var(--tw-contain-size) var(--tw-contain-layout) var(--tw-contain-paint) var(--tw-contain-style)}.contain-style{--tw-contain-style: style;contain:var(--tw-contain-size) var(--tw-contain-layout) var(--tw-contain-paint) var(--tw-contain-style)}.content-\[\'this-is-also-valid\]-weirdly-enough\'\]{--tw-content: "this-is-also-valid]-weirdly-enough";content:var(--tw-content)}.forced-color-adjust-auto{forced-color-adjust:auto}.forced-color-adjust-none{forced-color-adjust:none}.\[a-zA-Z0-9\:\\\\-\\\\\._\$\]{a-z-a--z0-9:\\-\\. $}.\[vite\:css\]{vite:css}@media (min-width: 640px){.sm\:container{width:100%}.sm\:container{max-width:640px}@media (min-width: 768px){.sm\:container{max-width:768px}}@media (min-width: 1024px){.sm\:container{max-width:1024px}}@media (min-width: 1280px){.sm\:container{max-width:1280px}}@media (min-width: 1536px){.sm\:container{max-width:1536px}}}.hover\:font-bold:hover{font-weight:700}.before\:hover\:text-center:hover:before{content:var(--tw-content);text-align:center}.hover\:before\:text-center:hover:before{content:var(--tw-content);text-align:center}.focus\:hover\:text-center:hover:focus{text-align:center}.hover\:focus\:text-center:focus:hover{text-align:center}@media (min-width: 640px){.sm\:underline{-webkit-text-decoration-line:underline;-moz-text-decoration-line:underline;text-decoration-line:underline}}@media (min-width: 1024px){.dark\:lg\:hover\:\[paint-order\:markers\]:hover:where([zaui-theme=dark],[zaui-theme=dark] *){paint-order:markers}}*{margin:0;padding:0;box-sizing:border-box}html,body{height:100%;position:fixed;width:100%;overflow:hidden;-ms-scroll-chaining:none;overscroll-behavior:none;-webkit-overflow-scrolling:touch;touch-action:pan-y}html{position:fixed;overflow:hidden}body{position:fixed;overflow:hidden;-ms-scroll-chaining:none;overscroll-behavior:none;-webkit-overflow-scrolling:touch;touch-action:manipulation}.zaui-page{overflow:hidden!important;-ms-scroll-chaining:none!important;overscroll-behavior:none!important;position:fixed!important;width:100%!important;height:100%!important}.page{padding:12px;height:100%;-ms-scroll-chaining:none;overscroll-behavior:none}.section-container{padding:12px;background:#fff;border-radius:8px;margin-bottom:12px}.zaui-list{overflow:hidden!important;margin:0!important}.zaui-list-item{cursor:pointer}.btm-nav{padding-bottom:0!important;min-height:56px!important;height:56px!important;position:fixed;bottom:0;width:100%}@supports (padding-bottom: env(safe-area-inset-bottom)){.page{padding-bottom:calc(12px + env(safe-area-inset-bottom))}.btm-nav{padding-bottom:env(safe-area-inset-bottom)!important}}.news-content img{max-width:100%;height:auto;border-radius:6px;margin:10px 0}.news-content h1,.news-content h2,.news-content h3,.news-content h4,.news-content h5,.news-content h6{color:#1f2937;font-weight:700;margin-top:20px;margin-bottom:10px}.news-content h1{font-size:24px}.news-content h2{font-size:22px}.news-content h3{font-size:20px}.news-content h4{font-size:18px}.news-content h5{font-size:16px}.news-content h6{font-size:14px}.news-content p{margin-bottom:16px;line-height:1.6;color:#4b5563}.news-content a{color:#047857;text-decoration:underline}.news-content ul,.news-content ol{margin-left:20px;margin-bottom:16px}.news-content li{margin-bottom:8px}.news-content figure{margin:20px 0}.news-content figure img{border-radius:8px;box-shadow:0 2px 4px #0000001a}.news-content figure figcaption{text-align:center;font-style:italic;color:#6b7280;margin-top:6px;font-size:14px}::-webkit-scrollbar{display:none}html{scrollbar-width:none}body{-ms-overflow-style:none}*{-webkit-overflow-scrolling:touch}html,body{overflow-y:auto;height:100%}.container,.page-container{overflow-y:auto}.container{padding-bottom:60px}@media (max-width: 768px){*{-webkit-tap-highlight-color:transparent}input,textarea,button,select,a{-webkit-tap-highlight-color:rgba(0,0,0,0)}}.zaui-bottom-navigation-item{padding-top:8px!important;padding-bottom:8px!important}.zaui-bottom-navigation-item-icon{margin-bottom:6px!important}.zaui-bottom-navigation-item-label{font-size:12px!important;margin-top:4px!important}

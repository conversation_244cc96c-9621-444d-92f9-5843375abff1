import React from 'react';
import { Box, Text } from 'zmp-ui';

const AttendanceStats = ({ attendanceStats, attendance, progress }) => {
    if (!attendanceStats) return null;

    return (
        <Box style={{ backgroundColor: '#f8f9fa', borderRadius: '6px', padding: '10px', marginBottom: '10px' }}>
            <Box style={{ display: 'flex', justifyContent: 'space-between', alignItems: 'center', marginBottom: '8px' }}>
                <Text style={{ fontSize: '14px', fontWeight: 'bold' }}>
                    Điểm danh: {attendanceStats.totalRecords}/{attendanceStats.totalExpectedAttendanceRecords}
                </Text>
                <Text style={{
                    fontSize: '14px',
                    fontWeight: 'bold',
                    color: attendanceStats.rate >= 80 ? '#4CAF50' :
                           attendanceStats.rate >= 60 ? '#FF9800' : '#F44336'
                }}>
                    {progress}
                </Text>
            </Box>

            <Box style={{ 
                display: 'flex', 
                justifyContent: 'space-between',
                marginBottom: '8px',
                flexWrap: 'wrap',
                gap: '4px',
                overflowX: 'hidden'
            }}>
                <Box style={{ textAlign: 'center', flex: 1, minWidth: '60px' }}>
                    <Text style={{ fontSize: '16px', fontWeight: 'bold', color: '#4CAF50' }}>
                        {attendanceStats.present}
                    </Text>
                    <Text style={{ fontSize: '11px', color: '#666' }}>Có mặt</Text>
                </Box>
                <Box style={{ textAlign: 'center', flex: 1, minWidth: '60px' }}>
                    <Text style={{ fontSize: '16px', fontWeight: 'bold', color: '#F44336' }}>
                        {attendanceStats.absent}
                    </Text>
                    <Text style={{ fontSize: '11px', color: '#666' }}>Vắng</Text>
                </Box>
                <Box style={{ textAlign: 'center', flex: 1, minWidth: '60px' }}>
                    <Text style={{ fontSize: '16px', fontWeight: 'bold', color: '#FF9800' }}>
                        {attendanceStats.late}
                    </Text>
                    <Text style={{ fontSize: '11px', color: '#666' }}>Muộn</Text>
                </Box>
                <Box style={{ textAlign: 'center', flex: 1, minWidth: '60px' }}>
                    <Text style={{ fontSize: '16px', fontWeight: 'bold', color: '#9E9E9E' }}>
                        {attendanceStats.rejected}
                    </Text>
                    <Text style={{ fontSize: '11px', color: '#666' }}>Từ chối</Text>
                </Box>
                <Box style={{ textAlign: 'center', flex: 1, minWidth: '60px' }}>
                    <Text style={{ fontSize: '16px', fontWeight: 'bold', color: '#666' }}>
                        {attendanceStats.totalRecords}
                    </Text>
                    <Text style={{ fontSize: '11px', color: '#666' }}>Buổi học</Text>
                </Box>
            </Box>

            <Box className="attendance-bar" style={{ height: '8px', backgroundColor: '#e0e0e0', borderRadius: '4px', position: 'relative', overflow: 'hidden' }}>
                <Box
                    className="attendance-progress"
                    style={{
                        width: progress,
                        height: '100%',
                        backgroundColor: attendanceStats.rate >= 80 ? '#4CAF50' :
                                       attendanceStats.rate >= 60 ? '#FF9800' : '#F44336',
                        borderRadius: '4px',
                        transition: 'width 0.3s ease'
                    }}
                />
            </Box>
        </Box>
    );
};

export default AttendanceStats; 
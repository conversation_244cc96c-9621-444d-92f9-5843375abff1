import { ICONS } from './icons';

const SESSION_STATUS_DISPLAY = {
    DA_PHAN_CONG: {
        label: 'Đã phân công',
        style: {
            backgroundColor: '#e3f0ff',
            color: '#0068ff',
            border: '1px solid #eee',
            minWidth: 90,
            textAlign: 'center',
            padding: '2px 10px',
            borderRadius: '12px',
            fontSize: '11px',
            fontWeight: 'bold',
            marginTop: 2
        },
        icon: ICONS.SUCCESS
    },
    DA_HOAN_THANH: {
        label: 'Đã hoàn thành',
        style: {
            backgroundColor: '#e8f5e8',
            color: '#34c759',
            border: '1px solid #eee',
            minWidth: 90,
            textAlign: 'center',
            padding: '2px 10px',
            borderRadius: '12px',
            fontSize: '11px',
            fontWeight: 'bold',
            marginTop: 2
        },
        icon: ICONS.TARGET
    },
    YEU_CAU_DOI_BUOI: {
        label: 'Yêu cầu đổi buổi',
        style: {
            backgroundColor: '#fff4e3',
            color: '#ff9500',
            border: '1px solid #eee',
            minWidth: 90,
            textAlign: 'center',
            padding: '2px 10px',
            borderRadius: '12px',
            fontSize: '11px',
            fontWeight: 'bold',
            marginTop: 2
        },
        icon: ICONS.REFRESH
    },
    CHUA_PHAN_CONG: {
        key: 'CHUA_PHAN_CONG',
        label: 'Chưa phân công',
        color: '#ff9500',
        bg: '#fff8e6',
        icon: ICONS.PENDING
    }
};

const SESSION_STATUS = {
    DA_PHAN_CONG: 'Đã phân công',
    DA_HOAN_THANH: 'Đã hoàn thành',
    YEU_CAU_DOI_BUOI: 'Yêu cầu đổi buổi'
};

const SESSION_STATUS_CODE = {
    DA_PHAN_CONG: 'DA_PHAN_CONG',
    DA_HOAN_THANH: 'DA_HOAN_THANH',
    YEU_CAU_DOI_BUOI: 'YEU_CAU_DOI_BUOI'
};


export { SESSION_STATUS, SESSION_STATUS_DISPLAY, SESSION_STATUS_CODE };
import React, { useState, useContext, useEffect, useCallback } from 'react';
import DateInput from '../../components/utils/DateInput';
import { Box, Text, Button, useNavigate, Modal, Input, Checkbox, Select } from 'zmp-ui';
import HeaderEdu from '../../components/HeaderEdu';
import HeaderSpacer from '../../components/utils/HeaderSpacer';
import BottomNavigationEdu from '../../components/BottomNavigationEdu';
import TabFilter from '../../components/utils/TabFilter';
import { AuthContext } from '../../context/AuthContext';
import { authApi } from '../../utils/api';
import { format, formatDistanceToNow } from 'date-fns';
import vi from 'date-fns/locale/vi';
import LoadingIndicator from '../../components/utils/LoadingIndicator';
import useApiCache from '../../hooks/useApiCache';
import useNotification from '../../hooks/useNotification';
import ZaloMessageFormatter from '../../components/utils/ZaloMessageFormatter';
import ZaloNotificationToggle from '../../components/utils/ZaloNotificationToggle';
import useInfiniteScroll from '../../hooks/useInfiniteScroll';
import useAnnouncement from '../../hooks/useAnnouncement';
import ImageWithLoading from '../../components/utils/ImageWithLoading';
import { ICONS } from '../../constants/icons';

const { Option } = Select;

const LeaveRequestApproval = () => {
    const navigate = useNavigate();
    const { user, loading: authLoading } = useContext(AuthContext);
    const notification = useNotification();
    const { sendCustomNotification } = useAnnouncement();
    const [selectedTab, setSelectedTab] = useState('pending');
    const [requestsLoading, setRequestsLoading] = useState(false);
    const [selectedRequest, setSelectedRequest] = useState(null);
    const [approvalModalVisible, setApprovalModalVisible] = useState(false);
    const [approvalAction, setApprovalAction] = useState('approved');
    const [approverNotes, setApproverNotes] = useState('');
    const [submitting, setSubmitting] = useState(false);
    const [error, setError] = useState('');
    const [refreshKey, setRefreshKey] = useState(Date.now());
    const [sendZaloNotification, setSendZaloNotification] = useState(false);

    // Filter and pagination states
    const [currentPage, setCurrentPage] = useState(1);
    const [pageLimit] = useState(15);
    const [dateFilter, setDateFilter] = useState({
        startDate: '',
        endDate: ''
    });
    const [displayDateFilter, setDisplayDateFilter] = useState({
        startDate: '',
        endDate: ''
    });
    const [showFilters, setShowFilters] = useState(false);

    // Pagination info from API response
    const [paginationInfo, setPaginationInfo] = useState({
        totalCount: 0,
        totalPages: 0,
        hasNextPage: false,
        hasPrevPage: false,
        count: 0
    });

    // Infinite scroll states
    const [allLeaveRequests, setAllLeaveRequests] = useState([]);
    const [loadingMore, setLoadingMore] = useState(false);

    // Tab configuration
    const tabs = [
        { key: 'pending', label: 'Chờ duyệt', icon: ICONS.PENDING, color: '#ff9500' },
        { key: 'approved', label: 'Đã duyệt', icon: ICONS.SUCCESS, color: '#34c759' },
        { key: 'rejected', label: 'Từ chối', icon: ICONS.ERROR, color: '#ff3b30' }
    ];

    // Kiểm tra đăng nhập và quyền
    useEffect(() => {
        if (!authLoading && !user) {
            navigate('/login', { replace: true });
        } else if (user && !user.role.includes('teacher') && !user.role.includes('TEACHER') && !user.role.includes('admin')) {
            navigate('/teacher', { replace: true });
        }
    }, [user, authLoading, navigate]);

    // API function để lấy danh sách đơn xin nghỉ với filters và pagination
    const fetchLeaveRequests = useCallback(async (page = 1, isLoadMore = false, showLoadingOverlay = false) => {
        if (!user) return [];

        if (isLoadMore) {
            setLoadingMore(true);
        } else if (!showLoadingOverlay) {
            setRequestsLoading(true);
        }

        try {
            // Build query parameters
            const params = new URLSearchParams();
            params.append('status', selectedTab);
            params.append('page', page.toString());
            params.append('limit', pageLimit.toString());
            
            // Send yyyy-mm-dd format to API
            if (dateFilter.startDate) {
                params.append('startDate', dateFilter.startDate);
            }
            if (dateFilter.endDate) {
                params.append('endDate', dateFilter.endDate);
            }

            let response;
            
            if (user.role.includes('admin')) {
                // Admin có thể xem tất cả đơn xin nghỉ của giáo viên
                response = await authApi.get(`/leave-requests/teachers?${params.toString()}`);
            } else {
                // Giáo viên chỉ xem đơn của học sinh trong lớp mình làm chủ nhiệm
                response = await authApi.get(`/leave-requests/my-students?${params.toString()}`);
            }
            
            const responseData = response.data;
            
            // Update pagination info
            setPaginationInfo({
                totalCount: responseData.totalCount || 0,
                totalPages: responseData.totalPages || 0,
                hasNextPage: responseData.hasNextPage || false,
                hasPrevPage: responseData.hasPrevPage || false,
                count: responseData.count || 0
            });

            const newData = responseData.data || [];
            
            if (isLoadMore) {
                setAllLeaveRequests(prev => [...prev, ...newData]);
            } else {
                setAllLeaveRequests(newData);
                setCurrentPage(page);
            }

            return newData;
        } catch (error) {
            console.error('Error fetching leave requests:', error);
            // Reset pagination info on error
            setPaginationInfo({
                totalCount: 0,
                totalPages: 0,
                hasNextPage: false,
                hasPrevPage: false,
                count: 0
            });
            if (!isLoadMore) {
                setAllLeaveRequests([]);
            }
            return [];
        } finally {
            if (isLoadMore) {
                setLoadingMore(false);
            } else {
                setRequestsLoading(false);
            }
        }
    }, [user, selectedTab, pageLimit, dateFilter.startDate, dateFilter.endDate]);

    // Use useApiCache for initial load only
    const {
        loading,
        refetch
    } = useApiCache(
        () => fetchLeaveRequests(1, false, true), 
        [user?._id, dateFilter.startDate, dateFilter.endDate, refreshKey], 
        {
            cacheKey: `leave_requests_${user?._id}_${dateFilter.startDate}_${dateFilter.endDate}_${refreshKey}`,
            enabled: !!user && (user.role.includes('teacher') || user.role.includes('TEACHER') || user.role.includes('admin')),
            cacheTime: 2 * 60 * 1000, // Cache 2 phút
        }
    );

    // Separate effect for tab changes - only refresh requests, not the whole page
    useEffect(() => {
        if (user) {
            fetchLeaveRequests(1, false, false);
        }
    }, [selectedTab]);

    // Load more function for infinite scroll
    const handleLoadMore = useCallback(() => {
        if (currentPage < paginationInfo.totalPages && !loadingMore) {
            fetchLeaveRequests(currentPage + 1, true);
        }
    }, [currentPage, paginationInfo.totalPages, loadingMore, fetchLeaveRequests]);

    // Infinite scroll hook
    const { observerRef } = useInfiniteScroll({
        currentPage,
        totalPages: paginationInfo.totalPages,
        loading: loadingMore,
        onLoadMore: handleLoadMore,
        enabled: true
    });

    // Ensure leaveRequests is always an array
    const safeLeaveRequests = Array.isArray(allLeaveRequests) ? allLeaveRequests : [];

    // Helper function to convert yyyy-mm-dd to dd/mm/yyyy
    const convertToDisplayFormat = (dateString) => {
        if (!dateString) return '';
        // Convert yyyy-mm-dd to dd/mm/yyyy
        const parts = dateString.split('-');
        if (parts.length === 3) {
            return `${parts[2]}/${parts[1]}/${parts[0]}`;
        }
        return dateString;
    };

    // Helper function to convert dd/mm/yyyy to yyyy-mm-dd for date input
    const convertToDateInputFormat = (displayDate) => {
        if (!displayDate) return '';
        // Convert dd/mm/yyyy to yyyy-mm-dd
        const parts = displayDate.split('/');
        if (parts.length === 3) {
            return `${parts[2]}-${parts[1].padStart(2, '0')}-${parts[0].padStart(2, '0')}`;
        }
        return displayDate;
    };

    // Helper functions for date conversion
    const formatDateForDisplay = (dateString) => {
        if (!dateString) return '';
        const date = new Date(dateString);
        return format(date, 'dd/MM/yyyy');
    };

    const formatDateForAPI = (displayDate) => {
        if (!displayDate) return '';
        // Parse dd/mm/yyyy format
        const parts = displayDate.split('/');
        if (parts.length !== 3) return '';
        const day = parts[0].padStart(2, '0');
        const month = parts[1].padStart(2, '0');
        const year = parts[2];
        
        // Validate
        const date = new Date(year, month - 1, day);
        if (date.getFullYear() != year || date.getMonth() != month - 1 || date.getDate() != day) {
            return '';
        }
        
        return `${year}-${month}-${day}`;
    };

    const validateDateInput = (value) => {
        // Allow partial input during typing
        if (value.length === 0) return true;
        
        // Check format: dd/mm/yyyy
        const regex = /^(\d{0,2})\/?(\d{0,2})\/?(\d{0,4})$/;
        const match = value.match(regex);
        
        if (!match) return false;
        
        const [, day, month, year] = match;
        
        // If complete date, validate values
        if (day && month && year && value.length === 10) {
            const dayNum = parseInt(day);
            const monthNum = parseInt(month);
            const yearNum = parseInt(year);
            
            if (dayNum < 1 || dayNum > 31) return false;
            if (monthNum < 1 || monthNum > 12) return false;
            if (yearNum < 1900 || yearNum > 2100) return false;
            
            // Validate actual date
            const date = new Date(yearNum, monthNum - 1, dayNum);
            return date.getFullYear() === yearNum && 
                   date.getMonth() === monthNum - 1 && 
                   date.getDate() === dayNum;
        }
        
        return true;
    };

    // Reset page and data when filters change
    useEffect(() => {
        setCurrentPage(1);
        setAllLeaveRequests([]);
    }, [selectedTab, dateFilter.startDate, dateFilter.endDate]);

    // Handle date filter change
    const handleDateFilterChange = (field, value) => {
        // Store yyyy-mm-dd for API
        setDateFilter(prev => ({
            ...prev,
            [field]: value
        }));
        
        // Store dd/mm/yyyy for display
        if (value) {
            setDisplayDateFilter(prev => ({
                ...prev,
                [field]: formatDateForDisplay(value)
            }));
        } else {
            setDisplayDateFilter(prev => ({
                ...prev,
                [field]: ''
            }));
        }
    };

    // Clear filters
    const clearFilters = () => {
        setDateFilter({
            startDate: '',
            endDate: ''
        });
        setDisplayDateFilter({
            startDate: '',
            endDate: ''
        });
        setCurrentPage(1);
    };

    // Pagination handlers
    const handlePageChange = (newPage) => {
        if (newPage >= 1 && newPage <= paginationInfo.totalPages) {
            setCurrentPage(newPage);
        }
    };

    // Format date
    const formatDate = (dateString) => {
        try {
            return format(new Date(dateString), 'dd/MM/yyyy', { locale: vi });
        } catch {
            return 'N/A';
        }
    };

    // Format sessions
    const formatSessions = (sessions) => {
        if (!sessions || sessions.length === 0) return 'Chưa xác định';
        const sessionMap = {
            morning: 'Sáng',
            afternoon: 'Chiều'
        };
        return sessions.map(s => sessionMap[s] || s).join(', ');
    };

    // Get status color and text
    const getStatusInfo = (status) => {
        const statusMap = {
            pending: { color: '#ff9500', text: 'Chờ duyệt', bg: '#fff8e6' },
            approved: { color: '#34c759', text: 'Đã duyệt', bg: '#e6f7ee' },
            rejected: { color: '#ff3b30', text: 'Từ chối', bg: '#ffe5e5' }
        };
        return statusMap[status] || statusMap.pending;
    };

    // Handle request click
    const handleRequestClick = (request) => {
        setSelectedRequest(request);
        setApprovalModalVisible(true);
        setApproverNotes('');
        setError('');
        // Nếu đơn đã duyệt thì mặc định là từ chối, ngược lại mặc định là duyệt
        setApprovalAction(request.status === 'approved' ? 'rejected' : 'approved');
    };

    // Handle approval action
    const handleApprovalAction = async () => {
        if (!selectedRequest) return;

        setSubmitting(true);
        setError('');

        try {
            // First update the leave request status
            await authApi.put(`/leave-requests/${selectedRequest._id}`, {
                status: approvalAction,
                approverNotes: approverNotes || (approvalAction === 'approved' ? 'Đã duyệt đơn xin nghỉ' : 'Từ chối đơn xin nghỉ')
            });

            // Create message once for both notifications
            const message = ZaloMessageFormatter({
                type: approvalAction,
                startDate: selectedRequest.startDate,
                endDate: selectedRequest.endDate,
                sessions: selectedRequest.sessions,
                reason: selectedRequest.reason,
                approverNotes: approverNotes
            });

            // Send announcement notification (always)
            if (response.data) {
                try {
                    await sendCustomNotification({
                        title: approvalAction === 'approved' ? 'Đơn xin nghỉ đã được duyệt' : 'Đơn xin nghỉ bị từ chối',
                        content: message,
                        users: [selectedRequest.requester],
                        showSuccess: false // Don't show success toast for this
                    });
                } catch (announcementError) {
                    console.error('Error sending announcement notification:', announcementError);
                    // Don't show error to user for announcement failure
                }
            }

            // Send Zalo notification if enabled and user has zaloId
            if (sendZaloNotification && selectedRequest.requester?.zaloId) {
                try {
                    await authApi.post('/zalo/send-user-message', {
                        userId: selectedRequest.requester.zaloId,
                        message: message
                    });
                } catch (zaloError) {
                    console.error('Error sending Zalo notification:', zaloError);
                    notification.error('Không thể gửi thông báo qua Zalo');
                }
            } else if (sendZaloNotification && !selectedRequest.requester?.zaloId) {
                notification.error('Không thể gửi thông báo qua Zalo vì người dùng chưa liên kết tài khoản Zalo');
            }

            setApprovalModalVisible(false);
            setSelectedRequest(null);
            setApproverNotes('');
            setSendZaloNotification(false);
            
            // Force refresh toàn bộ data bằng cách update refreshKey
            setRefreshKey(Date.now());
            setCurrentPage(1);
            
        } catch (err) {
            console.error('Error updating leave request:', err);
            setError(err.response?.data?.message || 'Có lỗi xảy ra khi cập nhật đơn xin nghỉ');
        } finally {
            setSubmitting(false);
        }
    };

    if (authLoading) {
        return (
            <Box style={{ display: 'flex', justifyContent: 'center', alignItems: 'center', minHeight: '100vh' }}>
                <LoadingIndicator />
            </Box>
        );
    }

    return (
        <Box className="container" style={{ minHeight: '100vh', display: 'flex', flexDirection: 'column', paddingBottom: '60px', backgroundColor: '#f5f5f5' }}>
            <HeaderEdu 
                title="Duyệt đơn xin nghỉ" 
                showBackButton={true} 
                onBackClick={() => navigate(-1)}
            />
            <HeaderSpacer />
            
            <Box style={{ padding: '15px', flex: 1 }}>
                {/* Filters */}
                <Box style={{ backgroundColor: 'white', borderRadius: '10px', padding: '15px', marginBottom: '15px' }}>
                    <Box style={{ display: 'flex', justifyContent: 'space-between', alignItems: 'center', marginBottom: '15px' }}>
                        <Text bold size="large">Bộ lọc</Text>
                        <Button
                            size="small"
                            onClick={() => setShowFilters(!showFilters)}
                            style={{ backgroundColor: '#f0f8ff', color: '#0068ff' }}
                        >
                            {showFilters ? 'Ẩn' : 'Hiện'} bộ lọc
                        </Button>
                    </Box>

                    {showFilters && (
                        <>
                            <Box style={{ display: 'grid', gridTemplateColumns: 'repeat(2, 1fr)', gap: '12px', marginBottom: '15px' }}>
                                <Box>
                                    <Text style={{ marginBottom: '8px', fontSize: '14px', fontWeight: '500' }}>
                                        Từ ngày:
                                    </Text>
                                    <DateInput
                                        value={dateFilter.startDate}
                                        onChange={(e) => handleDateFilterChange('startDate', e.target.value)}
                                        style={{
                                            padding: '10px',
                                            borderRadius: '6px',
                                            fontSize: '14px'
                                        }}
                                    />
                                    {displayDateFilter.startDate && (
                                        <Text style={{ fontSize: '12px', color: '#0068ff', marginTop: '4px', fontWeight: '500' }}>
                                            {ICONS.CALENDAR} {displayDateFilter.startDate}
                                        </Text>
                                    )}
                                </Box>
                                <Box>
                                    <Text style={{ marginBottom: '8px', fontSize: '14px', fontWeight: '500' }}>
                                        Đến ngày:
                                    </Text>
                                    <DateInput
                                        value={dateFilter.endDate}
                                        onChange={(e) => handleDateFilterChange('endDate', e.target.value)}
                                        min={dateFilter.startDate}
                                        style={{
                                            padding: '10px',
                                            borderRadius: '6px',
                                            fontSize: '14px'
                                        }}
                                    />
                                    {displayDateFilter.endDate && (
                                        <Text style={{ fontSize: '12px', color: '#0068ff', marginTop: '4px', fontWeight: '500' }}>
                                            {ICONS.CALENDAR} {displayDateFilter.endDate}
                                        </Text>
                                    )}
                                </Box>
                            </Box>
                            <Box style={{ display: 'flex', gap: '10px' }}>
                                <Button
                                    size="small"
                                    onClick={clearFilters}
                                    style={{ backgroundColor: '#f5f5f5', color: '#666' }}
                                >
                                    Xóa bộ lọc
                                </Button>
                                <Button
                                    size="small"
                                    onClick={() => refetch()}
                                    style={{ backgroundColor: '#0068ff', color: 'white' }}
                                >
                                    Áp dụng
                                </Button>
                            </Box>
                        </>
                    )}
                </Box>

                {/* Tab filter */}
                <TabFilter
                    tabs={tabs}
                    selectedTab={selectedTab}
                    onTabChange={setSelectedTab}
                />

                {/* Stats Summary */}
                {!loading && safeLeaveRequests.length > 0 && (
                    <Box style={{ backgroundColor: 'white', borderRadius: '10px', padding: '15px', marginBottom: '15px' }}>
                        <Text style={{ fontSize: '14px', color: '#666' }}>
                            Hiển thị {paginationInfo.count || safeLeaveRequests.length} trong tổng số {paginationInfo.totalCount || safeLeaveRequests.length} đơn
                            {paginationInfo.totalPages > 1 && ` • Trang ${currentPage}/${paginationInfo.totalPages}`}
                        </Text>
                    </Box>
                )}

                {/* Request list */}
                {(loading || requestsLoading) ? (
                    <Box style={{ display: 'flex', justifyContent: 'center', padding: '40px' }}>
                        <LoadingIndicator />
                    </Box>
                ) : safeLeaveRequests.length === 0 ? (
                    <Box style={{ backgroundColor: 'white', borderRadius: '10px', padding: '40px', textAlign: 'center' }}>
                        <Text style={{ fontSize: '48px', marginBottom: '10px' }}>📝</Text>
                        <Text bold size="large" style={{ marginBottom: '8px', color: '#666' }}>
                            Chưa có đơn xin nghỉ
                        </Text>
                        <Text style={{ color: '#999' }}>
                            {selectedTab === 'pending' ? 'Chưa có đơn xin nghỉ nào cần duyệt' : 
                             selectedTab === 'approved' ? 'Chưa có đơn xin nghỉ nào được duyệt' : 
                             'Chưa có đơn xin nghỉ nào bị từ chối'}
                        </Text>
                    </Box>
                ) : (
                    <Box style={{ display: 'flex', flexDirection: 'column', gap: '12px' }}>
                        {safeLeaveRequests.map((request) => {
                            const statusInfo = getStatusInfo(request.status);
                            const requestorName = request.requestType === 'teacher' ? 
                                request.student?.name || 'Giáo viên' : 
                                request.student?.name || 'Học sinh';
                            
                            return (
                                <Box
                                    key={request._id}
                                    onClick={() => handleRequestClick(request)}
                                    style={{
                                        backgroundColor: 'white',
                                        borderRadius: '10px',
                                        padding: '15px',
                                        cursor: 'pointer',
                                        border: `1px solid ${statusInfo.color}20`,
                                        boxShadow: '0 2px 8px rgba(0,0,0,0.1)'
                                    }}
                                >
                                    <Box style={{ display: 'flex', justifyContent: 'space-between', alignItems: 'flex-start', marginBottom: '10px' }}>
                                        <Box style={{ flex: 1 }}>
                                            <Text bold size="large" style={{ marginBottom: '5px' }}>
                                                {requestorName}
                                            </Text>
                                            <Text style={{ fontSize: '12px', color: '#666', marginBottom: '8px' }}>
                                                {request.requestType === 'teacher' ? `${ICONS.TEACHER} Giáo viên` : `${ICONS.STUDENT} Học sinh`} • 
                                                {request.class?.name && ` ${request.class.name} • `}
                                                {formatDistanceToNow(new Date(request.createdAt), { locale: vi, addSuffix: true })}
                                            </Text>
                                        </Box>
                                        <Box
                                            style={{
                                                backgroundColor: statusInfo.bg,
                                                color: statusInfo.color,
                                                padding: '4px 8px',
                                                borderRadius: '12px',
                                                fontSize: '12px',
                                                fontWeight: 'bold'
                                            }}
                                        >
                                            {statusInfo.text}
                                        </Box>
                                    </Box>
                                    
                                    <Box style={{ marginBottom: '10px' }}>
                                        <Text style={{ fontSize: '14px', marginBottom: '5px' }}>
                                            <Text bold>Thời gian:</Text> {formatDate(request.startDate)} 
                                            {request.startDate !== request.endDate && ` - ${formatDate(request.endDate)}`}
                                        </Text>
                                        <Text style={{ fontSize: '14px', marginBottom: '5px' }}>
                                            <Text bold>Buổi:</Text> {formatSessions(request.sessions)}
                                        </Text>
                                    </Box>
                                    
                                    <Text style={{ fontSize: '14px', color: '#333', lineHeight: 1.4 }}>
                                        <Text bold>Lý do:</Text> {request.reason}
                                    </Text>
                                    
                                    {request.approverNotes && (
                                        <Box style={{ marginTop: '10px', padding: '8px', backgroundColor: '#f9f9f9', borderRadius: '6px' }}>
                                            <Text style={{ fontSize: '12px', color: '#666' }}>
                                                <Text bold>Ghi chú duyệt:</Text> {request.approverNotes}
                                            </Text>
                                        </Box>
                                    )}
                                </Box>
                            );
                        })}
                    </Box>
                )}

                {/* Infinite scroll loading indicator */}
                {loadingMore && (
                    <Box style={{ display: 'flex', justifyContent: 'center', padding: '20px' }}>
                        <LoadingIndicator />
                    </Box>
                )}
                
                {/* Infinite scroll observer */}
                <div ref={observerRef} style={{ height: '20px' }} />
            </Box>

            {/* Approval Modal */}
            <Modal
                visible={approvalModalVisible}
                title="Duyệt đơn xin nghỉ"
                onClose={() => {
                    setApprovalModalVisible(false);
                    setSelectedRequest(null);
                    setApproverNotes('');
                    setError('');
                    setSendZaloNotification(false);
                }}
                actions={[
                    { text: 'Hủy', close: true, danger: true },
                    { 
                        text: approvalAction === 'approved' ? 'Duyệt đơn' : (selectedRequest?.status === 'approved' ? 'Hủy duyệt' : 'Từ chối đơn'), 
                        close: false, 
                        onClick: handleApprovalAction,
                        disabled: submitting,
                        style: { backgroundColor: approvalAction === 'approved' ? '#34c759' : '#ff3b30' }
                    }
                ]}
            >
                {selectedRequest && (
                    <Box style={{ padding: '20px' }}>
                        {error && (
                            <Box style={{ backgroundColor: '#fee', padding: '12px', borderRadius: '8px', marginBottom: '15px', border: '1px solid #fcc' }}>
                                <Text style={{ color: '#c33', fontSize: '14px' }}>{error}</Text>
                            </Box>
                        )}

                        <Box style={{ marginBottom: '20px' }}>
                            <Text bold size="large" style={{ marginBottom: '10px' }}>
                                Chi tiết đơn xin nghỉ
                            </Text>
                            
                            <Box style={{ backgroundColor: '#f9f9f9', padding: '15px', borderRadius: '8px', marginBottom: '15px' }}>
                                <Text style={{ marginBottom: '8px' }}>
                                    <Text bold>Người xin nghỉ:</Text> {selectedRequest.requester?.name || 'N/A'}
                                </Text>
                                <Text style={{ marginBottom: '8px' }}>
                                    <Text bold>Loại:</Text> {selectedRequest.requestType === 'teacher' ? 'Giáo viên' : 'Học sinh'}
                                </Text>
                                {selectedRequest.class && (
                                    <Text style={{ marginBottom: '8px' }}>
                                        <Text bold>Lớp:</Text> {selectedRequest.class.name}
                                    </Text>
                                )}
                                <Text style={{ marginBottom: '8px' }}>
                                    <Text bold>Thời gian:</Text> {formatDate(selectedRequest.startDate)} 
                                    {selectedRequest.startDate !== selectedRequest.endDate && ` - ${formatDate(selectedRequest.endDate)}`}
                                </Text>
                                <Text style={{ marginBottom: '8px' }}>
                                    <Text bold>Buổi:</Text> {formatSessions(selectedRequest.sessions)}
                                </Text>
                                <Text>
                                    <Text bold>Lý do:</Text> {selectedRequest.reason}
                                </Text>
                            </Box>

                            {/* Attachments */}
                            {selectedRequest.attachments && selectedRequest.attachments.length > 0 && (
                                <Box style={{ marginBottom: '15px' }}>
                                    <Text bold style={{ marginBottom: '8px' }}>Tài liệu đính kèm:</Text>
                                    <Box style={{ display: 'flex', gap: '10px', flexWrap: 'wrap' }}>
                                        {selectedRequest.attachments.map((attachment, index) => (
                                            <ImageWithLoading
                                                key={index}
                                                src={attachment.data}
                                                alt={`Attachment ${index + 1}`}
                                                style={{
                                                    width: '80px',
                                                    height: '80px',
                                                    objectFit: 'cover',
                                                    borderRadius: '8px',
                                                    border: '1px solid #ddd',
                                                    cursor: 'pointer'
                                                }}
                                                onClick={() => window.open(attachment.data, '_blank')}
                                                placeholder="📷\nLỗi tải ảnh"
                                            />
                                        ))}
                                    </Box>
                                </Box>
                            )}
                        </Box>

                        {/* Action selection */}
                        <Box style={{ marginBottom: '15px' }}>
                            <Text bold style={{ marginBottom: '10px' }}>Quyết định:</Text>
                            <Box style={{ display: 'flex', gap: '10px' }}>
                                {selectedRequest.status !== 'approved' && (
                                    <Button
                                        onClick={() => setApprovalAction('approved')}
                                        style={{
                                            backgroundColor: approvalAction === 'approved' ? '#34c759' : '#f0f0f0',
                                            color: approvalAction === 'approved' ? 'white' : '#666',
                                            border: 'none'
                                        }}
                                    >
                                                                                    {ICONS.SUCCESS} Duyệt
                                    </Button>
                                )}
                                <Button
                                    onClick={() => setApprovalAction('rejected')}
                                    style={{
                                        backgroundColor: approvalAction === 'rejected' ? '#ff3b30' : '#f0f0f0',
                                        color: approvalAction === 'rejected' ? 'white' : '#666',
                                        border: 'none'
                                    }}
                                >
                                                                                {ICONS.ERROR} {selectedRequest.status === 'approved' ? 'Hủy duyệt' : 'Từ chối'}
                                </Button>
                            </Box>
                        </Box>

                        {/* Notes */}
                        <Box style={{ marginBottom: '15px' }}>
                            <Text bold style={{ marginBottom: '8px' }}>
                                Ghi chú (tùy chọn):
                            </Text>
                            <Input
                                type="textarea"
                                placeholder={`Nhập ghi chú cho quyết định ${approvalAction === 'approved' ? 'duyệt' : (selectedRequest?.status === 'approved' ? 'hủy duyệt' : 'từ chối')}...`}
                                value={approverNotes}
                                onChange={(e) => setApproverNotes(e.target.value)}
                                style={{ minHeight: '80px' }}
                            />
                        </Box>

                        {/* Zalo Notification Option */}
                        <ZaloNotificationToggle
                            checked={sendZaloNotification}
                            onChange={setSendZaloNotification}
                            hasZaloId={selectedRequest?.requester?.zaloId}
                        />
                    </Box>
                )}
            </Modal>

            <BottomNavigationEdu />
        </Box>
    );
};

export default LeaveRequestApproval; 
import React from 'react';
import { EXAM_TYPES, TIME_SLOTS } from '../../constants/exam';
import { formatDate } from '../../utils/dateUtils';
import { ICONS } from '../../constants/icons';

const ZaloMessageFormatter = ({ type, startDate, endDate, sessions, reason, approverNotes }) => {

    const formatSessions = (sessions) => {
        if (!sessions || sessions.length === 0) return 'Chưa xác định';
        const sessionMap = {
            morning: 'Sáng',
            afternoon: 'Chiều'
        };
        return sessions.map(s => sessionMap[s] || s).join(', ');
    };

    const getEmoji = (type) => {
        return type === 'approved' ? ICONS.SUCCESS : ICONS.ERROR;
    };

    const getTitle = (type) => {
        return type === 'approved'
            ? 'ĐƠN XIN NGHỈ ĐÃ ĐƯỢC DUYỆT'
            : 'ĐƠN XIN NGHỈ ĐÃ BỊ TỪ CHỐI';
    };

    const getStatusEmoji = (type) => {
        return type === 'approved' ? ICONS.SUCCESS : ICONS.ERROR;
    };

    // Build message parts
    const parts = [];

    // Header
    parts.push(`${getEmoji(type)} *${getTitle(type)}*`);

    // Time
            parts.push(`${ICONS.CALENDAR} Thời gian: ${formatDate(startDate)}${startDate !== endDate ? ` - ${formatDate(endDate)}` : ''}`);

    // Sessions
    parts.push(`⏰ Buổi: ${formatSessions(sessions)}`);

    // Reason if exists
    if (reason) {
        parts.push(`📝 Lý do: ${reason}`);
    }

    // Approver notes if exists
    if (approverNotes) {
        parts.push(`💬 Ghi chú: ${approverNotes}`);
    }

    // Footer
    parts.push('_Tin nhắn tự động từ hệ thống_');

    // Join all parts with newlines
    return parts.join('\n');
};

export const formatExamSupervisionMessage = ({ examType, schoolYear, sessions, notes }) => {
    const sessionDetails = sessions.map(session => {
        const date = new Date(session.date).toLocaleDateString('vi-VN');
        const timeSlot = TIME_SLOTS[session.timeSlot] || session.timeSlot;
        const room = session.room ? `Phòng ${session.room}` : '';
        const subject = session.subject ? `Môn ${session.subject}` : '';

        return `${ICONS.CALENDAR} ${date} - ${timeSlot}${room ? ` - ${room}` : ''}${subject ? ` - ${subject}` : ''}`;
    }).join('\n');

    const defaultNotes = `⚠️ Lưu ý:
- Thầy Cô không tự ý đổi buổi coi thi.
- Trường hợp đặc biệt phải báo cho HT trước 2 ngày so với ngày được phân công
- Đề nghị Thầy Cô lưu ý thực hiện.`;

    return `🎓 THÔNG BÁO PHÂN CÔNG COI THI

Bạn đã được phân công coi thi ${EXAM_TYPES[examType] || examType} năm học ${schoolYear}

Chi tiết các buổi thi:
${sessionDetails}

${notes ? `📝 Ghi chú:\n${notes}\n\n` : ''}${defaultNotes}

_Tin nhắn tự động từ hệ thống_`;
};

export const formatExamChangeRequestMessage = ({
    teacher,
    examType,
    schoolYear,
    originalDate,
    originalTimeSlot,
    proposedNewDate,
    proposedNewTimeSlot,
    reason,
    status = 'PENDING',
    approvalNotes
}) => {
    const originalDateFormatted = formatDate(originalDate);
    const proposedDateFormatted = formatDate(proposedNewDate);
    const originalTimeSlotText = TIME_SLOTS[originalTimeSlot] || originalTimeSlot;
    const proposedTimeSlotText = TIME_SLOTS[proposedNewTimeSlot] || proposedNewTimeSlot;
    const examTypeText = EXAM_TYPES[examType] || examType;

    const statusEmoji = status === 'APPROVED' ? ICONS.SUCCESS : status === 'REJECTED' ? ICONS.ERROR : ICONS.PENDING;
    const statusText = status === 'APPROVED' ? 'ĐÃ ĐƯỢC DUYỆT' :
        status === 'REJECTED' ? 'ĐÃ BỊ TỪ CHỐI' :
            'ĐANG CHỜ XỬ LÝ';

    return `${statusEmoji} YÊU CẦU ĐỔI BUỔI COI THI ${statusText}

👤 Giáo viên: ${teacher.name}
🎓 Kỳ thi: ${examTypeText} - ${schoolYear}

        ${ICONS.CALENDAR} Buổi thi hiện tại:
• Ngày: ${originalDateFormatted}
• Ca: ${originalTimeSlotText}

🔄 Buổi thi đề xuất:
• Ngày: ${proposedDateFormatted}
• Ca: ${proposedTimeSlotText}

${(status === 'APPROVED' || status === 'REJECTED') ? `💬 Ghi chú: ${approvalNotes}.` : `📝 Lý do: ${reason}.`}

${status === 'PENDING' ? '⏳ Yêu cầu đang chờ phê duyệt từ ban giám hiệu.\n_Tin nhắn tự động từ hệ thống_' : '_Tin nhắn tự động từ hệ thống_'}`;
};

export default ZaloMessageFormatter; 
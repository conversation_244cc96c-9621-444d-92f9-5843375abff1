import React, { useState, useEffect } from 'react';
import { Box, Text, Input } from 'zmp-ui';
import { authApi } from '../../utils/api';
import useNotification from '../../hooks/useNotification';
import { DEPARTMENT_GROUPS } from '../../constants/exam';
import SmartSelect from './SmartSelect';

const TeacherSelector = ({ 
    value, 
    onChange, 
    label = "Chọn giáo viên",
    required = false,
    disabled = false,
    style = {}
}) => {
    const notification = useNotification();
    const [teachers, setTeachers] = useState([]);
    const [pagination, setPagination] = useState({
        current: 1,
        pages: 1,
        total: 0
    });
    const [filters, setFilters] = useState({
        search: '',
        department: '',
        page: 1,
        limit: 20
    });
    const [loading, setLoading] = useState(false);

    // Fetch teachers list with pagination and filters
    const fetchTeachers = async () => {
        setLoading(true);
        try {
            const queryParams = new URLSearchParams({
                page: filters.page.toString(),
                limit: filters.limit.toString(),
                ...(filters.search && { search: filters.search }),
                ...(filters.department && { department: filters.department })
            });

            const response = await authApi.get(`/directory/teachers?${queryParams}`);
            if (response.data) {
                setTeachers(response.data.teachers || []);
                setPagination(response.data.pagination || {
                    current: 1,
                    pages: 1,
                    total: 0
                });
            }
        } catch (error) {
            console.error('Error fetching teachers:', error);
            notification.showError('Lỗi', 'Không thể tải danh sách giáo viên');
        } finally {
            setLoading(false);
        }
    };

    // Handle filter changes
    const handleFilterChange = (field, value) => {
        setFilters(prev => ({
            ...prev,
            [field]: value,
            page: 1 // Reset to first page when filters change
        }));
    };

    // Fetch teachers with debounce
    useEffect(() => {
        const timer = setTimeout(() => {
            fetchTeachers();
        }, 500);

        return () => clearTimeout(timer);
    }, [filters]);

    // Reset filters when value is cleared
    useEffect(() => {
        if (!value) {
            setFilters(prev => ({
                ...prev,
                search: '',
                department: '',
                page: 1
            }));
        }
    }, [value]);

    // Transform data for SmartSelect
    const departmentOptions = [
        { value: '', name: 'Tất cả bộ môn' },
        ...Object.entries(DEPARTMENT_GROUPS).map(([key, label]) => ({
            value: label,
            name: label
        }))
    ];

    const teacherOptions = teachers.map(teacher => ({
        value: teacher._id,
        name: teacher.name || 'Chưa có tên',
        department: teacher.department,
        label: `${teacher.name || 'Chưa có tên'}${teacher.department ? ` - ${teacher.department}` : ''}`
    }));

    return (
        <Box style={style}>
            {/* Department Filter */}
            <SmartSelect
                label="Bộ môn"
                placeholder="Chọn bộ môn"
                value={filters.department}
                onChange={(value) => handleFilterChange('department', value)}
                options={departmentOptions}
                disabled={disabled}
                style={{ marginBottom: '10px' }}
                noDataMessage="Không có bộ môn nào"
            />

            {/* Search Input */}
            <Box style={{ marginBottom: '15px' }}>
                <Text bold style={{ fontSize: '14px', marginBottom: '8px', color: '#333' }}>
                    Tìm kiếm giáo viên
                </Text>
                <Input
                    placeholder="Nhập tên giáo viên..."
                    value={filters.search}
                    onChange={(e) => handleFilterChange('search', e.target.value)}
                    disabled={disabled}
                    prefix="🔍"
                />
            </Box>

            {/* Teacher Selection */}
            <SmartSelect
                label={label}
                placeholder={loading ? "Đang tải..." : "Chọn giáo viên"}
                value={value}
                onChange={onChange}
                options={teacherOptions}
                disabled={disabled || loading}
                required={required}
                renderOption={(option) => option.label}
                searchKey="name"
                noDataMessage="Không có giáo viên nào"
                style={{ marginBottom: '10px' }}
            />

            {/* Pagination Info */}
            <Text style={{ fontSize: '12px', color: '#666', textAlign: 'right' }}>
                Trang {pagination.current}/{pagination.pages} ({pagination.total} giáo viên)
            </Text>
        </Box>
    );
};

export default TeacherSelector; 
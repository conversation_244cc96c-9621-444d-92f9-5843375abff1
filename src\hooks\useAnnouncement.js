import { useState } from 'react';
import { 
    postAnnouncement, 
    postExamChangeRequestNotification, 
    postExamSupervisionNotification,
    postCustomNotification 
} from '../utils/announcementService';
import useNotification from './useNotification';

const useAnnouncement = () => {
    const [loading, setLoading] = useState(false);
    const notification = useNotification();

    /**
     * Post a general announcement
     */
    const sendAnnouncement = async ({ title, content, userIds, type, showSuccess = true }) => {
        setLoading(true);
        try {
            const result = await postAnnouncement({ title, content, userIds, type });
            if (showSuccess) {
                notification.showSuccess('Thành công', 'Đã gửi thông báo');
            }
            return result;
        } catch (error) {
            console.error('Error sending announcement:', error);
            notification.showError('Lỗi', 'Không thể gửi thông báo');
            throw error;
        } finally {
            setLoading(false);
        }
    };

    /**
     * Send exam change request notification
     */
    const sendExamChangeNotification = async (params) => {
        setLoading(true);
        try {
            const result = await postExamChangeRequestNotification(params);
            notification.showSuccess('Thành công', 'Đã gửi thông báo về quyết định đổi lịch thi');
            return result;
        } catch (error) {
            console.error('Error sending exam change notification:', error);
            notification.showError('Lỗi', 'Không thể gửi thông báo');
            throw error;
        } finally {
            setLoading(false);
        }
    };

    /**
     * Send exam supervision assignment notification
     */
    const sendSupervisionNotification = async (params) => {
        setLoading(true);
        try {
            const result = await postExamSupervisionNotification(params);
            notification.showSuccess('Thành công', 'Đã gửi thông báo phân công coi thi');
            return result;
        } catch (error) {
            console.error('Error sending supervision notification:', error);
            notification.showError('Lỗi', 'Không thể gửi thông báo');
            throw error;
        } finally {
            setLoading(false);
        }
    };

    /**
     * Send custom notification
     */
    const sendCustomNotification = async ({ title, content, users, showSuccess = true }) => {
        setLoading(true);
        try {
            const result = await postCustomNotification({ title, content, users });
            if (showSuccess) {
                notification.showSuccess('Thành công', 'Đã gửi thông báo');
            }
            return result;
        } catch (error) {
            console.error('Error sending custom notification:', error);
            notification.showError('Lỗi', 'Không thể gửi thông báo');
            throw error;
        } finally {
            setLoading(false);
        }
    };

    return {
        loading,
        sendAnnouncement,
        sendExamChangeNotification,
        sendSupervisionNotification,
        sendCustomNotification
    };
};

export default useAnnouncement; 
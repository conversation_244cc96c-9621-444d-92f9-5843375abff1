import React from 'react';
import { Box, Text, Checkbox } from 'zmp-ui';
import { ICONS } from '../../constants/icons';

const ZaloNotificationToggle = ({ checked, onChange, hasZaloId }) => {
    return (
        <Box style={{ marginBottom: '15px' }}>
            <Checkbox
                checked={checked}
                onChange={(e) => onChange(e.target.checked)}
                label="Gửi thông báo qua Zalo"
            />
            {checked && !hasZaloId && (
                <Text style={{ fontSize: '12px', color: '#ff3b30', marginTop: '4px' }}>
                    {ICONS.WARNING} Người dùng chưa liên kết tài khoản <PERSON>
                </Text>
            )}
        </Box>
    );
};

export default ZaloNotificationToggle; 
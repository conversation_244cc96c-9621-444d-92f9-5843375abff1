import React, { useState, useEffect, useContext } from 'react';
import { Box, Text, Button, useNavigate, Modal, Input, Select } from 'zmp-ui';
import { AuthContext } from '../../context/AuthContext';
import { authApi } from '../../utils/api';
import HeaderEdu from '../../components/HeaderEdu';
import HeaderSpacer from '../../components/utils/HeaderSpacer';
import BottomNavigationEdu from '../../components/BottomNavigationEdu';
import LoadingIndicator from '../../components/utils/LoadingIndicator';
import ZaloNotificationToggle from '../../components/utils/ZaloNotificationToggle';
import TabFilter from '../../components/utils/TabFilter';
import useNotification from '../../hooks/useNotification';
import { EXAM_TYPES, TIME_SLOTS, TEACHER_EXAM_SCHEDULE_TABS } from '../../constants/exam';
import { SESSION_STATUS, SESSION_STATUS_DISPLAY, SESSION_STATUS_CODE } from '../../constants/sessionStatus';
import { formatDate } from '../../utils/dateUtils';
import { formatExamChangeRequestMessage } from '../../components/utils/ZaloMessageFormatter';
import { useSchoolYear } from '../../context/SchoolYearContext';
import useAnnouncement from '../../hooks/useAnnouncement';

const { Option } = Select;

const TeacherExamSchedule = () => {
    const navigate = useNavigate();
    const { user } = useContext(AuthContext);
    const notification = useNotification();
    const { sendCustomNotification } = useAnnouncement();
    const [loading, setLoading] = useState(true);
    const [dataLoading, setDataLoading] = useState(false);
    const [assignments, setAssignments] = useState([]);
    const [currentMonth, setCurrentMonth] = useState(new Date().getMonth());
    const [currentYear, setCurrentYear] = useState(new Date().getFullYear());
    const { selectedSchoolYear } = useSchoolYear();    
    const [selectedTab, setSelectedTab] = useState('all');

    // Change request modal states
    const [changeRequestModalVisible, setChangeRequestModalVisible] = useState(false);
    const [selectedSession, setSelectedSession] = useState(null);
    const [changeRequestData, setChangeRequestData] = useState({
        reason: '',
        proposedNewDate: '',
        proposedNewTimeSlot: ''
    });
    const [sendZaloNotification, setSendZaloNotification] = useState(false);
    const [selectedUserData, setSelectedUserData] = useState(null);
    const [submitting, setSubmitting] = useState(false);

    const monthNames = [
        'Tháng 1', 'Tháng 2', 'Tháng 3', 'Tháng 4', 'Tháng 5', 'Tháng 6',
        'Tháng 7', 'Tháng 8', 'Tháng 9', 'Tháng 10', 'Tháng 11', 'Tháng 12'
    ];

    const weekDays = ['T2', 'T3', 'T4', 'T5', 'T6', 'T7', 'CN'];

    const fetchAssignments = async (isInitialLoad = false) => {
        if (!selectedSchoolYear) {
            console.log('No school year selected, skipping fetch');
            return;
        }

        if (isInitialLoad) {
            setLoading(true);
        } else {
            setDataLoading(true);
        }
        try {
            const queryParams = new URLSearchParams();
            queryParams.append('schoolYear', selectedSchoolYear);
            
            // Add status filter from tab
            if (selectedTab !== 'all') {
                queryParams.append('status', selectedTab);
            }

            const response = await authApi.get(`/exam-supervisions/my-assignments?${queryParams.toString()}`);
            if (response.data) {
                setAssignments(response.data);
            }
        } catch (error) {
            console.error('Error fetching assignments:', error);
            notification.showError('Lỗi', 'Không thể tải lịch thi');
        } finally {
            setLoading(false);
            setDataLoading(false);
        }
    };

    useEffect(() => {
        if (selectedSchoolYear) {
            fetchAssignments(true);
        }
    }, [selectedSchoolYear]);

    useEffect(() => {
        if (selectedSchoolYear && loading === false) { // Only fetch when not initial load
            fetchAssignments(false);
        }
    }, [selectedTab]);

    // Fetch user data when modal opens
    const fetchUserData = async (userId) => {
        if (userId) {
            try {
                const response = await authApi.get(`/directory/user/${userId}`);
                setSelectedUserData(response.data.data);
            } catch (error) {
                console.error('Error fetching user data:', error);
                setSelectedUserData(null);
            }
        } else {
            setSelectedUserData(null);
        }
    };

    // Handle change request modal
    const handleChangeRequest = (session, assignment) => {
        setSelectedSession({ ...session, assignment });
        setChangeRequestData({
            reason: '',
            proposedNewDate: '',
            proposedNewTimeSlot: ''
        });
        setSendZaloNotification(false);
        setSelectedUserData(null);
        setChangeRequestModalVisible(true);

        // Fetch user data for the assignment creator
        console.log('assignment:', assignment);
        if (assignment?.createdBy) {
            fetchUserData(assignment.createdBy);
        }
    };

    const handleSubmitChangeRequest = async () => {
        // Kiểm tra dữ liệu đầu vào
        if (
            !changeRequestData.reason ||
            !changeRequestData.proposedNewDate ||
            !changeRequestData.proposedNewTimeSlot
        ) {
            notification.showError('Lỗi', 'Vui lòng điền đầy đủ thông tin');
            return;
        }

        setSubmitting(true);

        try {
            const payload = {
                supervisionId: selectedSession.assignment._id,
                sessionId: selectedSession._id,
                reason: changeRequestData.reason,
                proposedNewDate: changeRequestData.proposedNewDate,
                proposedNewTimeSlot: changeRequestData.proposedNewTimeSlot,
                sendZaloNotification: sendZaloNotification,
            };

            // Gửi yêu cầu đổi buổi thi
            const response = await authApi.post('/exam-change-requests', payload);

            // Cập nhật cục bộ state assignments dựa trên updatedSession
            if (response.data.updatedSession) {
                const updatedAssignments = assignments.map((assignment) => {
                    if (assignment._id === selectedSession.assignment._id) {
                        const updatedSessions = assignment.sessions.map((session) => {
                            if (session._id === selectedSession._id) {
                                return {
                                    ...session,
                                    ...response.data.updatedSession, // Cập nhật toàn bộ thông tin từ updatedSession
                                };
                            }
                            return session;
                        });
                        return { ...assignment, sessions: updatedSessions };
                    }
                    return assignment;
                });
                setAssignments(updatedAssignments);
            }

            // Create message once for both notifications
            const message = formatExamChangeRequestMessage({
                teacher: selectedSession.assignment.teacher,
                examType: selectedSession.assignment.examType,
                schoolYear: selectedSession.assignment.schoolYear,
                originalDate: selectedSession.date,
                originalTimeSlot: selectedSession.timeSlot,
                proposedNewDate: changeRequestData.proposedNewDate,
                proposedNewTimeSlot: changeRequestData.proposedNewTimeSlot,
                reason: changeRequestData.reason,
                status: 'PENDING',
                approvalNotes: ''
            });

            // Send announcement notification (always)
            if (response.data) {
                try {
                    await sendCustomNotification({
                        title: 'Yêu cầu đổi buổi coi thi',
                        content: message,
                        users: [selectedUserData]
                    });
                } catch (announcementError) {
                    console.error('Error sending announcement notification:', announcementError);
                    // Don't show error to user for announcement failure
                }
            }

            // Send Zalo notification if enabled and user has Zalo ID
            if (sendZaloNotification && selectedUserData?.zaloId) {
                try {
                    await authApi.post('/zalo/send-user-message', {
                        userId: selectedUserData.zaloId,
                        message: message
                    });
                } catch (zaloError) {
                    console.error('Error sending Zalo notification:', zaloError);
                    notification.showError('Lỗi', 'Không thể gửi thông báo qua Zalo');
                }
            }

            // Hiển thị thông báo thành công
            notification.showSuccess('Thành công', 'Đã gửi yêu cầu đổi buổi thi');

            // Đóng modal và reset state
            setChangeRequestModalVisible(false);
            setSelectedSession(null);
            setChangeRequestData({
                reason: '',
                proposedNewDate: '',
                proposedNewTimeSlot: '',
            });
            setSendZaloNotification(false);
        } catch (error) {
            // Xử lý lỗi
            let errorMessage = 'Không thể gửi yêu cầu đổi buổi thi';
            if (error.response?.data) {
                const { errors, msg, message } = error.response.data;
                if (errors?.length > 0) {
                    errorMessage = errors[0].msg;
                } else if (msg) {
                    errorMessage = msg;
                } else if (message) {
                    errorMessage = message;
                }
            }
            notification.showError('Lỗi', errorMessage);
        } finally {
            setSubmitting(false);
        }
    };
    // Generate calendar for current month
    const generateCalendar = () => {
        const firstDay = new Date(currentYear, currentMonth, 1);
        const lastDay = new Date(currentYear, currentMonth + 1, 0);
        const startDate = new Date(firstDay);
        startDate.setDate(startDate.getDate() - firstDay.getDay() + 1); // Start from Monday

        const calendar = [];
        const today = new Date();

        for (let week = 0; week < 6; week++) {
            const weekDays = [];
            for (let day = 0; day < 7; day++) {
                const currentDate = new Date(startDate);
                currentDate.setDate(startDate.getDate() + week * 7 + day);

                const isCurrentMonth = currentDate.getMonth() === currentMonth;
                const isToday = currentDate.toDateString() === today.toDateString();

                // Get sessions for this day
                const sessionsOnDay = getSessionsForDate(currentDate);
                const hasMorningSession = sessionsOnDay.some(s => s.timeSlot === 'morning');
                const hasAfternoonSession = sessionsOnDay.some(s => s.timeSlot === 'afternoon');
                const hasSession = sessionsOnDay.length > 0;

                weekDays.push({
                    date: currentDate.getDate(),
                    fullDate: new Date(currentDate),
                    isCurrentMonth,
                    isToday,
                    hasSession: hasSession && isCurrentMonth,
                    morningSessions: isCurrentMonth ? (hasMorningSession ? 1 : 0) : 0,
                    afternoonSessions: isCurrentMonth ? (hasAfternoonSession ? 1 : 0) : 0,
                    totalSessions: isCurrentMonth ? sessionsOnDay.length : 0
                });
            }
            calendar.push(weekDays);
        }

        return calendar;
    };

    const getSessionsForDate = (date) => {
        const sessions = [];
        assignments.forEach(assignment => {
            assignment.sessions.forEach(session => {
                const sessionDate = new Date(session.date);
                if (sessionDate.toDateString() === date.toDateString()) {
                    sessions.push({
                        ...session,
                        examType: assignment.examType,
                        schoolYear: assignment.schoolYear,
                        assignment: assignment
                    });
                }
            });
        });
        return sessions;
    };

    const getSessionsForDay = (dayName) => {
        const sessions = [];
        assignments.forEach(assignment => {
            assignment.sessions.forEach(session => {
                const sessionDate = new Date(session.date);
                const sessionDay = ['Sunday', 'Monday', 'Tuesday', 'Wednesday', 'Thursday', 'Friday', 'Saturday'][sessionDate.getDay()];
                if (sessionDay === dayName) {
                    sessions.push({
                        ...session,
                        examType: assignment.examType,
                        schoolYear: assignment.schoolYear,
                        assignment: assignment
                    });
                }
            });
        });
        return sessions.sort((a, b) => {
            if (a.timeSlot === b.timeSlot) return 0;
            return a.timeSlot === 'morning' ? -1 : 1;
        });
    };

    if (loading) {
        return (
            <Box className="container" style={{ minHeight: '100vh', display: 'flex', flexDirection: 'column', paddingBottom: '60px', backgroundColor: '#f5f5f5' }}>
                <HeaderEdu />
                <HeaderSpacer />
                <Box style={{ flex: 1, display: 'flex', justifyContent: 'center', alignItems: 'center' }}>
                    <LoadingIndicator />
                </Box>
            </Box>
        );
    }

    return (
        <Box className="container" style={{ minHeight: '100vh', display: 'flex', flexDirection: 'column', paddingBottom: '60px', backgroundColor: '#f5f5f5' }}>
            <HeaderEdu
                title="Lịch coi thi"
                showBackButton={true}
                onBackClick={() => navigate(-1)}
            />
            <HeaderSpacer />

            <Box style={{ padding: '15px' }}>
                {/* Month Navigation */}
                <Box style={{ display: 'flex', justifyContent: 'space-between', alignItems: 'center', marginBottom: '20px', backgroundColor: 'white', padding: '15px', borderRadius: '10px' }}>
                    <Text
                        style={{ fontSize: '18px', cursor: 'pointer', color: '#0068ff' }}
                        onClick={() => {
                            if (currentMonth === 0) {
                                setCurrentMonth(11);
                                setCurrentYear(currentYear - 1);
                            } else {
                                setCurrentMonth(currentMonth - 1);
                            }
                        }}
                    >
                        ‹
                    </Text>
                    <Text bold size="large">
                        {monthNames[currentMonth]} {currentYear}
                    </Text>
                    <Text
                        style={{ fontSize: '18px', cursor: 'pointer', color: '#0068ff' }}
                        onClick={() => {
                            if (currentMonth === 11) {
                                setCurrentMonth(0);
                                setCurrentYear(currentYear + 1);
                            } else {
                                setCurrentMonth(currentMonth + 1);
                            }
                        }}
                    >
                        ›
                    </Text>
                </Box>

                {/* Tab filter */}
                <TabFilter
                    tabs={TEACHER_EXAM_SCHEDULE_TABS}
                    selectedTab={selectedTab}
                    onTabChange={setSelectedTab}
                />

                {/* Data Loading Indicator */}
                {dataLoading && (
                    <Box style={{ backgroundColor: 'white', borderRadius: '10px', padding: '20px', marginBottom: '15px', textAlign: 'center' }}>
                        <LoadingIndicator />
                        <Text style={{ marginTop: '10px', color: '#666' }}>Đang tải dữ liệu...</Text>
                    </Box>
                )}

                {/* Calendar */}
                {!dataLoading && (
                <Box style={{ backgroundColor: 'white', borderRadius: '10px', padding: '15px', marginBottom: '20px' }}>
                    {/* Week days header */}
                    <Box style={{ display: 'grid', gridTemplateColumns: 'repeat(7, 1fr)', gap: '5px', marginBottom: '10px' }}>
                        {weekDays.map((day, index) => (
                            <Text key={index} style={{ textAlign: 'center', fontSize: '12px', color: '#666', fontWeight: 'bold' }}>
                                {day}
                            </Text>
                        ))}
                    </Box>

                    {/* Calendar grid */}
                    {generateCalendar().map((week, weekIndex) => (
                        <Box key={weekIndex} style={{ display: 'grid', gridTemplateColumns: 'repeat(7, 1fr)', gap: '5px', marginBottom: '5px' }}>
                            {week.map((day, dayIndex) => (
                                <Box
                                    key={dayIndex}
                                    style={{
                                        minHeight: '40px',
                                        display: 'flex',
                                        flexDirection: 'column',
                                        alignItems: 'center',
                                        justifyContent: 'center',
                                        borderRadius: '5px',
                                        backgroundColor: day.isToday ? '#0068ff' : (day.hasSession ? '#e8f0fe' : 'transparent'),
                                        color: day.isToday ? 'white' : (day.isCurrentMonth ? 'black' : '#ccc'),
                                        border: day.hasSession && !day.isToday ? '1px solid #0068ff' : 'none',
                                        position: 'relative'
                                    }}
                                >
                                    <Text style={{ fontSize: '14px', fontWeight: day.isToday ? 'bold' : 'normal' }}>
                                        {day.date}
                                    </Text>
                                    {/* Indicators for exam sessions */}
                                    {day.hasSession && (
                                        <Box style={{ display: 'flex', gap: '3px', marginTop: '2px' }}>
                                            {/* Morning session indicator */}
                                            {day.morningSessions > 0 && (
                                                <Box style={{
                                                    width: '4px',
                                                    height: '4px',
                                                    borderRadius: '50%',
                                                    backgroundColor: day.isToday ? 'white' : '#4CAF50',
                                                    position: 'relative'
                                                }} />
                                            )}
                                            {/* Afternoon session indicator */}
                                            {day.afternoonSessions > 0 && (
                                                <Box style={{
                                                    width: '4px',
                                                    height: '4px',
                                                    borderRadius: '50%',
                                                    backgroundColor: day.isToday ? 'white' : '#FF9800',
                                                    position: 'relative'
                                                }} />
                                            )}
                                        </Box>
                                    )}
                                </Box>
                            ))}
                        </Box>
                    ))}
                </Box>
                )}

                {/* Weekly Schedule Details */}
                {!dataLoading && (
                <Box style={{ backgroundColor: 'white', borderRadius: '10px', padding: '15px' }}>
                    <Text bold size="large" style={{ marginBottom: '15px' }}>
                        Lịch coi thi trong tuần
                    </Text>

                    {['Monday', 'Tuesday', 'Wednesday', 'Thursday', 'Friday', 'Saturday', 'Sunday'].map((dayEn, index) => {
                        const dayVN = weekDays[index];
                        const daySessions = getSessionsForDay(dayEn);

                        return (
                            <Box key={dayEn} style={{ marginBottom: '15px', paddingBottom: '15px', borderBottom: index < 6 ? '1px solid #f0f0f0' : 'none' }}>
                                <Text bold style={{ marginBottom: '10px', color: '#0068ff' }}>
                                    {dayVN}
                                </Text>
                                {daySessions.length > 0 ? (
                                    daySessions.map((session, sessionIndex) => (
                                        <Box key={sessionIndex} style={{
                                            backgroundColor: '#f9f9f9',
                                            padding: '10px',
                                            borderRadius: '5px',
                                            marginBottom: '5px'
                                        }}>
                                            <Box style={{ display: 'flex', justifyContent: 'space-between', alignItems: 'center', marginBottom: '5px' }}>
                                                <Box>
                                                    <Text bold style={{ fontSize: '14px' }}>
                                                        {EXAM_TYPES[session.examType]}
                                                    </Text>
                                                    <Text style={{ fontSize: '12px', color: '#666' }}>
                                                        Ngày: {formatDate(session.date)}
                                                    </Text>
                                                    <Text style={{ fontSize: '12px', color: '#666' }}>
                                                        Năm học: {session.schoolYear}
                                                    </Text>
                                                    <Text style={{ fontSize: '12px', color: '#666' }}>
                                                        Môn: {session.subject}
                                                    </Text>
                                                    <Text style={{ fontSize: '12px', color: '#666' }}>
                                                        Phòng: {session.room}
                                                    </Text>
                                                </Box>
                                                <Box style={{ display: 'flex', flexDirection: 'column', alignItems: 'flex-end', gap: 4 }}>
                                                    <Text style={{ fontSize: '12px', color: '#0068ff', fontWeight: 'bold' }}>
                                                        {TIME_SLOTS[session.timeSlot]}
                                                    </Text>
                                                    {/* Status label */}
                                                    {session.status && SESSION_STATUS_DISPLAY[session.status] && (
                                                        <Box style={SESSION_STATUS_DISPLAY[session.status].style}>
                                                            {SESSION_STATUS_DISPLAY[session.status].label}
                                                        </Box>
                                                    )}
                                                </Box>
                                            </Box>
                                            {session.status !== SESSION_STATUS_CODE.DA_HOAN_THANH && (
                                                <Button
                                                    size="small"
                                                    onClick={() => handleChangeRequest(session, session.assignment)}
                                                    style={{ backgroundColor: '#ff9500', color: 'white' }}
                                                >
                                                    Đổi buổi thi
                                                </Button>
                                            )}
                                        </Box>
                                    ))
                                ) : (
                                    <Text style={{ fontSize: '14px', color: '#999', fontStyle: 'italic' }}>
                                        Không có lịch coi thi
                                    </Text>
                                )}
                            </Box>
                        );
                    })}
                </Box>
                )}
            </Box>

            {/* Change Request Modal */}
            <Modal
                visible={changeRequestModalVisible}
                title="Yêu cầu đổi buổi thi"
                onClose={() => {
                    setChangeRequestModalVisible(false);
                    setSelectedSession(null);
                    setSelectedUserData(null);
                }}
                actions={[
                    { text: 'Hủy', close: true, danger: true },
                    { text: submitting ? 'Đang gửi...' : 'Gửi yêu cầu', close: false, onClick: handleSubmitChangeRequest, disabled: submitting },
                ]}
            >
                <Box style={{ padding: '20px' }}>
                    {selectedSession && (
                        <>
                            <Box style={{ marginBottom: '20px', padding: '15px', backgroundColor: '#f9f9f9', borderRadius: '8px' }}>
                                <Text bold style={{ marginBottom: '10px' }}>Thông tin buổi thi hiện tại:</Text>
                                <Text>Ngày: {new Date(selectedSession.date).toLocaleDateString('vi-VN')}</Text>
                                <Text>Ca: {TIME_SLOTS[selectedSession.timeSlot]}</Text>
                                <Text>Phòng: {selectedSession.room}</Text>
                                <Text>Môn: {selectedSession.subject}</Text>
                            </Box>

                            <Box style={{ marginBottom: '15px' }}>
                                <Text style={{ marginBottom: '5px' }}>Lý do đổi buổi <Text style={{ color: 'red' }}>*</Text></Text>
                                <textarea
                                    value={changeRequestData.reason}
                                    onChange={(e) => setChangeRequestData(prev => ({ ...prev, reason: e.target.value }))}
                                    placeholder="Nhập lý do đổi buổi thi..."
                                    style={{
                                        width: '100%',
                                        minHeight: '80px',
                                        padding: '10px',
                                        border: '1px solid #e0e0e0',
                                        borderRadius: '8px',
                                        resize: 'vertical'
                                    }}
                                />
                            </Box>

                            <Box style={{ marginBottom: '15px' }}>
                                <Text style={{ marginBottom: '5px' }}>Ngày thi mới <Text style={{ color: 'red' }}>*</Text></Text>
                                <Input
                                    type="date"
                                    value={changeRequestData.proposedNewDate}
                                    onChange={(e) => setChangeRequestData(prev => ({ ...prev, proposedNewDate: e.target.value }))}
                                    placeholder="Chọn ngày thi mới"
                                />
                            </Box>

                            <Box style={{ marginBottom: '15px' }}>
                                <Text style={{ marginBottom: '5px' }}>Ca thi mới <Text style={{ color: 'red' }}>*</Text></Text>
                                <Select
                                    value={changeRequestData.proposedNewTimeSlot}
                                    onChange={(value) => setChangeRequestData(prev => ({ ...prev, proposedNewTimeSlot: value }))}
                                    placeholder="Chọn ca thi mới"
                                >
                                    {Object.entries(TIME_SLOTS).map(([key, label]) => (
                                        <Option key={key} value={key} title={label} />
                                    ))}
                                </Select>
                            </Box>

                            <Box style={{ marginBottom: '20px' }}>
                                <ZaloNotificationToggle
                                    checked={sendZaloNotification}
                                    onChange={setSendZaloNotification}
                                    hasZaloId={selectedUserData?.zaloId}
                                />
                            </Box>
                        </>
                    )}
                </Box>
            </Modal>

            <BottomNavigationEdu />
        </Box>
    );
};

export default TeacherExamSchedule; 
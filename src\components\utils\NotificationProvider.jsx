import React, { useState, useEffect } from 'react';
import ToastContainer from './Toast';
import UniversalModal from './UniversalModal';
import LoadingIndicator from './LoadingIndicator';
import notificationService from '../../utils/notificationService';
import { Box } from 'zmp-ui';

const NotificationProvider = ({ children }) => {
    const [toasts, setToasts] = useState([]);
    const [modal, setModal] = useState({ visible: false, options: {} });
    const [loading, setLoading] = useState({ show: false, message: '' });

    useEffect(() => {
        // Đăng ký callbacks với notification service
        notificationService.registerToastCallback(handleAddToast);
        notificationService.registerModalCallback(handleShowModal);
        notificationService.registerLoadingCallback(handleLoading);

        return () => {
            // Cleanup callbacks
            notificationService.registerToastCallback(null);
            notificationService.registerModalCallback(null);
            notificationService.registerLoadingCallback(null);
        };
    }, []);

    // Toast handlers
    const handleAddToast = (toastData) => {
        setToasts(prevToasts => {
            // Giới hạn số toast hiển thị (max 5)
            const newToasts = [toastData, ...prevToasts.slice(0, 4)];
            return newToasts;
        });
    };

    const handleRemoveToast = (timestamp) => {
        setToasts(prevToasts => 
            prevToasts.filter(toast => toast.timestamp !== timestamp)
        );
    };

    // Modal handlers
    const handleShowModal = (options) => {
        setModal({
            visible: true,
            options
        });
    };

    const handleCloseModal = () => {
        setModal({
            visible: false,
            options: {}
        });
    };

    // Loading handlers
    const handleLoading = (loadingData) => {
        setLoading(loadingData);
    };

    return (
        <>
            {children}
            
            {/* Toast Container */}
            <ToastContainer 
                toasts={toasts} 
                onRemoveToast={handleRemoveToast} 
            />
            
            {/* Universal Modal */}
            <UniversalModal
                visible={modal.visible}
                options={modal.options}
                onClose={handleCloseModal}
            />
            
            {/* Global Loading Overlay */}
            {loading.show && (
                <Box style={{
                    position: 'fixed',
                    top: 0,
                    left: 0,
                    right: 0,
                    bottom: 0,
                    backgroundColor: 'rgba(0, 0, 0, 0.5)',
                    display: 'flex',
                    alignItems: 'center',
                    justifyContent: 'center',
                    zIndex: 10000,
                    flexDirection: 'column',
                    gap: '15px'
                }}>
                    <LoadingIndicator />
                    {loading.message && (
                        <Box style={{
                            backgroundColor: 'white',
                            padding: '10px 20px',
                            borderRadius: '8px',
                            color: '#333',
                            fontSize: '14px'
                        }}>
                            {loading.message}
                        </Box>
                    )}
                </Box>
            )}
        </>
    );
};

export default NotificationProvider; 
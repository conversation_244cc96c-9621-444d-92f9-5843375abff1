import React, { useState, useEffect, useContext } from 'react';
import { Box, Text, Button, Select, Input, useNavigate } from 'zmp-ui';
import { AuthContext } from '../../context/AuthContext';
import { authApi } from '../../utils/api';
import HeaderEdu from '../../components/HeaderEdu';
import HeaderSpacer from '../../components/utils/HeaderSpacer';
import BottomNavigationEdu from '../../components/BottomNavigationEdu';
import LoadingIndicator from '../../components/utils/LoadingIndicator';
import useNotification from '../../hooks/useNotification';
import { TIME_SLOTS } from '../../constants/exam';

const { Option } = Select;

const ExamChangeRequestForm = () => {
    const navigate = useNavigate();
    const { user } = useContext(AuthContext);
    const notification = useNotification();
    const [loading, setLoading] = useState(false);
    const [supervision, setSupervision] = useState(null);
    const [formData, setFormData] = useState({
        reason: '',
        proposedNewDate: '',
        proposedNewTimeSlot: ''
    });

    // Get supervisionId and sessionId from URL
    const searchParams = new URLSearchParams(window.location.search);
    const supervisionId = searchParams.get('supervisionId');
    const sessionId = searchParams.get('sessionId');

    // Fetch supervision details
    const fetchSupervisionDetails = async () => {
        if (!supervisionId || !sessionId) {
            notification.showError('Lỗi', 'Thiếu thông tin phân công coi thi');
            navigate('/exam-supervisions');
            return;
        }

        setLoading(true);
        try {
            const response = await authApi.get(`/exam-supervisions/${supervisionId}`);
            if (response.data) {
                setSupervision(response.data);
                const session = response.data.sessions.find(s => s._id === sessionId);
                if (session) {
                    setFormData(prev => ({
                        ...prev,
                        proposedNewDate: new Date(session.date).toISOString().split('T')[0],
                        proposedNewTimeSlot: session.timeSlot
                    }));
                }
            }
        } catch (error) {
            console.error('Error fetching supervision details:', error);
            notification.showError('Lỗi', 'Không thể tải thông tin phân công coi thi');
            navigate('/exam-supervisions');
        } finally {
            setLoading(false);
        }
    };

    useEffect(() => {
        fetchSupervisionDetails();
    }, [supervisionId, sessionId]);

    const handleInputChange = (field, value) => {
        setFormData(prev => ({
            ...prev,
            [field]: value
        }));
    };

    const validateForm = () => {
        if (!formData.reason || formData.reason.length < 10) {
            notification.showError('Lỗi', 'Vui lòng nhập lý do đổi buổi (ít nhất 10 ký tự)');
            return false;
        }
        return true;
    };

    const handleSubmit = async () => {
        if (!validateForm()) return;

        setLoading(true);
        try {
            const payload = {
                supervisionId,
                sessionId,
                reason: formData.reason,
                proposedNewDate: formData.proposedNewDate || undefined,
                proposedNewTimeSlot: formData.proposedNewTimeSlot || undefined
            };

            await authApi.post('/exam-change-requests', payload);
            notification.showSuccess('Thành công', 'Đã gửi yêu cầu đổi buổi');
            navigate('/exam-change-requests');
        } catch (error) {
            console.error('Error creating change request:', error);
            notification.showError('Lỗi', 'Không thể gửi yêu cầu đổi buổi');
        } finally {
            setLoading(false);
        }
    };

    if (loading && !supervision) {
        return (
            <Box className="container" style={{ minHeight: '100vh', display: 'flex', flexDirection: 'column', paddingBottom: '60px', backgroundColor: '#f5f5f5' }}>
                <HeaderEdu />
                <HeaderSpacer />
                <Box style={{ flex: 1, display: 'flex', justifyContent: 'center', alignItems: 'center' }}>
                    <LoadingIndicator />
                </Box>
            </Box>
        );
    }

    if (!supervision) {
        return null;
    }

    const session = supervision.sessions.find(s => s._id === sessionId);

    return (
        <Box className="container" style={{ minHeight: '100vh', display: 'flex', flexDirection: 'column', paddingBottom: '60px', backgroundColor: '#f5f5f5' }}>
            <HeaderEdu />
            <HeaderSpacer />

            <Box className="form" style={{ padding: '15px', backgroundColor: 'white' }}>
                <Text bold size="large" style={{ marginBottom: '20px' }}>
                    Yêu cầu đổi buổi thi
                </Text>

                {/* Current Session Info */}
                <Box style={{ marginBottom: '20px', padding: '15px', backgroundColor: '#f5f5f5', borderRadius: '8px' }}>
                    <Text bold style={{ marginBottom: '10px' }}>Thông tin buổi thi hiện tại:</Text>
                    <Text style={{ marginBottom: '5px' }}>
                        Ngày: {new Date(session.date).toLocaleDateString('vi-VN')}
                    </Text>
                    <Text style={{ marginBottom: '5px' }}>
                        Ca thi: {TIME_SLOTS[session.timeSlot]}
                    </Text>
                    <Text style={{ marginBottom: '5px' }}>
                        Phòng: {session.room}
                    </Text>
                    <Text style={{ marginBottom: '5px' }}>
                        Môn: {session.subject}
                    </Text>
                </Box>

                {/* Form */}
                <Box style={{ marginBottom: '20px' }}>
                    <Text style={{ marginBottom: '5px' }}>Lý do đổi buổi *</Text>
                    <textarea
                        value={formData.reason}
                        onChange={(e) => handleInputChange('reason', e.target.value)}
                        placeholder="Nhập lý do đổi buổi (ít nhất 10 ký tự)"
                        style={{
                            width: '100%',
                            minHeight: '100px',
                            padding: '10px',
                            border: '1px solid #e0e0e0',
                            borderRadius: '8px',
                            resize: 'vertical',
                            marginBottom: '15px'
                        }}
                    />

                    <Text style={{ marginBottom: '5px' }}>Ngày đề xuất (tùy chọn)</Text>
                    <Input
                        type="date"
                        value={formData.proposedNewDate}
                        onChange={(e) => handleInputChange('proposedNewDate', e.target.value)}
                        style={{ marginBottom: '15px' }}
                    />

                    <Text style={{ marginBottom: '5px' }}>Ca thi đề xuất (tùy chọn)</Text>
                    <Select
                        value={formData.proposedNewTimeSlot}
                        onChange={(value) => handleInputChange('proposedNewTimeSlot', value)}
                        style={{ marginBottom: '15px' }}
                    >
                        {Object.entries(TIME_SLOTS).map(([key, label]) => (
                            <Option key={key} value={key} title={label} />
                        ))}
                    </Select>
                </Box>

                {/* Action Buttons */}
                <Box style={{ display: 'flex', gap: '10px', justifyContent: 'flex-end' }}>
                    <Button onClick={() => navigate('/exam-supervisions')}>Hủy</Button>
                    <Button primary onClick={handleSubmit} loading={loading}>
                        Gửi yêu cầu
                    </Button>
                </Box>
            </Box>

            <BottomNavigationEdu />
        </Box>
    );
};

export default ExamChangeRequestForm; 
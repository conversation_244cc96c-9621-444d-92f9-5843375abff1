import React from 'react';
import { Box, Text } from 'zmp-ui';

const TabFilter = ({ tabs, selectedTab, onTabChange, style = {} }) => {
    return (
        <Box style={{ 
            display: 'flex', 
            backgroundColor: 'white', 
            marginBottom: '15px', 
            borderRadius: '10px', 
            overflow: 'hidden',
            ...style 
        }}>
            {tabs.map((tab) => (
                <Box
                    key={tab.key}
                    onClick={() => onTabChange(tab.key)}
                    style={{
                        flex: 1,
                        padding: '12px 8px',
                        textAlign: 'center',
                        backgroundColor: selectedTab === tab.key ? tab.color : 'white',
                        color: selectedTab === tab.key ? 'white' : '#666',
                        cursor: 'pointer',
                        borderBottom: selectedTab === tab.key ? `3px solid ${tab.color}` : '3px solid transparent',
                        display: 'flex',
                        flexDirection: 'column',
                        alignItems: 'center',
                        gap: '4px'
                    }}
                >
                    <Text style={{ fontSize: '16px' }}>{tab.icon}</Text>
                    <Text bold={selectedTab === tab.key} style={{ fontSize: '13px', whiteSpace: 'nowrap' }}>
                        {tab.label}
                    </Text>
                </Box>
            ))}
        </Box>
    );
};

export default TabFilter; 
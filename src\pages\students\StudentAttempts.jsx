import React, { useState, useEffect, useContext, useCallback } from 'react';
import { Box, Text, Button, useNavigate, List } from 'zmp-ui';
import { useParams, useSearchParams } from 'react-router-dom';
import HeaderEdu from '../../components/HeaderEdu';
import HeaderSpacer from '../../components/utils/HeaderSpacer';
import BottomNavigationEdu from '../../components/BottomNavigationEdu';
import { AuthContext } from '../../context/AuthContext';
import { authApi } from '../../utils/api';
import LoadingIndicator from '../../components/utils/LoadingIndicator';
import { formatDistanceToNow, format } from 'date-fns';
import vi from 'date-fns/locale/vi';
import { ICONS } from '@/constants/icons';

const StudentAttempts = () => {
    const navigate = useNavigate();
    const { studentId } = useParams();
    const [searchParams] = useSearchParams();
    const examId = searchParams.get('examId');
    const { user, loading: authLoading } = useContext(AuthContext);

    // State
    const [attempts, setAttempts] = useState([]);
    const [studentInfo, setStudentInfo] = useState(null);
    const [examInfo, setExamInfo] = useState(null);
    const [loading, setLoading] = useState(true);

    // Fetch student attempts
    const fetchStudentAttempts = useCallback(async () => {
        if (!user || !studentId || !examId) return;
        
        setLoading(true);
        try {
            const response = await authApi.get(`/exams/students/${studentId}/exam-attempts?examId=${examId}`);
            
            if (response.data.success) {
                setAttempts(response.data.data || []);
                
                // Extract student and exam info from first attempt
                if (response.data.data && response.data.data.length > 0) {
                    const firstAttempt = response.data.data[0];
                    setExamInfo(firstAttempt.exam);
                    
                    // Get student info separately if not available
                    try {
                        const studentResponse = await authApi.get(`/students/${studentId}`);
                        if (studentResponse.data.success) {
                            setStudentInfo(studentResponse.data.data);
                        }
                    } catch (err) {
                        console.error('Error fetching student info:', err);
                    }
                }
            }
        } catch (error) {
            console.error('Error fetching student attempts:', error);
        } finally {
            setLoading(false);
        }
    }, [user, studentId, examId]);

    // Calculate duration
    const calculateDuration = (startTime, endTime) => {
        const start = new Date(startTime);
        const end = new Date(endTime);
        const diffMs = end - start;
        const diffMins = Math.floor(diffMs / 60000);
        const diffSecs = Math.floor((diffMs % 60000) / 1000);
        
        if (diffMins > 0) {
            return `${diffMins}p ${diffSecs}s`;
        }
        return `${diffSecs}s`;
    };

    // Get score color
    const getScoreColor = (score) => {
        if (score >= 8) return '#4CAF50';
        if (score >= 6.5) return '#8BC34A';
        if (score >= 5) return '#FF9800';
        if (score >= 3.5) return '#FF5722';
        return '#F44336';
    };

    // Get performance emoji
    const getPerformanceEmoji = (score) => {
        if (score >= 8) return ICONS.TROPHY;
        if (score >= 6.5) return ICONS.MEDAL;
        if (score >= 5) return ICONS.THUMBS_UP;
        if (score >= 3.5) return ICONS.SAD;
        return ICONS.SAD;
    };

    // Get attempt status
    const getAttemptStatus = (attempt) => {
        const accuracy = (attempt.totalCorrect / attempt.totalQuestions) * 100;
        if (accuracy >= 80) return { label: 'Xuất sắc', color: '#4CAF50' };
        if (accuracy >= 60) return { label: 'Tốt', color: '#8BC34A' };
        if (accuracy >= 40) return { label: 'Trung bình', color: '#FF9800' };
        return { label: 'Cần cải thiện', color: '#F44336' };
    };

    // Calculate statistics
    const statistics = React.useMemo(() => {
        if (!attempts || attempts.length === 0) {
            return {
                totalAttempts: 0,
                bestScore: 0,
                averageScore: 0,
                totalTimeSpent: 0,
                averageTime: 0,
                accuracy: 0,
                improvement: 0
            };
        }

        const scores = attempts.map(attempt => attempt.score || 0);
        const bestScore = Math.max(...scores);
        const averageScore = scores.reduce((sum, score) => sum + score, 0) / scores.length;
        
        // Calculate total time spent
        const totalTimeSpent = attempts.reduce((total, attempt) => {
            const start = new Date(attempt.startTime);
            const end = new Date(attempt.endTime);
            return total + (end - start);
        }, 0);
        
        const averageTime = totalTimeSpent / attempts.length;
        
        // Calculate overall accuracy
        const totalCorrect = attempts.reduce((sum, attempt) => sum + (attempt.totalCorrect || 0), 0);
        const totalQuestions = attempts.reduce((sum, attempt) => sum + (attempt.totalQuestions || 0), 0);
        const accuracy = totalQuestions > 0 ? (totalCorrect / totalQuestions) * 100 : 0;
        
        // Calculate improvement (compare last vs first)
        const improvement = attempts.length > 1 ? 
            ((scores[0] - scores[scores.length - 1]) / scores[scores.length - 1]) * 100 : 0;

        return {
            totalAttempts: attempts.length,
            bestScore: Number(bestScore.toFixed(1)),
            averageScore: Number(averageScore.toFixed(1)),
            totalTimeSpent: Math.floor(totalTimeSpent / 60000), // in minutes
            averageTime: Math.floor(averageTime / 60000), // in minutes
            accuracy: Number(accuracy.toFixed(1)),
            improvement: Number(improvement.toFixed(1))
        };
    }, [attempts]);

    // Check authentication
    useEffect(() => {
        if (!authLoading && !user) {
            navigate('/login', { replace: true });
        } else if (user && !user.role.includes('teacher') && !user.role.includes('TEACHER')) {
            navigate('/login', { replace: true });
        }
    }, [user, authLoading, navigate]);

    // Fetch data on mount
    useEffect(() => {
        fetchStudentAttempts();
    }, [fetchStudentAttempts]);

    return (
        <Box className="container" style={{ minHeight: '100vh', display: 'flex', flexDirection: 'column', paddingBottom: '60px', backgroundColor: '#f5f5f5' }}>
            <HeaderEdu
                title="Lịch sử làm bài"
                subtitle={
                    studentInfo && examInfo 
                        ? `${studentInfo.name} (${studentInfo.studentId}) - ${examInfo.title} (${examInfo.subject?.name})`
                        : "Đang tải thông tin..."
                }
                showBackButton
                onBackClick={() => navigate(-1)}
            />
            <HeaderSpacer />
            
            {/* Statistics Overview */}
            <Box style={{ padding: '15px', backgroundColor: 'white', marginBottom: '10px' }}>
                <Text bold style={{ marginBottom: '15px' }}>Thống kê tổng quan</Text>
                
                <Box style={{ display: 'grid', gridTemplateColumns: 'repeat(2, 1fr)', gap: '12px', marginBottom: '15px' }}>
                    <Box style={{ backgroundColor: '#f0f8ff', borderRadius: '8px', padding: '10px', textAlign: 'center' }}>
                        <Text bold style={{ fontSize: '18px', color: '#0068ff' }}>
                            {statistics.totalAttempts}
                        </Text>
                        <Text style={{ fontSize: '11px', color: '#666' }}>Lượt thi</Text>
                    </Box>
                    
                    <Box style={{ backgroundColor: '#f8fff8', borderRadius: '8px', padding: '10px', textAlign: 'center' }}>
                        <Text bold style={{ fontSize: '18px', color: '#4CAF50' }}>
                            {statistics.bestScore}
                        </Text>
                        <Text style={{ fontSize: '11px', color: '#666' }}>Điểm cao nhất</Text>
                    </Box>
                    
                    <Box style={{ backgroundColor: '#fff8f0', borderRadius: '8px', padding: '10px', textAlign: 'center' }}>
                        <Text bold style={{ fontSize: '18px', color: '#FF9800' }}>
                            {statistics.averageScore}
                        </Text>
                        <Text style={{ fontSize: '11px', color: '#666' }}>Điểm trung bình</Text>
                    </Box>
                    
                    <Box style={{ backgroundColor: '#f5f5f5', borderRadius: '8px', padding: '10px', textAlign: 'center' }}>
                        <Text bold style={{ fontSize: '18px', color: '#666' }}>
                            {statistics.accuracy}%
                        </Text>
                        <Text style={{ fontSize: '11px', color: '#666' }}>Độ chính xác</Text>
                    </Box>
                </Box>

                <Box style={{ backgroundColor: '#f9f9f9', borderRadius: '8px', padding: '10px' }}>
                    <Box flex justifyContent="space-between">
                        <Box style={{ textAlign: 'center' }}>
                            <Text bold style={{ fontSize: '14px', color: '#666' }}>
                                {statistics.totalTimeSpent}p
                            </Text>
                            <Text style={{ fontSize: '10px', color: '#666' }}>Tổng thời gian</Text>
                        </Box>
                        <Box style={{ textAlign: 'center' }}>
                            <Text bold style={{ fontSize: '14px', color: '#666' }}>
                                {statistics.averageTime}p
                            </Text>
                            <Text style={{ fontSize: '10px', color: '#666' }}>TB/lần thi</Text>
                        </Box>
                        <Box style={{ textAlign: 'center' }}>
                            <Text bold style={{ 
                                fontSize: '14px', 
                                color: statistics.improvement >= 0 ? '#4CAF50' : '#F44336' 
                            }}>
                                {statistics.improvement > 0 ? '+' : ''}{statistics.improvement}%
                            </Text>
                            <Text style={{ fontSize: '10px', color: '#666' }}>Tiến bộ</Text>
                        </Box>
                    </Box>
                </Box>
            </Box>

            {/* Attempts List */}
            <Box style={{ flex: 1, backgroundColor: 'white', padding: '15px' }}>
                <Box flex justifyContent="space-between" alignItems="center" style={{ marginBottom: '15px' }}>
                    <Text bold size="large">Lịch sử các lần thi ({attempts.length})</Text>
                    <Button
                        onClick={fetchStudentAttempts}
                        style={{ backgroundColor: 'transparent', color: '#0068ff', fontSize: '20px' }}
                    >
                        {ICONS.REFRESH}
                    </Button>
                </Box>

                {loading ? (
                    <Box style={{ display: 'flex', justifyContent: 'center', padding: '40px' }}>
                        <LoadingIndicator />
                    </Box>
                ) : attempts.length > 0 ? (
                    <List style={{ display: 'flex', flexDirection: 'column', gap: '12px' }}>
                        {attempts.map((attempt, index) => {
                            const status = getAttemptStatus(attempt);
                            const duration = calculateDuration(attempt.startTime, attempt.endTime);
                            const accuracy = (attempt.totalCorrect / attempt.totalQuestions) * 100;
                            
                            return (
                                <Box 
                                    key={attempt._id}
                                    style={{
                                        backgroundColor: '#f9f9f9',
                                        borderRadius: '12px',
                                        padding: '15px',
                                        boxShadow: '0 2px 8px rgba(0,0,0,0.1)',
                                        position: 'relative',
                                        borderLeft: `4px solid ${getScoreColor(attempt.score)}`
                                    }}
                                >
                                    {/* Attempt Number & Status */}
                                    <Box flex justifyContent="space-between" alignItems="flex-start" style={{ marginBottom: '10px' }}>
                                        <Box flex alignItems="center">
                                            <Box style={{
                                                backgroundColor: '#e8f0fe',
                                                color: '#0068ff',
                                                borderRadius: '50%',
                                                width: '32px',
                                                height: '32px',
                                                display: 'flex',
                                                alignItems: 'center',
                                                justifyContent: 'center',
                                                fontSize: '14px',
                                                fontWeight: 'bold',
                                                marginRight: '10px'
                                            }}>
                                                {attempts.length - index}
                                            </Box>
                                            <Box>
                                                <Text bold style={{ fontSize: '14px' }}>
                                                    Lần {attempts.length - index}
                                                </Text>
                                                <Text style={{ fontSize: '11px', color: '#666' }}>
                                                    {format(new Date(attempt.startTime), 'dd/MM/yyyy HH:mm', { locale: vi })}
                                                </Text>
                                            </Box>
                                        </Box>
                                        
                                        <Box style={{
                                            backgroundColor: status.color,
                                            color: 'white',
                                            padding: '2px 6px',
                                            borderRadius: '8px',
                                            fontSize: '10px',
                                            fontWeight: 'bold'
                                        }}>
                                            {status.label}
                                        </Box>
                                    </Box>

                                    {/* Score & Performance */}
                                    <Box style={{ display: 'grid', gridTemplateColumns: 'repeat(3, 1fr)', gap: '10px', marginBottom: '10px' }}>
                                        <Box style={{ textAlign: 'center' }}>
                                            <Box flex alignItems="center" justifyContent="center" style={{ marginBottom: '2px' }}>
                                                <Text style={{ fontSize: '16px', marginRight: '4px' }}>
                                                    {getPerformanceEmoji(attempt.score)}
                                                </Text>
                                                <Text bold style={{ 
                                                    fontSize: '16px', 
                                                    color: getScoreColor(attempt.score) 
                                                }}>
                                                    {attempt.score?.toFixed(1)}
                                                </Text>
                                            </Box>
                                            <Text style={{ fontSize: '10px', color: '#666' }}>Điểm</Text>
                                        </Box>
                                        
                                        <Box style={{ textAlign: 'center' }}>
                                            <Text bold style={{ fontSize: '16px', color: '#0068ff', marginBottom: '2px' }}>
                                                {attempt.totalCorrect}/{attempt.totalQuestions}
                                            </Text>
                                            <Text style={{ fontSize: '10px', color: '#666' }}>Đúng/Tổng</Text>
                                        </Box>
                                        
                                        <Box style={{ textAlign: 'center' }}>
                                            <Text bold style={{ fontSize: '16px', color: '#FF9800', marginBottom: '2px' }}>
                                                {accuracy.toFixed(0)}%
                                            </Text>
                                            <Text style={{ fontSize: '10px', color: '#666' }}>Độ chính xác</Text>
                                        </Box>
                                    </Box>

                                    {/* Time Info */}
                                    <Box style={{ 
                                        backgroundColor: '#f0f8ff', 
                                        borderRadius: '6px', 
                                        padding: '8px',
                                        marginBottom: '8px'
                                    }}>
                                        <Box flex justifyContent="space-between" alignItems="center">
                                            <Text style={{ fontSize: '12px', color: '#666' }}>
                                                {ICONS.TIMER} Thời gian làm bài: <Text bold>{duration}</Text>
                                            </Text>
                                            <Text style={{ fontSize: '11px', color: '#888' }}>
                                                {formatDistanceToNow(new Date(attempt.endTime), { 
                                                    locale: vi, 
                                                    addSuffix: true 
                                                })}
                                            </Text>
                                        </Box>
                                    </Box>

                                    {/* Progress Bar */}
                                    <Box style={{ marginBottom: '5px' }}>
                                        <Box style={{ 
                                            height: '4px', 
                                            backgroundColor: '#e0e0e0', 
                                            borderRadius: '2px',
                                            overflow: 'hidden'
                                        }}>
                                            <Box style={{
                                                width: `${accuracy}%`,
                                                height: '100%',
                                                backgroundColor: getScoreColor(attempt.score),
                                                transition: 'width 0.3s ease'
                                            }} />
                                        </Box>
                                        <Text style={{ fontSize: '10px', color: '#666', textAlign: 'right', marginTop: '2px' }}>
                                            {accuracy.toFixed(1)}% chính xác
                                        </Text>
                                    </Box>
                                </Box>
                            );
                        })}
                    </List>
                ) : (
                    <Box style={{ textAlign: 'center', padding: '40px', color: '#666' }}>
                        <Text style={{ fontSize: '48px', marginBottom: '10px' }}>{ICONS.DRAFT}</Text>
                        <Text bold size="large" style={{ marginBottom: '5px' }}>Chưa có lần thi nào</Text>
                        <Text style={{ fontSize: '14px' }}>Học sinh chưa làm bài thi này</Text>
                    </Box>
                )}
            </Box>

            <BottomNavigationEdu />
        </Box>
    );
};

export default StudentAttempts; 
import React, { useState, useEffect, useContext } from 'react';
import { Box, Text, Button, Select, useNavigate, Modal } from 'zmp-ui';
import { AuthContext } from '../../context/AuthContext';
import { authApi } from '../../utils/api';
import HeaderEdu from '../../components/HeaderEdu';
import HeaderSpacer from '../../components/utils/HeaderSpacer';
import BottomNavigationEdu from '../../components/BottomNavigationEdu';
import LoadingIndicator from '../../components/utils/LoadingIndicator';
import ZaloNotificationToggle from '../../components/utils/ZaloNotificationToggle';
import TabFilter from '../../components/utils/TabFilter';
import useNotification from '../../hooks/useNotification';
import { EXAM_TYPES, TIME_SLOTS, EXAM_CHANGE_REQUEST_TABS, EXAM_CHANGE_REQUEST_STATUS_LABELS, EXAM_CHANGE_REQUEST_STATUS_COLORS } from '../../constants/exam';
import DateInput from '../../components/utils/DateInput';
import DateRangeFilter from '../../components/utils/DateRangeFilter';
import { formatExamChangeRequestMessage } from '../../components/utils/ZaloMessageFormatter';
import { useSchoolYear } from '../../context/SchoolYearContext';
import useAnnouncement from '../../hooks/useAnnouncement';
import { formatDate } from '../../utils/dateUtils';
import { SESSION_STATUS_DISPLAY } from '@/constants/sessionStatus';
import { ICONS } from '@/constants/icons';

const { Option } = Select;

const ExamChangeRequests = () => {
    const navigate = useNavigate();
    const { user } = useContext(AuthContext);
    const notification = useNotification();
    const { sendCustomNotification } = useAnnouncement();
    const [loading, setLoading] = useState(true);
    const [requestsLoading, setRequestsLoading] = useState(false);
    const [requests, setRequests] = useState([]);
    const [selectedTab, setSelectedTab] = useState('PENDING');
    const { selectedSchoolYear } = useSchoolYear();

    // Filter states
    const [dateFilter, setDateFilter] = useState({
        startDate: '',
        endDate: ''
    });
    const [showFilters, setShowFilters] = useState(false);
    const [filters, setFilters] = useState({
        schoolYear: '',
        examType: '',
        page: 1,
        limit: 10
    });

    // Pagination info
    const [pagination, setPagination] = useState({
        current: 1,
        pages: 1,
        total: 0
    });

    // Approval modal states
    const [approvalModalVisible, setApprovalModalVisible] = useState(false);
    const [selectedRequest, setSelectedRequest] = useState(null);
    const [approvalData, setApprovalData] = useState({
        status: 'APPROVED',
        approvalNotes: '',
        newDate: '',
        newTimeSlot: ''
    });
    const [sendZaloNotification, setSendZaloNotification] = useState(false);
    const [selectedTeacherData, setSelectedTeacherData] = useState(null);
    const [submitting, setSubmitting] = useState(false);





    // Fetch change requests
    const fetchChangeRequests = async (showLoadingOverlay = false) => {
        if (showLoadingOverlay) {
            setLoading(true);
        } else {
            setRequestsLoading(true);
        }

        try {
            const queryParams = new URLSearchParams();
            queryParams.append('status', selectedTab);
            queryParams.append('page', filters.page.toString());
            queryParams.append('limit', filters.limit.toString());

            if (filters.schoolYear) {
                queryParams.append('schoolYear', filters.schoolYear);
            }
            if (filters.examType) {
                queryParams.append('examType', filters.examType);
            }
            if (dateFilter.startDate) {
                queryParams.append('startDate', dateFilter.startDate);
            }
            if (dateFilter.endDate) {
                queryParams.append('endDate', dateFilter.endDate);
            }

            const response = await authApi.get(`/exam-change-requests?${queryParams.toString()}`);
            if (response.data) {
                // Fix: Use changeRequests instead of requests
                setRequests(response.data.changeRequests || []);
                setPagination(response.data.pagination || {
                    current: 1,
                    pages: 1,
                    total: 0
                });
            }
        } catch (error) {
            console.error('Error fetching change requests:', error);
            notification.showError('Lỗi', 'Không thể tải danh sách yêu cầu đổi buổi');
        } finally {
            setLoading(false);
            setRequestsLoading(false);
        }
    };

    // Initial load
    useEffect(() => {
        fetchChangeRequests(true);
    }, []);

    // Filter changes - show overlay loading
    useEffect(() => {
        fetchChangeRequests(true);
    }, [filters]);

    // Tab changes - only refresh requests, not the whole page
    useEffect(() => {
        fetchChangeRequests(false);
    }, [selectedTab]);

    // Fetch teacher data when modal opens
    const fetchTeacherData = async (teacherId) => {
        if (teacherId) {
            try {
                const response = await authApi.get(`/directory/user/${teacherId}`);
                setSelectedTeacherData(response.data.data);
            } catch (error) {
                console.error('Error fetching teacher data:', error);
                setSelectedTeacherData(null);
            }
        } else {
            setSelectedTeacherData(null);
        }
    };

    // Handle date filter change
    const handleDateFilterChange = (field, value) => {
        setDateFilter(prev => ({
            ...prev,
            [field]: value
        }));
    };

    // Clear filters
    const clearFilters = () => {
        setDateFilter({
            startDate: '',
            endDate: ''
        });
        setFilters(prev => ({
            ...prev,
            schoolYear: '',
            examType: ''
        }));
    };

    const handleFilterChange = (field, value) => {
        setFilters(prev => ({
            ...prev,
            [field]: value,
            page: 1 // Reset to first page when filter changes
        }));
    };

    const handlePageChange = (newPage) => {
        setFilters(prev => ({
            ...prev,
            page: newPage
        }));
    };

    const handleApproval = (request) => {
        setSelectedRequest(request);
        setApprovalData({
            status: 'APPROVED',
            approvalNotes: '',
            newDate: request.proposedNewDate.split('T')[0], // Convert ISO date to YYYY-MM-DD format
            newTimeSlot: request.proposedNewTimeSlot
        });
        setSendZaloNotification(false);
        setSelectedTeacherData(null);
        setApprovalModalVisible(true);

        // Fetch teacher data
        if (request.teacher?._id) {
            fetchTeacherData(request.teacher._id);
        }
    };

    const handleRejection = (request) => {
        setSelectedRequest(request);
        setApprovalData({
            status: 'REJECTED',
            approvalNotes: '',
            newDate: '',
            newTimeSlot: ''
        });
        setSendZaloNotification(false);
        setSelectedTeacherData(null);
        setApprovalModalVisible(true);

        // Fetch teacher data
        if (request.teacher?._id) {
            fetchTeacherData(request.teacher._id);
        }
    };

    const handleSubmitApproval = async () => {
        if (!approvalData.approvalNotes) {
            notification.showError('Lỗi', 'Vui lòng nhập ghi chú');
            return;
        }

        setSubmitting(true);
        try {
            const payload = {
                status: approvalData.status,
                approvalNotes: approvalData.approvalNotes
            };

            if (approvalData.status === 'APPROVED') {
                payload.newDate = approvalData.newDate;
                payload.newTimeSlot = approvalData.newTimeSlot;
            }

            const response = await authApi.put(`/exam-change-requests/${selectedRequest._id}/approve`, payload);
            let message = '';

            if (response.data) {
                const approvedRequest = response.data;
                message = formatExamChangeRequestMessage({
                    teacher: approvedRequest.teacher,
                    examType: approvedRequest.supervision.examType,
                    schoolYear: approvedRequest.supervision.schoolYear,
                    originalDate: approvedRequest.originalDate,
                    originalTimeSlot: approvedRequest.originalTimeSlot,
                    proposedNewDate: approvedRequest.proposedNewDate,
                    proposedNewTimeSlot: approvedRequest.proposedNewTimeSlot,
                    reason: approvedRequest.reason,
                    status: approvedRequest.status,
                    approvalNotes: approvedRequest.approvalNotes
                });
            }

            // Send announcement notification (always)
            if (response.data) {
                try {
                    await sendCustomNotification({
                        title: 'Yêu cầu đổi buổi coi thi',
                        content: message,
                        users: [approvedRequest.teacher]
                    });
                } catch (announcementError) {
                    console.error('Error sending announcement notification:', announcementError);
                    // Don't show error to user for announcement failure
                }
            }

            // Send Zalo notification if enabled
            if (sendZaloNotification && selectedTeacherData?.zaloId && response.data) {
                try {
                    await authApi.post('/zalo/send-user-message', {
                        userId: selectedTeacherData.zaloId,
                        message: message
                    });
                } catch (zaloError) {
                    console.error('Error sending Zalo notification:', zaloError);
                    notification.showError('Lỗi', 'Không thể gửi thông báo qua Zalo');
                }
            }

            notification.showSuccess('Thành công', `Đã ${approvalData.status === 'APPROVED' ? 'duyệt' : 'từ chối'} yêu cầu`);
            setApprovalModalVisible(false);
            setSelectedRequest(null);
            setSelectedTeacherData(null);
            fetchChangeRequests();
        } catch (error) {
            console.error('Error approving request:', error);
            notification.showError('Lỗi', 'Không thể xử lý yêu cầu');
        } finally {
            setSubmitting(false);
        }
    };

    if (loading) {
        return (
            <Box className="container" style={{ minHeight: '100vh', display: 'flex', flexDirection: 'column', paddingBottom: '60px', backgroundColor: '#f5f5f5' }}>
                <HeaderEdu title="Duyệt yêu cầu đổi buổi" showBackButton={true} onBackClick={() => navigate(-1)} />
                <HeaderSpacer />
                <Box style={{ flex: 1, display: 'flex', justifyContent: 'center', alignItems: 'center' }}>
                    <LoadingIndicator />
                </Box>
            </Box>
        );
    }

    return (
        <Box className="container" style={{ minHeight: '100vh', display: 'flex', flexDirection: 'column', paddingBottom: '60px', backgroundColor: '#f5f5f5' }}>
            <HeaderEdu title="Duyệt yêu cầu đổi buổi" showBackButton={true} onBackClick={() => navigate(-1)} />
            <HeaderSpacer />

            <Box style={{ padding: '15px', flex: 1 }}>
                {/* Filters */}
                <Box style={{ backgroundColor: 'white', borderRadius: '10px', padding: '15px', marginBottom: '15px' }}>
                    <Box style={{ display: 'flex', justifyContent: 'space-between', alignItems: 'center', marginBottom: '15px' }}>
                        <Text bold size="large">Bộ lọc</Text>
                        <Button
                            size="small"
                            onClick={() => setShowFilters(!showFilters)}
                            style={{ backgroundColor: '#f0f8ff', color: '#0068ff' }}
                        >
                            {showFilters ? 'Ẩn' : 'Hiện'} bộ lọc
                        </Button>
                    </Box>

                    {showFilters && (
                        <>
                            <DateRangeFilter
                                startDate={dateFilter.startDate}
                                endDate={dateFilter.endDate}
                                onStartDateChange={(value) => handleDateFilterChange('startDate', value)}
                                onEndDateChange={(value) => handleDateFilterChange('endDate', value)}
                                onApply={() => fetchChangeRequests(false)}
                                onClear={clearFilters}
                                containerStyle={{ marginBottom: '15px' }}
                            />
                            <Box style={{ display: 'grid', gridTemplateColumns: 'repeat(2, 1fr)', gap: '12px', marginBottom: '15px' }}>
                                <Select
                                    placeholder="Năm học"
                                    value={filters.schoolYear}
                                    onChange={(value) => handleFilterChange('schoolYear', value)}
                                >
                                    <Option value="2023-2024" title="2023-2024" />
                                    <Option value="2024-2025" title="2024-2025" />
                                </Select>
                                <Select
                                    placeholder="Loại kỳ thi"
                                    value={filters.examType}
                                    onChange={(value) => handleFilterChange('examType', value)}
                                >
                                    {Object.entries(EXAM_TYPES).map(([key, label]) => (
                                        <Option key={key} value={key} title={label} />
                                    ))}
                                </Select>
                            </Box>
                        </>
                    )}
                </Box>

                {/* Tab filter */}
                <TabFilter
                    tabs={EXAM_CHANGE_REQUEST_TABS}
                    selectedTab={selectedTab}
                    onTabChange={setSelectedTab}
                />

                {/* Data Loading Indicator */}
                {requestsLoading && !loading && (
                    <Box style={{ backgroundColor: 'white', borderRadius: '10px', padding: '20px', marginBottom: '15px', textAlign: 'center' }}>
                        <LoadingIndicator />
                        <Text style={{ marginTop: '10px', color: '#666' }}>Đang tải dữ liệu...</Text>
                    </Box>
                )}

                {/* Stats Summary */}
                {!loading && requests.length > 0 && (
                    <Box style={{ backgroundColor: 'white', borderRadius: '10px', padding: '15px', marginBottom: '15px' }}>
                        <Text style={{ fontSize: '14px', color: '#666' }}>
                            Hiển thị {requests.length} trong tổng số {pagination.total} yêu cầu
                            {pagination.pages > 1 && ` • Trang ${pagination.current}/${pagination.pages}`}
                        </Text>
                    </Box>
                )}

                {/* List */}
                {requests.length === 0 && !requestsLoading && !loading ? (
                    <Box style={{ backgroundColor: 'white', borderRadius: '10px', padding: '40px', textAlign: 'center' }}>
                        <Text style={{ fontSize: '48px', marginBottom: '10px' }}>{ICONS.DRAFT}</Text>
                        <Text bold size="large" style={{ marginBottom: '8px', color: '#666' }}>
                            Chưa có yêu cầu đổi buổi
                        </Text>
                        <Text style={{ color: '#999' }}>
                            {selectedTab === 'PENDING' ? 'Chưa có yêu cầu đổi buổi nào cần duyệt' :
                                selectedTab === 'APPROVED' ? 'Chưa có yêu cầu đổi buổi nào được duyệt' :
                                    'Chưa có yêu cầu đổi buổi nào bị từ chối'}
                        </Text>
                    </Box>
                ) : !requestsLoading && !loading && (
                    <Box style={{ display: 'flex', flexDirection: 'column', gap: '12px' }}>
                        {requests.map((request) => (
                            <Box
                                key={request._id}
                                style={{
                                    backgroundColor: 'white',
                                    borderRadius: '10px',
                                    padding: '15px',
                                    border: `1px solid ${EXAM_CHANGE_REQUEST_STATUS_COLORS[request.status]}20`,
                                    boxShadow: '0 2px 8px rgba(0,0,0,0.1)'
                                }}
                            >
                                <Box style={{ display: 'flex', justifyContent: 'space-between', alignItems: 'flex-start', marginBottom: '10px' }}>
                                    <Box style={{ flex: 1 }}>
                                        <Text bold size="large" style={{ marginBottom: '5px' }}>
                                            {ICONS.TEACHER} {request.teacher?.name || 'Giáo viên'}
                                        </Text>
                                        <Text style={{ fontSize: '12px', color: '#666', marginBottom: '8px' }}>
                                            <Text bold>{EXAM_TYPES[request.supervision.examType]} - {request.supervision.schoolYear}</Text>
                                        </Text>
                                    </Box>
                                    <Box
                                        style={{
                                            backgroundColor: `${EXAM_CHANGE_REQUEST_STATUS_COLORS[request.status]}20`,
                                            color: EXAM_CHANGE_REQUEST_STATUS_COLORS[request.status],
                                            padding: '4px 8px',
                                            borderRadius: '12px',
                                            fontSize: '12px',
                                            fontWeight: 'bold'
                                        }}
                                    >
                                        {EXAM_CHANGE_REQUEST_STATUS_LABELS[request.status]}
                                    </Box>
                                </Box>

                                <Box style={{ marginBottom: '10px' }}>
                                    <Text style={{ fontSize: '14px', marginBottom: '5px' }}>
                                        <Text bold>{ICONS.CALENDAR} Buổi thi hiện tại:</Text>
                                    </Text>
                                    <Text style={{ fontSize: '12px', color: '#666' }}>
                                        {formatDate(request.originalDate)} - {TIME_SLOTS[request.originalTimeSlot]}
                                    </Text>
                                </Box>

                                <Box style={{ marginBottom: '10px' }}>
                                    <Text style={{ fontSize: '14px', marginBottom: '5px' }}>
                                        <Text bold>{SESSION_STATUS_DISPLAY.YEU_CAU_DOI_BUOI.icon} Yêu cầu đổi thành:</Text>
                                    </Text>
                                    <Text style={{ fontSize: '12px', color: '#666' }}>
                                        {new Date(request.proposedNewDate).toLocaleDateString('vi-VN')} - {TIME_SLOTS[request.proposedNewTimeSlot]}
                                    </Text>
                                </Box>

                                <Text style={{ fontSize: '14px', color: '#333', lineHeight: 1.4 }}>
                                    <Text bold>{ICONS.DRAFT} Lý do:</Text> {request.reason}
                                </Text>

                                {request.status === 'PENDING' && (
                                    <Box style={{ display: 'flex', gap: '10px', marginTop: '15px' }}>
                                        <Button
                                            size="small"
                                            onClick={() => handleApproval(request)}
                                            style={{ backgroundColor: '#34c759', color: 'white' }}
                                        >
                                            Duyệt
                                        </Button>
                                        <Button
                                            size="small"
                                            danger
                                            onClick={() => handleRejection(request)}
                                        >
                                            Từ chối
                                        </Button>
                                    </Box>
                                )}

                                {request.status !== 'PENDING' && request.approvalNotes && (
                                    <Box style={{ marginTop: '10px', padding: '8px', backgroundColor: '#f9f9f9', borderRadius: '6px' }}>
                                        <Text style={{ fontSize: '12px', color: '#666' }}>
                                            <Text bold>{ICONS.MESSAGE} Ghi chú duyệt:</Text> {request.approvalNotes}
                                        </Text>
                                    </Box>
                                )}
                            </Box>
                        ))}

                        {/* Pagination */}
                        {pagination.pages > 1 && (
                            <Box style={{ backgroundColor: 'white', borderRadius: '10px', padding: '15px', marginTop: '15px' }}>
                                <Box style={{ display: 'flex', justifyContent: 'space-between', alignItems: 'center' }}>
                                    <Button
                                        size="small"
                                        disabled={pagination.current === 1}
                                        onClick={() => handlePageChange(pagination.current - 1)}
                                        style={{
                                            backgroundColor: pagination.current > 1 ? '#0068ff' : '#f5f5f5',
                                            color: pagination.current > 1 ? 'white' : '#999'
                                        }}
                                    >
                                        ← Trước
                                    </Button>

                                    <Text style={{ fontSize: '14px', color: '#666' }}>
                                        Trang {pagination.current} / {pagination.pages}
                                    </Text>

                                    <Button
                                        size="small"
                                        disabled={pagination.current === pagination.pages}
                                        onClick={() => handlePageChange(pagination.current + 1)}
                                        style={{
                                            backgroundColor: pagination.current < pagination.pages ? '#0068ff' : '#f5f5f5',
                                            color: pagination.current < pagination.pages ? 'white' : '#999'
                                        }}
                                    >
                                        Tiếp →
                                    </Button>
                                </Box>
                            </Box>
                        )}
                    </Box>
                )}
            </Box>

            {/* Approval Modal */}
            <Modal
                visible={approvalModalVisible}
                title={`${approvalData.status === 'APPROVED' ? 'Duyệt' : 'Từ chối'} yêu cầu đổi buổi`}
                onClose={() => {
                    setApprovalModalVisible(false);
                    setSelectedRequest(null);
                    setSelectedTeacherData(null);
                }}
                actions={[
                    { text: 'Hủy', close: true, danger: true },
                    { text: submitting ? 'Đang xử lý...' : (approvalData.status === 'APPROVED' ? 'Duyệt' : 'Từ chối'), close: false, onClick: handleSubmitApproval, disabled: submitting },
                ]}
            >
                <Box style={{ padding: '20px' }}>
                    {selectedRequest && (
                        <>
                            <Box style={{ marginBottom: '20px', padding: '15px', backgroundColor: '#f9f9f9', borderRadius: '8px' }}>
                                <Text bold style={{ marginBottom: '10px' }}>Thông tin yêu cầu:</Text>
                                <Text>Giáo viên: {selectedRequest.teacher?.name}</Text>
                                <Text>Lý do: {selectedRequest.reason}</Text>
                            </Box>

                            {approvalData.status === 'APPROVED' && (
                                <>
                                    <Box style={{ marginBottom: '15px' }}>
                                        <Text style={{ marginBottom: '5px' }}>Ngày thi mới</Text>
                                        <input
                                            type="date"
                                            value={approvalData.newDate}
                                            onChange={(e) => setApprovalData(prev => ({ ...prev, newDate: e.target.value }))}
                                            style={{
                                                width: '100%',
                                                padding: '10px',
                                                border: '1px solid #e0e0e0',
                                                borderRadius: '8px'
                                            }}
                                        />
                                    </Box>

                                    <Box style={{ marginBottom: '15px' }}>
                                        <Text style={{ marginBottom: '5px' }}>Ca thi mới</Text>
                                        <Select
                                            value={approvalData.newTimeSlot}
                                            onChange={(value) => setApprovalData(prev => ({ ...prev, newTimeSlot: value }))}
                                        >
                                            {Object.entries(TIME_SLOTS).map(([key, label]) => (
                                                <Option key={key} value={key} title={label} />
                                            ))}
                                        </Select>
                                    </Box>
                                </>
                            )}

                            <Box style={{ marginBottom: '15px' }}>
                                <Text style={{ marginBottom: '5px' }}>Ghi chú <Text style={{ color: 'red' }}>*</Text></Text>
                                <textarea
                                    value={approvalData.approvalNotes}
                                    onChange={(e) => setApprovalData(prev => ({ ...prev, approvalNotes: e.target.value }))}
                                    placeholder="Nhập ghi chú..."
                                    style={{
                                        width: '100%',
                                        minHeight: '80px',
                                        padding: '10px',
                                        border: '1px solid #e0e0e0',
                                        borderRadius: '8px',
                                        resize: 'vertical'
                                    }}
                                />
                            </Box>

                            <Box style={{ marginBottom: '20px' }}>
                                <ZaloNotificationToggle
                                    checked={sendZaloNotification}
                                    onChange={setSendZaloNotification}
                                    hasZaloId={selectedTeacherData?.zaloId}
                                />
                            </Box>
                        </>
                    )}
                </Box>
            </Modal>

            <BottomNavigationEdu />
        </Box>
    );
};

export default ExamChangeRequests; 
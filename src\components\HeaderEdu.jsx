// src/components/Header.jsx
import React from 'react';
import { Box, Text, Icon, useNavigate, useLocation } from 'zmp-ui';

const HeaderEdu = ({ title, subtitle, showBackButton = false, onBackClick }) => {
  const navigate = useNavigate();
  const location = useLocation();

  const handleBack = () => {
    console.log('HeaderEdu: handleBack called');
    console.log('HeaderEdu: onBackClick exists?', !!onBackClick);

    // Nếu có hàm onBackClick được truyền vào, sử dụng nó
    if (onBackClick) {
      console.log('HeaderEdu: Calling custom onBackClick');
      onBackClick();
      return;
    }

    console.log('HeaderEdu: Using default back navigation');
    console.log('HeaderEdu: location.state', location.state);
    console.log('HeaderEdu: window.history.length', window.history.length);

    // Nếu có thông tin trang trước đó trong state, quay lại trang đó
    if (location.state?.previousPath) {
      console.log('HeaderEdu: Navigating to previousPath', location.state.previousPath);
      navigate(location.state.previousPath, {
        replace: true, // Use replace to avoid adding to history
        state: location.state.previousState || {}
      });
    } else if (location.state?.returnTo) {
      console.log('HeaderEdu: Navigating to returnTo', location.state.returnTo);
      navigate(location.state.returnTo.path, {
        replace: true,
        state: location.state.returnTo.state || {}
      });
    } else if (window.history.length > 1) {
      // Use browser's native back navigation
      console.log('HeaderEdu: Using browser back navigation');
      window.history.back();
    } else {
      // Fallback navigation
      console.log('HeaderEdu: Using fallback navigation');
      navigate('/announcements', { replace: true });
    }
  };

  return (
    <Box
      className="header"
      style={{
        backgroundColor: '#0068ff',
        color: 'white',
        padding: '30px 20px',
        textAlign: 'center',
        position: 'fixed',
        top: 0,
        left: 0,
        right: 0,
        minHeight: '150px',
        display: 'flex',
        flexDirection: 'column',
        justifyContent: 'center',
        alignItems: 'center',
        zIndex: 1000,
        width: '100%',
      }}
    >
      {/* Nút back - Đã điều chỉnh kích thước và vị trí để dễ nhấn hơn trên thiết bị di động */}
      {showBackButton && (
        <Box
          className="back-button"
          style={{
            position: 'absolute',
            top: '50px', // Đưa xuống thấp hơn một chút
            left: '15px',
            backgroundColor: 'rgba(255, 255, 255, 0.3)', // Tăng độ tương phản
            borderRadius: '50%',
            width: '44px', // Tăng kích thước
            height: '44px', // Tăng kích thước
            display: 'flex',
            alignItems: 'center',
            justifyContent: 'center',
            cursor: 'pointer',
            zIndex: 10,
            boxShadow: '0 2px 5px rgba(0, 0, 0, 0.2)', // Thêm bóng đổ để nổi bật hơn
            // Thêm vùng chạm lớn hơn cho thiết bị cảm ứng
            touchAction: 'manipulation',
          }}
          onClick={handleBack}
        >
          <Icon icon="zi-arrow-left" size={28} style={{ color: 'white' }} />
        </Box>
      )}
      <Text
        className="logo"
        bold
        size="xLarge"
        style={{ marginBottom: subtitle ? '5px' : '10px' }}
      >
        {title || 'THPT Số 1 Tư Nghĩa'}
      </Text>
      {subtitle && (
        <Text
          className="subtitle"
          size="small"
          style={{ opacity: 0.9, marginBottom: '10px', lineHeight: '1.3', maxWidth: '90%' }}
        >
          {subtitle}
        </Text>
      )}
      <Text
        className="tagline"
        size="normal"
        style={{ opacity: 0.9 }}
      >
        {!title ? 'Kết nối giáo dục - Nâng tầm tri thức' : ''}
      </Text>
      <Box
        className="header-wave"
        style={{ position: 'absolute', bottom: '-1px', left: 0, width: '100%' }}
      >
        <svg
          viewBox="0 0 1200 120"
          preserveAspectRatio="none"
          style={{ width: 'calc(100% + 1.3px)', height: '20px' }}
        >
          <path
            d="M321.39,56.44c58-10.79,114.16-30.13,172-41.86,82.39-16.72,168.19-17.73,250.45-.39C823.78,31,906.67,72,985.66,92.83c70.05,18.48,146.53,26.09,214.34,3V0H0V27.35A600.21,600.21,0,0,0,321.39,56.44Z"
            style={{ fill: '#FFFFFF' }}
          />
        </svg>
      </Box>
    </Box>
  );
};

export default HeaderEdu;
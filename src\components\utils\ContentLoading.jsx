import React from 'react';
import { Box } from 'zmp-ui';
import LoadingIndicator from './LoadingIndicator';

const ContentLoading = () => {
    return (
        <Box style={{
            display: 'flex',
            justifyContent: 'center',
            alignItems: 'center',
            padding: '50px 0',
            backgroundColor: 'white',
            borderRadius: '8px',
            boxShadow: '0 2px 4px rgba(0, 0, 0, 0.1)',
            margin: '10px 0'
        }}>
            <LoadingIndicator />
        </Box>
    );
};

export default ContentLoading;

import React from 'react';
import { Box, Text, Button } from 'zmp-ui';
import DateInput from './DateInput';

const DateRangeFilter = ({
    startDate,
    endDate,
    onStartDateChange,
    onEndDateChange,
    onApply,
    onClear,
    showApplyButton = true,
    showClearButton = true,
    containerStyle = {},
    labelStyle = {},
    buttonStyle = {}
}) => {
    return (
        <Box style={{ ...containerStyle }}>
            <Box style={{ display: 'flex', gap: '10px', marginBottom: '15px' }}>
                <Box style={{ flex: 1 }}>
                    <Text style={{ fontSize: '13px', color: '#666', marginBottom: '5px', ...labelStyle }}>
                        Từ ngày:
                    </Text>
                    <DateInput
                        value={startDate}
                        onChange={(e) => onStartDateChange(e.target.value)}
                    />
                </Box>
                <Box style={{ flex: 1 }}>
                    <Text style={{ fontSize: '13px', color: '#666', marginBottom: '5px', ...labelStyle }}>
                        Đến ngày:
                    </Text>
                    <DateInput
                        value={endDate}
                        onChange={(e) => onEndDateChange(e.target.value)}
                    />
                </Box>
            </Box>

            {/* Action Buttons */}
            <Box style={{ display: 'flex', justifyContent: 'flex-end', gap: '10px' }}>
                {showClearButton && (
                    <Button
                        style={{
                            backgroundColor: '#f5f5f5',
                            color: '#666',
                            padding: '6px 12px',
                            fontSize: '12px',
                            borderRadius: '4px',
                            ...buttonStyle
                        }}
                        onClick={onClear}
                    >
                        Xóa bộ lọc
                    </Button>
                )}
                {showApplyButton && (
                    <Button
                        style={{
                            backgroundColor: '#0068ff',
                            color: 'white',
                            padding: '6px 12px',
                            fontSize: '12px',
                            borderRadius: '4px',
                            ...buttonStyle
                        }}
                        onClick={onApply}
                    >
                        Áp dụng
                    </Button>
                )}
            </Box>
        </Box>
    );
};

export default DateRangeFilter; 
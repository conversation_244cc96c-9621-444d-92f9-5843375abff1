import React, { useState, useContext, useEffect, useCallback } from 'react';
import { Box, Text, Button, useNavigate, useLocation, Input, Modal } from 'zmp-ui';
import HeaderEdu from '../../components/HeaderEdu';
import HeaderSpacer from '../../components/utils/HeaderSpacer';
import BottomNavigationEdu from '../../components/BottomNavigationEdu';
import { AuthContext } from '../../context/AuthContext';
import { authApi } from '../../utils/api';
import { format } from 'date-fns';
import vi from 'date-fns/locale/vi';
import LoadingIndicator from '../../components/utils/LoadingIndicator';
import DateInput from '../../components/utils/DateInput';
import notificationService from '../../utils/notificationService';
import { ICONS } from '@/constants/icons';

const StudentAttendanceDetails = () => {
    const navigate = useNavigate();
    const location = useLocation();
    const { user, loading: authLoading } = useContext(AuthContext);
    const { student, classItem } = location.state || {};
    const [attendanceRecords, setAttendanceRecords] = useState([]);
    const [loading, setLoading] = useState(true);
    const [error, setError] = useState('');
    const [dateFilter, setDateFilter] = useState({
        startDate: '',
        endDate: ''
    });
    const [displayDateFilter, setDisplayDateFilter] = useState({
        startDate: '',
        endDate: ''
    });
    const [showFilters, setShowFilters] = useState(false);
    const [rejectModal, setRejectModal] = useState({
        visible: false,
        attendanceId: null,
        reason: ''
    });

    // Fetch attendance records
    const fetchAttendanceRecords = useCallback(async () => {
        if (!student?.student?.id) return;

        setLoading(true);
        setError('');
        try {
            const params = new URLSearchParams();
            if (dateFilter.startDate) {
                params.append('startDate', dateFilter.startDate);
            }
            if (dateFilter.endDate) {
                params.append('endDate', dateFilter.endDate);
            }

            const response = await authApi.get(`/attendance/student/${student.student.id}?${params.toString()}`);
            if (response.data.success) {
                setAttendanceRecords(response.data.data || []);
            }
        } catch (err) {
            console.error('Error fetching attendance records:', err);
            setError('Lỗi khi tải dữ liệu điểm danh');
            notificationService.showToast({
                type: 'error',
                message: 'Lỗi khi tải dữ liệu điểm danh'
            });
        } finally {
            setLoading(false);
        }
    }, [student?.student?.id, dateFilter.startDate, dateFilter.endDate]);

    // Fetch data on mount and when filters change
    useEffect(() => {
        fetchAttendanceRecords();
    }, [fetchAttendanceRecords]);

    // Handle date filter change
    const handleDateFilterChange = (field, value) => {
        setDateFilter(prev => ({
            ...prev,
            [field]: value
        }));
    };

    // Clear filters
    const clearFilters = () => {
        setDateFilter({
            startDate: '',
            endDate: ''
        });
    };

    // Format date
    const formatDate = (dateString) => {
        try {
            return format(new Date(dateString), 'dd/MM/yyyy', { locale: vi });
        } catch {
            return 'N/A';
        }
    };

    // Get status color and text
    const getStatusInfo = (status) => {
        const statusMap = {
            present: { color: '#34c759', text: 'Có mặt', bg: '#e6f7ee' },
            late: { color: '#ff9500', text: 'Muộn', bg: '#fff8e0' },
            absent: { color: '#ff3b30', text: 'Vắng', bg: '#ffe5e5' },
            rejected: { color: '#8e8e93', text: 'Từ chối', bg: '#f2f2f7' }
        };
        return statusMap[status] || statusMap.absent;
    };

    // Handle reject attendance
    const handleRejectAttendance = (attendanceId) => {
        setRejectModal({
            visible: true,
            attendanceId,
            reason: ''
        });
    };

    // Handle submit reject
    const handleSubmitReject = async () => {
        if (!rejectModal.reason.trim()) {
            notificationService.showToast({
                type: 'error',
                message: 'Vui lòng nhập lý do từ chối điểm danh'
            });
            return;
        }

        try {
            notificationService.showLoading('Đang xử lý...');
            const response = await authApi.put(`/attendance/${rejectModal.attendanceId}/reject`, {
                reason: rejectModal.reason
            });

            if (response.data.success) {
                notificationService.showToast('Từ chối điểm danh thành công', 'success');
                setRejectModal({ visible: false, attendanceId: null, reason: '' });
                fetchAttendanceRecords();
            } else {
                notificationService.showToast(
                    response.data.message || 'Có lỗi xảy ra khi từ chối điểm danh',
                    'error'
                );
            }
        } catch (err) {
            notificationService.showToast(
                err.response?.data?.message || 'Có lỗi xảy ra khi từ chối điểm danh',
                'error'
            );
        } finally {
            notificationService.hideLoading();
        }
    };

    if (authLoading) {
        return (
            <Box style={{ display: 'flex', justifyContent: 'center', alignItems: 'center', minHeight: '100vh' }}>
                <LoadingIndicator />
            </Box>
        );
    }

    return (
        <Box className="container" style={{ minHeight: '100vh', display: 'flex', flexDirection: 'column', paddingBottom: '60px', backgroundColor: '#f5f5f5' }}>
            <HeaderEdu
                title="Chi tiết điểm danh"
                showBackButton={true}
                onBackClick={() => navigate(-1)}
            />
            <HeaderSpacer />

            <Box style={{ padding: '15px', flex: 1 }}>
                {/* Student Info */}
                <Box style={{ backgroundColor: 'white', borderRadius: '10px', padding: '15px', marginBottom: '15px' }}>
                    <Box style={{ display: 'flex', alignItems: 'center', marginBottom: '15px' }}>
                        <Box
                            style={{
                                width: '50px',
                                height: '50px',
                                borderRadius: '25px',
                                backgroundColor: student?.attendanceRate > 80 ? '#4CAF50' :
                                    student?.attendanceRate > 60 ? '#FF9800' : '#F44336',
                                display: 'flex',
                                alignItems: 'center',
                                justifyContent: 'center',
                                color: 'white',
                                fontSize: '20px',
                                fontWeight: 'bold',
                                marginRight: '15px'
                            }}
                        >
                            {student?.student?.name?.charAt(0)}
                        </Box>
                        <Box style={{ flex: 1 }}>
                            <Text bold size="large" style={{ marginBottom: '5px' }}>
                                {student?.student?.name}
                            </Text>
                            <Text style={{ fontSize: '14px', color: '#666' }}>
                                Mã học sinh: {student?.student?.studentId}
                            </Text>
                            {classItem && (
                                <Text style={{ fontSize: '14px', color: '#666' }}>
                                    Lớp: {classItem.name}
                                </Text>
                            )}
                        </Box>
                    </Box>
                </Box>

                {/* Filters */}
                <Box style={{ backgroundColor: 'white', borderRadius: '10px', padding: '15px', marginBottom: '15px' }}>
                    <Box style={{ display: 'flex', justifyContent: 'space-between', alignItems: 'center', marginBottom: '15px' }}>
                        <Text bold size="large">Bộ lọc</Text>
                        <Button
                            size="small"
                            onClick={() => setShowFilters(!showFilters)}
                            style={{ backgroundColor: '#f0f8ff', color: '#0068ff' }}
                        >
                            {showFilters ? 'Ẩn' : 'Hiện'} bộ lọc
                        </Button>
                    </Box>

                    {showFilters && (
                        <>
                            <Box style={{ display: 'grid', gridTemplateColumns: 'repeat(2, 1fr)', gap: '12px', marginBottom: '15px' }}>
                                <Box>
                                    <Text style={{ marginBottom: '8px', fontSize: '14px', fontWeight: '500' }}>
                                        Từ ngày:
                                    </Text>
                                    <DateInput
                                        value={dateFilter.startDate}
                                        onChange={(e) => handleDateFilterChange('startDate', e.target.value)}
                                        style={{
                                            padding: '10px',
                                            borderRadius: '6px',
                                            fontSize: '14px'
                                        }}
                                    />
                                </Box>
                                <Box>
                                    <Text style={{ marginBottom: '8px', fontSize: '14px', fontWeight: '500' }}>
                                        Đến ngày:
                                    </Text>
                                    <DateInput
                                        value={dateFilter.endDate}
                                        onChange={(e) => handleDateFilterChange('endDate', e.target.value)}
                                        min={dateFilter.startDate}
                                        style={{
                                            padding: '10px',
                                            borderRadius: '6px',
                                            fontSize: '14px'
                                        }}
                                    />
                                </Box>
                            </Box>
                            <Box style={{ display: 'flex', gap: '10px' }}>
                                <Button
                                    size="small"
                                    onClick={clearFilters}
                                    style={{ backgroundColor: '#f5f5f5', color: '#666' }}
                                >
                                    Xóa bộ lọc
                                </Button>
                                <Button
                                    size="small"
                                    onClick={fetchAttendanceRecords}
                                    style={{ backgroundColor: '#0068ff', color: 'white' }}
                                >
                                    Áp dụng
                                </Button>
                            </Box>
                        </>
                    )}
                </Box>

                {/* Attendance Records */}
                {loading ? (
                    <Box style={{ display: 'flex', justifyContent: 'center', padding: '40px' }}>
                        <LoadingIndicator />
                    </Box>
                ) : error ? (
                    <Box style={{ backgroundColor: 'white', borderRadius: '10px', padding: '20px', textAlign: 'center' }}>
                        <Text style={{ color: '#ff3b30' }}>{error}</Text>
                    </Box>
                ) : attendanceRecords.length === 0 ? (
                    <Box style={{ backgroundColor: 'white', borderRadius: '10px', padding: '40px', textAlign: 'center' }}>
                        <Text style={{ fontSize: '48px', marginBottom: '10px' }}>{ICONS.DRAFT}</Text>
                        <Text bold size="large" style={{ marginBottom: '8px', color: '#666' }}>
                            Chưa có dữ liệu điểm danh
                        </Text>
                        <Text style={{ color: '#999' }}>
                            Không tìm thấy bản ghi điểm danh nào trong khoảng thời gian đã chọn
                        </Text>
                    </Box>
                ) : (
                    <Box style={{ display: 'flex', flexDirection: 'column', gap: '12px' }}>
                        {attendanceRecords.map((record) => {
                            const statusInfo = getStatusInfo(record.status);
                            return (
                                <Box
                                    key={record._id}
                                    style={{
                                        backgroundColor: 'white',
                                        borderRadius: '10px',
                                        padding: '15px',
                                        border: `1px solid ${statusInfo.color}20`,
                                        boxShadow: '0 2px 8px rgba(0,0,0,0.1)'
                                    }}
                                >
                                    <Box style={{ display: 'flex', justifyContent: 'space-between', alignItems: 'flex-start', marginBottom: '10px' }}>
                                        <Box>
                                            <Text bold style={{ fontSize: '16px', marginBottom: '5px' }}>
                                                {formatDate(record.date)} - {record.session === 'morning' ? 'Sáng' : 'Chiều'}
                                            </Text>
                                            {record.location && (
                                                <Text style={{ fontSize: '12px', color: '#666' }}>
                                                    {ICONS.ROUND_PUSHPIN} {record.location}
                                                </Text>
                                            )}
                                            {record.location && (
                                                <Text style={{ fontSize: '12px', color: '#666' }}>
                                                    {ICONS.BOOK} {record.rejectionReason}
                                                </Text>
                                            )}
                                        </Box>
                                        <Box
                                            style={{
                                                backgroundColor: statusInfo.bg,
                                                color: statusInfo.color,
                                                padding: '4px 8px',
                                                borderRadius: '12px',
                                                fontSize: '12px',
                                                fontWeight: 'bold'
                                            }}
                                        >
                                            {statusInfo.text}
                                        </Box>
                                    </Box>

                                    {record.notes && (
                                        <Text style={{ fontSize: '14px', color: '#666', marginBottom: '10px' }}>
                                            {record.notes}
                                        </Text>
                                    )}

                                    {record.status !== 'rejected' && (
                                        <Button
                                            size="small"
                                            onClick={() => handleRejectAttendance(record._id)}
                                            style={{
                                                backgroundColor: '#ff3b30',
                                                color: 'white',
                                                marginTop: '10px'
                                            }}
                                        >
                                            Từ chối điểm danh
                                        </Button>
                                    )}
                                </Box>
                            );
                        })}
                    </Box>
                )}
            </Box>

            {/* Reject Modal */}
            <Modal
                visible={rejectModal.visible}
                title="Từ chối điểm danh"
                onClose={() => setRejectModal({ visible: false, attendanceId: null, reason: '' })}
                actions={[
                    {
                        text: 'Hủy',
                        onClick: () => setRejectModal({ visible: false, attendanceId: null, reason: '' })
                    },
                    {
                        text: 'Xác nhận',
                        onClick: handleSubmitReject,
                        style: { backgroundColor: '#ff3b30', color: 'white' }
                    }
                ]}
            >
                <Box style={{ padding: '20px' }}>
                    <Text style={{ marginBottom: '10px' }}>
                        Vui lòng nhập lý do từ chối điểm danh:
                    </Text>
                    <Input
                        type="textarea"
                        value={rejectModal.reason}
                        onChange={(e) => setRejectModal(prev => ({ ...prev, reason: e.target.value }))}
                        placeholder="Nhập lý do từ chối..."
                        style={{ minHeight: '100px' }}
                    />
                </Box>
            </Modal>

            <BottomNavigationEdu />
        </Box>
    );
};

export default StudentAttendanceDetails; 
import React, { useState, useEffect, useContext } from 'react';
import { Box, Text, Button, useNavigate } from 'zmp-ui';
import { AuthContext } from '../../context/AuthContext';
import { authApi } from '../../utils/api';
import HeaderEdu from '../../components/HeaderEdu';
import HeaderSpacer from '../../components/utils/HeaderSpacer';
import BottomNavigationEdu from '../../components/BottomNavigationEdu';
import LoadingIndicator from '../../components/utils/LoadingIndicator';
import useNotification from '../../hooks/useNotification';
import { EXAM_TYPES, TIME_SLOTS, EXAM_CHANGE_REQUEST_TABS, EXAM_CHANGE_REQUEST_STATUS_LABELS, EXAM_CHANGE_REQUEST_STATUS_COLORS } from '../../constants/exam';
import DateInput from '../../components/utils/DateInput';
import DateRangeFilter from '../../components/utils/DateRangeFilter';
import TabFilter from '../../components/utils/TabFilter';
import { useSchoolYear } from '../../context/SchoolYearContext';
import { formatDate, formatDateTime } from '../../utils/dateUtils';
import { SESSION_STATUS_DISPLAY } from '@/constants/sessionStatus';
import { ICONS } from '@/constants/icons';

const MyExamChangeRequests = () => {
    const navigate = useNavigate();
    const { user } = useContext(AuthContext);
    const notification = useNotification();
    const [loading, setLoading] = useState(true);
    const [dataLoading, setDataLoading] = useState(false);
    const [requests, setRequests] = useState([]);
    const [selectedTab, setSelectedTab] = useState('PENDING');
    const { selectedSchoolYear } = useSchoolYear();

    // Filter states
    const [dateFilter, setDateFilter] = useState({
        startDate: '',
        endDate: ''
    });
    const [showFilters, setShowFilters] = useState(false);





    // Fetch my change requests
    const fetchMyChangeRequests = async (isInitialLoad = false) => {
        if (isInitialLoad) {
            setLoading(true);
        } else {
            setDataLoading(true);
        }
        try {
            const queryParams = new URLSearchParams();
            if (selectedTab) {
                queryParams.append('status', selectedTab);
            }
            if (dateFilter.startDate) {
                queryParams.append('startDate', dateFilter.startDate);
            }
            if (dateFilter.endDate) {
                queryParams.append('endDate', dateFilter.endDate);
            }

            const response = await authApi.get(`/exam-change-requests/my-requests?${queryParams.toString()}`);
            if (response.data) {
                setRequests(response.data || []);
            }
        } catch (error) {
            console.error('Error fetching my change requests:', error);
            notification.showError('Lỗi', 'Không thể tải danh sách yêu cầu đổi buổi');
        } finally {
            setLoading(false);
            setDataLoading(false);
        }
    };

    useEffect(() => {
        fetchMyChangeRequests(true);
    }, []);

    useEffect(() => {
        if (loading === false) { // Only fetch when not initial load
            fetchMyChangeRequests(false);
        }
    }, [selectedTab]);

    // Handle date filter change
    const handleDateFilterChange = (field, value) => {
        setDateFilter(prev => ({
            ...prev,
            [field]: value
        }));
    };

    // Clear filters
    const clearFilters = () => {
        setDateFilter({
            startDate: '',
            endDate: ''
        });
    };

    if (loading) {
        return (
            <Box className="container" style={{ minHeight: '100vh', display: 'flex', flexDirection: 'column', paddingBottom: '60px', backgroundColor: '#f5f5f5' }}>
                <HeaderEdu title="Yêu cầu đổi buổi của tôi" showBackButton={true} onBackClick={() => navigate(-1)} />
                <HeaderSpacer />
                <Box style={{ flex: 1, display: 'flex', justifyContent: 'center', alignItems: 'center' }}>
                    <LoadingIndicator />
                </Box>
            </Box>
        );
    }

    return (
        <Box className="container" style={{ minHeight: '100vh', display: 'flex', flexDirection: 'column', paddingBottom: '60px', backgroundColor: '#f5f5f5' }}>
            <HeaderEdu title="Yêu cầu đổi buổi của tôi" showBackButton={true} onBackClick={() => navigate(-1)} />
            <HeaderSpacer />
            
            <Box style={{ padding: '15px', flex: 1 }}>
                {/* Filters */}
                <Box style={{ backgroundColor: 'white', borderRadius: '10px', padding: '15px', marginBottom: '15px' }}>
                    <Box style={{ display: 'flex', justifyContent: 'space-between', alignItems: 'center', marginBottom: '15px' }}>
                        <Text bold size="large">Bộ lọc</Text>
                        <Button
                            size="small"
                            onClick={() => setShowFilters(!showFilters)}
                            style={{ backgroundColor: '#f0f8ff', color: '#0068ff' }}
                        >
                            {showFilters ? 'Ẩn' : 'Hiện'} bộ lọc
                        </Button>
                    </Box>

                    {showFilters && (
                        <DateRangeFilter
                            startDate={dateFilter.startDate}
                            endDate={dateFilter.endDate}
                            onStartDateChange={(value) => handleDateFilterChange('startDate', value)}
                            onEndDateChange={(value) => handleDateFilterChange('endDate', value)}
                            onApply={() => fetchMyChangeRequests(false)}
                            onClear={clearFilters}
                        />
                    )}
                </Box>

                {/* Tab filter */}
                <TabFilter
                    tabs={EXAM_CHANGE_REQUEST_TABS}
                    selectedTab={selectedTab}
                    onTabChange={setSelectedTab}
                />

                {/* Data Loading Indicator */}
                {dataLoading && (
                    <Box style={{ backgroundColor: 'white', borderRadius: '10px', padding: '20px', marginBottom: '15px', textAlign: 'center' }}>
                        <LoadingIndicator />
                        <Text style={{ marginTop: '10px', color: '#666' }}>Đang tải dữ liệu...</Text>
                    </Box>
                )}

                {/* Stats Summary */}
                {!loading && requests.length > 0 && (
                    <Box style={{ backgroundColor: 'white', borderRadius: '10px', padding: '15px', marginBottom: '15px' }}>
                        <Text style={{ fontSize: '14px', color: '#666' }}>
                            Hiển thị {requests.length} yêu cầu đổi buổi
                        </Text>
                    </Box>
                )}

                {/* List */}
                {requests.length === 0 && !dataLoading ? (
                    <Box style={{ backgroundColor: 'white', borderRadius: '10px', padding: '40px', textAlign: 'center' }}>
                        <Text style={{ fontSize: '48px', marginBottom: '10px' }}>{ICONS.DRAFT}</Text>
                        <Text bold size="large" style={{ marginBottom: '8px', color: '#666' }}>
                            Chưa có yêu cầu đổi buổi
                        </Text>
                        <Text style={{ color: '#999' }}>
                            {selectedTab === 'PENDING' ? 'Chưa có yêu cầu đổi buổi nào cần duyệt' : 
                             selectedTab === 'APPROVED' ? 'Chưa có yêu cầu đổi buổi nào được duyệt' : 
                             'Chưa có yêu cầu đổi buổi nào bị từ chối'}
                        </Text>
                    </Box>
                ) : !dataLoading && (
                    <Box style={{ display: 'flex', flexDirection: 'column', gap: '12px' }}>
                        {requests.map((request) => (
                            <Box
                                key={request._id}
                                style={{
                                    backgroundColor: 'white',
                                    borderRadius: '10px',
                                    padding: '15px',
                                    border: `1px solid ${EXAM_CHANGE_REQUEST_STATUS_COLORS[request.status]}20`,
                                    boxShadow: '0 2px 8px rgba(0,0,0,0.1)'
                                }}
                            >
                                <Box style={{ display: 'flex', justifyContent: 'space-between', alignItems: 'flex-start', marginBottom: '10px' }}>
                                    <Box style={{ flex: 1 }}>
                                        <Text bold size="large" style={{ marginBottom: '5px' }}>                            
                                            <Text bold>{EXAM_TYPES[request.supervision.examType]} - {request.supervision.schoolYear}</Text>
                                        </Text>
                                        <Text style={{ fontSize: '12px', color: '#666', marginBottom: '8px' }}>
                                            {ICONS.CALENDAR} {formatDateTime(request.createdAt)}
                                        </Text>
                                    </Box>
                                    <Box
                                        style={{
                                            backgroundColor: `${EXAM_CHANGE_REQUEST_STATUS_COLORS[request.status]}20`,
                                            color: EXAM_CHANGE_REQUEST_STATUS_COLORS[request.status],
                                            padding: '4px 8px',
                                            borderRadius: '12px',
                                            fontSize: '12px',
                                            fontWeight: 'bold'
                                        }}
                                    >
                                        {EXAM_CHANGE_REQUEST_STATUS_LABELS[request.status]}
                                    </Box>
                                </Box>
                                
                                <Box style={{ marginBottom: '10px' }}>
                                    <Text style={{ fontSize: '14px', marginBottom: '5px' }}>
                                        <Text bold>{ICONS.CALENDAR} Buổi thi hiện tại:</Text>
                                    </Text>
                                    <Text style={{ fontSize: '12px', color: '#666' }}>
                                        {formatDate(request.originalDate)} - {TIME_SLOTS[request.originalTimeSlot]}
                                    </Text>
                                    <Text style={{ fontSize: '12px', color: '#666' }}>
                                        Phòng: {request.currentRoom}
                                    </Text>
                                    <Text style={{ fontSize: '12px', color: '#666' }}>
                                        Môn: {request.currentSubject}
                                    </Text>
                                </Box>

                                <Box style={{ marginBottom: '10px' }}>
                                    <Text style={{ fontSize: '14px', marginBottom: '5px' }}>
                                        <Text bold>{SESSION_STATUS_DISPLAY.YEU_CAU_DOI_BUOI.icon} Yêu cầu đổi thành:</Text>
                                    </Text>
                                    <Text style={{ fontSize: '12px', color: '#666' }}>
                                        {new Date(request.proposedNewDate).toLocaleDateString('vi-VN')} - {TIME_SLOTS[request.proposedNewTimeSlot]}
                                    </Text>
                                </Box>

                                <Text style={{ fontSize: '14px', color: '#333', lineHeight: 1.4 }}>
                                    <Text bold>{ICONS.DRAFT} Lý do:</Text> {request.reason}
                                </Text>

                                {request.status !== 'PENDING' && request.approvalNotes && (
                                    <Box style={{ marginTop: '10px', padding: '8px', backgroundColor: '#f9f9f9', borderRadius: '6px' }}>
                                        <Text style={{ fontSize: '12px', color: '#666' }}>
                                            <Text bold>{ICONS.MESSAGE} Ghi chú duyệt:</Text> {request.approvalNotes}
                                        </Text>
                                    </Box>
                                )}

                                {request.status === 'APPROVED' && request.newDate && (
                                    <Box style={{ marginTop: '10px', padding: '10px', backgroundColor: '#e8f5e8', borderRadius: '4px' }}>
                                        <Text style={{ fontSize: '12px', color: '#34c759', fontWeight: 'bold' }}>
                                            Buổi thi mới: {new Date(request.newDate).toLocaleDateString('vi-VN')} - {TIME_SLOTS[request.newTimeSlot]}
                                        </Text>
                                    </Box>
                                )}
                            </Box>
                        ))}
                    </Box>
                )}
            </Box>

            <BottomNavigationEdu />
        </Box>
    );
};

export default MyExamChangeRequests; 
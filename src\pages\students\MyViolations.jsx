import React, { useState, useEffect, useContext } from 'react';
import { Box, Text, useNavigate, Modal, Button, Input, Select } from 'zmp-ui';
import HeaderEdu from '../../components/HeaderEdu';
import HeaderSpacer from '../../components/utils/HeaderSpacer';
import BottomNavigationEdu from '../../components/BottomNavigationEdu';
import { AuthContext } from '../../context/AuthContext';
import { authApi } from '../../utils/api';
import { ICONS } from '../../constants/icons';
import {
    VIOLATION_PAGINATION,
    VIOLATION_STATUS,
    VIOLATION_TYPES,
    VIOLATION_TYPE_LABELS,
    APPEAL_RULES,
    getViolationTypeLabel,
    getViolationStatusInfo
} from '../../constants/violations';
import LoadingIndicator from '../../components/utils/LoadingIndicator';
import useNotification from '../../hooks/useNotification';
import { formatDistanceToNow } from 'date-fns';
import vi from 'date-fns/locale/vi';
import { useSchoolYear } from '../../context/SchoolYearContext';

const MyViolations = () => {
    const navigate = useNavigate();
    const { user } = useContext(AuthContext);
    const { error, success } = useNotification();
    const { selectedSchoolYear } = useSchoolYear();
    const [violations, setViolations] = useState([]);
    const [loading, setLoading] = useState(true);
    const [selectedViolation, setSelectedViolation] = useState(null);
    const [detailVisible, setDetailVisible] = useState(false);
    const [appealVisible, setAppealVisible] = useState(false);
    const [appealReason, setAppealReason] = useState('');
    const [submitting, setSubmitting] = useState(false);
    const [page, setPage] = useState(1);
    const [hasMore, setHasMore] = useState(true);
    const [filters, setFilters] = useState({
        violationType: '',
        status: '',
        startDate: '',
        endDate: ''
    });
    const [showFilters, setShowFilters] = useState(false);

    // Fetch violations
    const fetchViolations = async (pageNum = 1, append = false) => {
        try {
            if (!append) setLoading(true);

            const params = new URLSearchParams({
                page: pageNum.toString(),
                limit: VIOLATION_PAGINATION.DEFAULT_LIMIT.toString(),
                studentId: user._id
            });

            if (selectedSchoolYear) {
                params.append('schoolYear', selectedSchoolYear);
            }

            // Add filters
            if (filters.violationType) {
                params.append('violationType', filters.violationType);
            }
            if (filters.status) {
                params.append('status', filters.status);
            }
            if (filters.startDate) {
                params.append('startDate', filters.startDate);
            }
            if (filters.endDate) {
                params.append('endDate', filters.endDate);
            }

            const response = await authApi.get(`/violations?${params.toString()}`);
            console.log('API Response:', response.data); // Debug log
            
            // Handle different response structures
            const newViolations = response.data?.data?.docs || response.data?.violations || response.data?.data || [];
            
            if (append) {
                setViolations(prev => [...prev, ...newViolations]);
            } else {
                setViolations(newViolations);
            }
            
            // Handle different pagination structures
            const paginationData = response.data?.data || response.data?.pagination || response.data;
            setHasMore(paginationData?.hasNextPage || paginationData?.hasNext || false);
            setPage(pageNum);
        } catch (error) {
            console.error('Error fetching violations:', error);
        } finally {
            setLoading(false);
        }
    };

    useEffect(() => {
        if (user?._id) {
            fetchViolations();
        }
    }, [user]);

    // Fetch violations when filters change
    useEffect(() => {
        if (user?._id) {
            fetchViolations(1, false);
        }
    }, [filters]);





    // Handle violation detail
    const handleViolationDetail = async (violation) => {
        try {
            const response = await authApi.get(`/violations/${violation._id}`);
            // Handle different response structures
            const violationData = response.data?.data || response.data;
            setSelectedViolation(violationData);
            setDetailVisible(true);
        } catch (error) {
            console.error('Error fetching violation detail:', error);
        }
    };

    // Handle appeal submission
    const handleAppealSubmit = async () => {
        if (appealReason.trim().length < APPEAL_RULES.MIN_REASON_LENGTH) {
            error(`Lý do khiếu nại phải có ít nhất ${APPEAL_RULES.MIN_REASON_LENGTH} ký tự`);
            return;
        }

        try {
            setSubmitting(true);
            await authApi.post(`/violations/${selectedViolation._id}/appeal`, {
                appealReason: appealReason.trim()
            });
            
            setAppealVisible(false);
            setAppealReason('');
            setSelectedViolation(null);
            fetchViolations(); // Refresh list
            success('Gửi khiếu nại thành công');
        } catch (err) {
            console.error('Error submitting appeal:', err);
            error('Có lỗi xảy ra khi gửi khiếu nại');
        } finally {
            setSubmitting(false);
        }
    };

    // Load more violations
    const loadMore = () => {
        if (hasMore && !loading) {
            fetchViolations(page + 1, true);
        }
    };

    // Handle filter change
    const handleFilterChange = (field, value) => {
        setFilters(prev => ({
            ...prev,
            [field]: value
        }));
        setPage(1); // Reset to first page when filter changes
    };

    // Clear all filters
    const clearFilters = () => {
        setFilters({
            violationType: '',
            status: '',
            startDate: '',
            endDate: ''
        });
    };

    if (loading && violations.length === 0) {
        return (
            <Box className="container" style={{ minHeight: '100vh', display: 'flex', flexDirection: 'column', paddingBottom: '60px', backgroundColor: '#f5f5f5' }}>
                <HeaderEdu 
                    title="Vi phạm của tôi"
                    showBackButton={true}
                    onBackClick={() => navigate('/student')}
                />
                <HeaderSpacer />
                <Box style={{ flex: 1, display: 'flex', justifyContent: 'center', alignItems: 'center' }}>
                    <LoadingIndicator />
                </Box>
            </Box>
        );
    }

    return (
        <Box className="container" style={{ minHeight: '100vh', display: 'flex', flexDirection: 'column', paddingBottom: '60px', backgroundColor: '#f5f5f5' }}>
            <HeaderEdu 
                title="Vi phạm của tôi"
                showBackButton={true}
                onBackClick={() => navigate('/student')}
            />
            <HeaderSpacer />

            <Box style={{ flex: 1, padding: '15px' }}>
                {/* Filter Section */}
                <Box style={{
                    backgroundColor: 'white',
                    borderRadius: '12px',
                    padding: '15px',
                    marginBottom: '15px',
                    boxShadow: '0 2px 8px rgba(0,0,0,0.1)'
                }}>
                    <Box style={{ display: 'flex', justifyContent: 'space-between', alignItems: 'center', marginBottom: '15px' }}>
                        <Text bold style={{ fontSize: '16px', color: '#333' }}>
                            {ICONS.FILTER} Bộ lọc
                        </Text>
                        <Button
                            size="small"
                            variant="tertiary"
                            onClick={() => setShowFilters(!showFilters)}
                            style={{ fontSize: '12px' }}
                        >
                            {showFilters ? 'Ẩn' : 'Hiện'}
                        </Button>
                    </Box>

                    {showFilters && (
                        <Box style={{ display: 'grid', gap: '10px' }}>
                            {/* Violation Type Filter */}
                            <Box>
                                <Text style={{ fontSize: '14px', marginBottom: '5px', color: '#666' }}>Loại vi phạm:</Text>
                                <Select
                                    placeholder="Chọn loại vi phạm"
                                    value={filters.violationType}
                                    onChange={(value) => handleFilterChange('violationType', value)}
                                >
                                    <Select.Option value="">Tất cả</Select.Option>
                                    {Object.entries(VIOLATION_TYPE_LABELS).map(([key, label]) => (
                                        <Select.Option key={key} value={key}>{label}</Select.Option>
                                    ))}
                                </Select>
                            </Box>

                            {/* Status Filter */}
                            <Box>
                                <Text style={{ fontSize: '14px', marginBottom: '5px', color: '#666' }}>Trạng thái:</Text>
                                <Select
                                    placeholder="Chọn trạng thái"
                                    value={filters.status}
                                    onChange={(value) => handleFilterChange('status', value)}
                                >
                                    <Select.Option value="">Tất cả</Select.Option>
                                    <Select.Option value="pending">Chờ xử lý</Select.Option>
                                    <Select.Option value="processed">Đã xử lý</Select.Option>
                                    <Select.Option value="appealed">Đã khiếu nại</Select.Option>
                                    <Select.Option value="appeal_approved">Khiếu nại được chấp nhận</Select.Option>
                                    <Select.Option value="appeal_rejected">Khiếu nại bị từ chối</Select.Option>
                                </Select>
                            </Box>

                            {/* Date Range Filter */}
                            <Box style={{ display: 'grid', gridTemplateColumns: '1fr 1fr', gap: '10px' }}>
                                <Box>
                                    <Text style={{ fontSize: '14px', marginBottom: '5px', color: '#666' }}>Từ ngày:</Text>
                                    <Input
                                        type="date"
                                        value={filters.startDate}
                                        onChange={(e) => handleFilterChange('startDate', e.target.value)}
                                    />
                                </Box>
                                <Box>
                                    <Text style={{ fontSize: '14px', marginBottom: '5px', color: '#666' }}>Đến ngày:</Text>
                                    <Input
                                        type="date"
                                        value={filters.endDate}
                                        onChange={(e) => handleFilterChange('endDate', e.target.value)}
                                    />
                                </Box>
                            </Box>

                            {/* Clear Filters Button */}
                            <Button
                                size="small"
                                variant="secondary"
                                onClick={clearFilters}
                                style={{ marginTop: '10px' }}
                            >
                                Xóa bộ lọc
                            </Button>
                        </Box>
                    )}
                </Box>

                {violations.length === 0 ? (
                    <Box style={{ textAlign: 'center', padding: '40px 20px' }}>
                        <Text style={{ fontSize: '48px', marginBottom: '15px' }}>{ICONS.SUCCESS}</Text>
                        <Text bold style={{ fontSize: '18px', marginBottom: '10px', color: '#28a745' }}>
                            Chưa có vi phạm nào
                        </Text>
                        <Text style={{ color: '#666', fontSize: '14px' }}>
                            Bạn đang có hạnh kiểm tốt. Hãy tiếp tục duy trì!
                        </Text>
                    </Box>
                ) : (
                    <Box style={{ backgroundColor: 'white', borderRadius: '12px', padding: '15px' }}>
                        <Box style={{ display: 'flex', justifyContent: 'space-between', alignItems: 'center', marginBottom: '15px' }}>
                            <Text bold style={{ fontSize: '18px', color: '#333' }}>
                                {ICONS.WARNING} Danh sách vi phạm
                            </Text>
                            {violations.length > 0 && (
                                <Text style={{ fontSize: '14px', color: '#666' }}>
                                    {violations.length} vi phạm
                                </Text>
                            )}
                        </Box>

                        {loading && violations.length === 0 ? (
                            <Box style={{ display: 'flex', justifyContent: 'center', alignItems: 'center', padding: '40px' }}>
                                <LoadingIndicator />
                            </Box>
                        ) : (
                            <Box>
                                {violations.map((violation, index) => {
                                    const statusInfo = getViolationStatusInfo(violation.status);
                                    return (
                                        <Box
                                            key={violation._id || index}
                                            onClick={() => handleViolationDetail(violation)}
                                            style={{
                                                padding: '15px',
                                                border: '1px solid #e0e0e0',
                                                borderRadius: '8px',
                                                marginBottom: '10px',
                                                backgroundColor: '#fff',
                                                cursor: 'pointer',
                                                transition: 'all 0.2s ease',
                                                boxShadow: '0 1px 3px rgba(0,0,0,0.1)'
                                            }}
                                            onMouseEnter={(e) => {
                                                e.currentTarget.style.transform = 'translateY(-2px)';
                                                e.currentTarget.style.boxShadow = '0 4px 12px rgba(0,0,0,0.15)';
                                            }}
                                            onMouseLeave={(e) => {
                                                e.currentTarget.style.transform = 'translateY(0)';
                                                e.currentTarget.style.boxShadow = '0 1px 3px rgba(0,0,0,0.1)';
                                            }}
                                        >
                                            <Box style={{ display: 'flex', justifyContent: 'space-between', alignItems: 'flex-start', marginBottom: '10px' }}>
                                                <Box style={{ flex: 1 }}>
                                                    <Text bold style={{ fontSize: '16px', color: '#333', marginBottom: '4px' }}>
                                                        {getViolationTypeLabel(violation.violationType)}
                                                    </Text>
                                                    <Text style={{ fontSize: '14px', color: '#666', marginBottom: '8px' }}>
                                                        {violation.description}
                                                    </Text>
                                                </Box>
                                                <Text style={{ 
                                                    fontSize: '16px', 
                                                    fontWeight: 'bold', 
                                                    color: '#dc3545',
                                                    marginLeft: '15px'
                                                }}>
                                                    -{violation.pointsDeducted} điểm
                                                </Text>
                                            </Box>
                                            
                                            <Box style={{ display: 'flex', justifyContent: 'space-between', alignItems: 'center' }}>
                                                <Text style={{ fontSize: '12px', color: '#999' }}>
                                                    {ICONS.CALENDAR} {formatDistanceToNow(new Date(violation.violationDate), { 
                                                        addSuffix: true, 
                                                        locale: vi 
                                                    })}
                                                </Text>
                                                
                                                <Box style={{
                                                    padding: '6px 10px',
                                                    borderRadius: '16px',
                                                    backgroundColor: statusInfo.bg,
                                                    border: `1px solid ${statusInfo.color}`
                                                }}>
                                                    <Text style={{ fontSize: '11px', color: statusInfo.color, fontWeight: 'bold' }}>
                                                        {statusInfo.label}
                                                    </Text>
                                                </Box>
                                            </Box>
                                        </Box>
                                    );
                                })}

                                {hasMore && (
                                    <Box style={{ textAlign: 'center', padding: '20px' }}>
                                        <Button 
                                            onClick={loadMore}
                                            loading={loading}
                                            style={{ backgroundColor: '#0068ff', color: 'white' }}
                                        >
                                            Tải thêm
                                        </Button>
                                    </Box>
                                )}
                            </Box>
                        )}
                    </Box>
                )}
            </Box>

            {/* Violation Detail Modal */}
            <Modal
                visible={detailVisible}
                title="Chi tiết vi phạm"
                onClose={() => {
                    setDetailVisible(false);
                    setSelectedViolation(null);
                }}
            >
                {selectedViolation && (
                    <Box style={{ padding: '20px' }}>
                        <Box style={{ marginBottom: '20px' }}>
                            <Text bold style={{ fontSize: '18px', color: '#0068ff', marginBottom: '10px' }}>
                                {getViolationTypeLabel(selectedViolation.violationType)}
                            </Text>
                            
                            <Box style={{ marginBottom: '15px' }}>
                                <Text bold style={{ fontSize: '14px', marginBottom: '5px' }}>Mô tả:</Text>
                                <Text style={{ fontSize: '14px', color: '#333' }}>
                                    {selectedViolation.description}
                                </Text>
                            </Box>

                            <Box style={{ display: 'grid', gridTemplateColumns: '1fr 1fr', gap: '15px', marginBottom: '15px' }}>
                                <Box>
                                    <Text bold style={{ fontSize: '12px', color: '#666', marginBottom: '5px' }}>
                                        Ngày vi phạm:
                                    </Text>
                                    <Text style={{ fontSize: '14px' }}>
                                        {new Date(selectedViolation.violationDate).toLocaleDateString('vi-VN')}
                                    </Text>
                                </Box>
                                <Box>
                                    <Text bold style={{ fontSize: '12px', color: '#666', marginBottom: '5px' }}>
                                        Điểm trừ:
                                    </Text>
                                    <Text style={{ fontSize: '14px', color: '#dc3545', fontWeight: 'bold' }}>
                                        -{selectedViolation.pointsDeducted} điểm
                                    </Text>
                                </Box>
                            </Box>

                            {selectedViolation.reportedBy && (
                                <Box style={{ marginBottom: '15px' }}>
                                    <Text bold style={{ fontSize: '12px', color: '#666', marginBottom: '5px' }}>
                                        Người báo cáo:
                                    </Text>
                                    <Text style={{ fontSize: '14px' }}>
                                        {selectedViolation.reportedBy.name}
                                    </Text>
                                </Box>
                            )}

                            {selectedViolation.appealReason && (
                                <Box style={{ marginBottom: '15px', padding: '12px', backgroundColor: '#f8f9fa', borderRadius: '8px' }}>
                                    <Text bold style={{ fontSize: '12px', color: '#666', marginBottom: '5px' }}>
                                        Lý do khiếu nại:
                                    </Text>
                                    <Text style={{ fontSize: '14px' }}>
                                        {selectedViolation.appealReason}
                                    </Text>
                                </Box>
                            )}
                        </Box>

                        {selectedViolation.status === VIOLATION_STATUS.PROCESSED && !selectedViolation.appealReason && (
                            <Button
                                fullWidth
                                style={{ backgroundColor: '#f39c12', color: 'white', marginTop: '10px' }}
                                onClick={() => {
                                    setDetailVisible(false);
                                    setAppealVisible(true);
                                }}
                            >
                                {ICONS.APPEAL} Gửi khiếu nại
                            </Button>
                        )}
                    </Box>
                )}
            </Modal>

            {/* Appeal Modal */}
            <Modal
                visible={appealVisible}
                title="Gửi khiếu nại"
                onClose={() => {
                    setAppealVisible(false);
                    setAppealReason('');
                }}
                actions={[
                    { text: 'Hủy', close: true, danger: true },
                    { 
                        text: submitting ? 'Đang gửi...' : 'Gửi khiếu nại',
                        close: false,
                        onClick: handleAppealSubmit,
                        disabled: submitting || appealReason.length < APPEAL_RULES.MIN_REASON_LENGTH
                    }
                ]}
            >
                <Box style={{ padding: '20px' }}>
                    <Text style={{ marginBottom: '15px', fontSize: '14px', color: '#666' }}>
                        Vui lòng nêu rõ lý do khiếu nại vi phạm này. Khiếu nại sẽ được xem xét và phản hồi trong thời gian sớm nhất.
                    </Text>
                    
                    <Input.TextArea
                        placeholder={`Nhập lý do khiếu nại (tối thiểu ${APPEAL_RULES.MIN_REASON_LENGTH} ký tự)...`}
                        value={appealReason}
                        onChange={(e) => setAppealReason(e.target.value)}
                        rows={4}
                        style={{ marginBottom: '10px' }}
                    />

                    <Text style={{ fontSize: '12px', color: appealReason.length < APPEAL_RULES.MIN_REASON_LENGTH ? '#dc3545' : '#28a745' }}>
                        {appealReason.length}/{APPEAL_RULES.MIN_REASON_LENGTH} ký tự tối thiểu
                    </Text>
                </Box>
            </Modal>

            <BottomNavigationEdu />
        </Box>
    );
};

export default MyViolations;

import React, { useState, useEffect, useCallback, useRef } from 'react';
import { Box, Text, Button, Input, useNavigate, useLocation } from 'zmp-ui';
import { formatDistanceToNow } from 'date-fns';
import vi from 'date-fns/locale/vi';
import HeaderEdu from './HeaderEdu';
import HeaderSpacer from './utils/HeaderSpacer';
import BottomNavigationEdu from './BottomNavigationEdu';
import LoadingIndicator from './utils/LoadingIndicator';
import { authApi } from '../utils/api';
import useNotification from '../hooks/useNotification';

const AnnouncementDetail = () => {
    const navigate = useNavigate();
    const location = useLocation();
    const notification = useNotification();
    const { announcement } = location.state || {};
    const [replies, setReplies] = useState([]);
    const [loading, setLoading] = useState(true);
    const [replyContent, setReplyContent] = useState('');
    const [editingReply, setEditingReply] = useState(null);
    const [replyingTo, setReplyingTo] = useState(null);
    const [nestedReplies, setNestedReplies] = useState({});
    const [loadingNestedReplies, setLoadingNestedReplies] = useState({});
    const [isSubmitting, setIsSubmitting] = useState(false);
    const replyInputRef = useRef(null);
    const [pagination, setPagination] = useState({
        currentPage: 1,
        totalPages: 1,
        totalItems: 0,
        itemsPerPage: 10
    });

    const fetchReplies = useCallback(async (page = 1) => {
        if (!announcement?._id) return;

        setLoading(true);
        try {
            const response = await authApi.get(`/announcements/${announcement._id}/replies?page=${page}&limit=10`);
            if (response.data.success) {
                setReplies(response.data.data.replies);
                setPagination(response.data.data.pagination);
            }
        } catch (err) {
            notification.error('Lỗi khi tải phản hồi');
        } finally {
            setLoading(false);
        }
    }, [announcement?._id]);

    const fetchNestedReplies = useCallback(async (parentReplyId, page = 1) => {
        if (!announcement?._id || !parentReplyId) return;

        setLoadingNestedReplies(prev => ({ ...prev, [parentReplyId]: true }));
        try {
            const response = await authApi.get(
                `/announcements/${announcement._id}/replies?parentReplyId=${parentReplyId}&page=${page}&limit=10`
            );
            if (response.data.success) {
                setNestedReplies(prev => ({
                    ...prev,
                    [parentReplyId]: {
                        replies: response.data.data.replies,
                        pagination: response.data.data.pagination
                    }
                }));
            }
        } catch (err) {
            notification.error('Lỗi khi tải phản hồi con');
        } finally {
            setLoadingNestedReplies(prev => ({ ...prev, [parentReplyId]: false }));
        }
    }, [announcement?._id]);

    useEffect(() => {
        if (announcement?._id) {
            fetchReplies();
        }
    }, [fetchReplies, announcement?._id]);

    const handleSubmitReply = async () => {
        if (!replyContent.trim()) {
            notification.warning('Vui lòng nhập nội dung phản hồi');
            return;
        }

        try {
            setIsSubmitting(true);
            const payload = {
                content: replyContent
            };

            if (replyingTo) {
                payload.parentReplyId = replyingTo._id;
                payload.replyToUserId = replyingTo.user._id;
            }

            const response = await authApi.post(`/announcements/${announcement._id}/reply`, payload);

            if (response.data.success) {
                notification.success('Gửi phản hồi thành công');
                setReplyContent('');
                
                // Cập nhật state trực tiếp thay vì gọi lại API
                const newReply = response.data.data;
                if (replyingTo) {
                    // Nếu là phản hồi con, cập nhật nestedReplies
                    setNestedReplies(prev => ({
                        ...prev,
                        [replyingTo._id]: {
                            replies: [...(prev[replyingTo._id]?.replies || []), newReply],
                            pagination: {
                                ...prev[replyingTo._id]?.pagination,
                                totalItems: (prev[replyingTo._id]?.pagination?.totalItems || 0) + 1
                            }
                        }
                    }));
                } else {
                    // Nếu là phản hồi chính, cập nhật replies
                    setReplies(prev => [...prev, newReply]);
                    setPagination(prev => ({
                        ...prev,
                        totalItems: prev.totalItems + 1
                    }));
                }
            }
        } catch (err) {
            notification.error('Lỗi khi gửi phản hồi');
        } finally {
            setIsSubmitting(false);
        }
    };

    const handleEditReply = async (replyId) => {
        if (!replyContent.trim()) {
            notification.warning('Vui lòng nhập nội dung phản hồi');
            return;
        }

        try {
            notification.showLoading('Đang cập nhật phản hồi...');
            const response = await authApi.put(`/announcements/${announcement._id}/replies/${replyId}`, {
                content: replyContent
            });

            if (response.data.success) {
                notification.success('Cập nhật phản hồi thành công');
                setReplyContent('');
                setEditingReply(null);
                fetchReplies();
            }
        } catch (err) {
            notification.error('Lỗi khi cập nhật phản hồi');
        } finally {
            notification.hideLoading();
        }
    };

    const handleDeleteReply = async (replyId) => {
        try {
            notification.showLoading('Đang xóa phản hồi...');
            const response = await authApi.delete(`/announcements/${announcement._id}/replies/${replyId}`);

            if (response.data.success) {
                notification.success('Xóa phản hồi thành công');
                fetchReplies();
            }
        } catch (err) {
            notification.error('Lỗi khi xóa phản hồi');
        } finally {
            notification.hideLoading();
        }
    };

    const handleReplyTo = (reply) => {
        setReplyingTo(reply);
        setEditingReply(null);
        setReplyContent('');
        setTimeout(() => {
            replyInputRef.current?.focus();
        }, 100);
    };

    const handleCancelReply = () => {
        setReplyingTo(null);
        setEditingReply(null);
        setReplyContent('');
    };

    const renderReply = (reply, isNested = false) => {
        const hasNestedReplies = reply.replyCount > 0;
        const nestedRepliesData = nestedReplies[reply._id];
        const isReplyingToThis = replyingTo?._id === reply._id;

        return (
            <Box
                key={reply._id}
                style={{
                    backgroundColor: '#f9f9f9',
                    borderRadius: '8px',
                    padding: '12px',
                    border: '1px solid #eee',
                    marginLeft: isNested ? '20px' : '0'
                }}
            >
                <Box style={{ display: 'flex', justifyContent: 'space-between', marginBottom: '8px' }}>
                    <Box>
                        <Text bold style={{ fontSize: '14px' }}>
                            {reply.user.name}
                        </Text>
                        <Text style={{ fontSize: '12px', color: '#666' }}>
                            {reply.timeAgo}
                            {reply.isEdited && (
                                <Text style={{ color: '#999', marginLeft: '5px' }}>
                                    (Đã chỉnh sửa)
                                </Text>
                            )}
                        </Text>
                    </Box>
                    {(reply.canEdit || reply.canDelete) && (
                        <Box style={{ display: 'flex', gap: '8px' }}>
                            {reply.canEdit && (
                                <Button
                                    size="small"
                                    onClick={() => {
                                        setEditingReply(reply._id);
                                        setReplyContent(reply.content);
                                        setReplyingTo(null);
                                    }}
                                    style={{ backgroundColor: '#f0f8ff', color: '#0068ff' }}
                                >
                                    Sửa
                                </Button>
                            )}
                            {reply.canDelete && (
                                <Button
                                    size="small"
                                    onClick={() => handleDeleteReply(reply._id)}
                                    style={{ backgroundColor: '#ffe5e5', color: '#ff3b30' }}
                                >
                                    Xóa
                                </Button>
                            )}
                        </Box>
                    )}
                </Box>
                <Text style={{ fontSize: '14px', whiteSpace: 'pre-wrap', marginBottom: '8px' }}>
                    {reply.content}
                </Text>
                <Box style={{ display: 'flex', gap: '8px' }}>
                    <Button
                        size="small"
                        onClick={() => handleReplyTo(reply)}
                        style={{ backgroundColor: '#f0f8ff', color: '#0068ff' }}
                    >
                        Phản hồi
                    </Button>
                    {hasNestedReplies && !nestedRepliesData && (
                        <Button
                            size="small"
                            onClick={() => fetchNestedReplies(reply._id)}
                            style={{ backgroundColor: '#f0f8ff', color: '#0068ff' }}
                        >
                            Xem {reply.replyCount} phản hồi
                        </Button>
                    )}
                </Box>

                {/* Inline Reply Input */}
                {isReplyingToThis && (
                    <Box style={{ marginTop: '12px', padding: '12px', backgroundColor: '#fff', borderRadius: '8px', border: '1px solid #e0e0e0' }}>
                        <Box style={{ display: 'flex', justifyContent: 'space-between', alignItems: 'center', marginBottom: '8px' }}>
                            <Text style={{ fontSize: '14px', color: '#0068ff' }}>
                                Đang phản hồi cho <Text bold>{reply.user.name}</Text>
                            </Text>
                            <Button
                                size="small"
                                onClick={handleCancelReply}
                                style={{ backgroundColor: '#f5f5f5', color: '#666' }}
                                disabled={isSubmitting}
                            >
                                Hủy
                            </Button>
                        </Box>
                        <Input
                            type="textarea"
                            value={replyContent}
                            onChange={(e) => setReplyContent(e.target.value)}
                            placeholder="Viết phản hồi..."
                            style={{ minHeight: '80px', marginBottom: '8px' }}
                            disabled={isSubmitting}
                        />
                        <Button
                            size="small"
                            onClick={handleSubmitReply}
                            style={{ backgroundColor: '#0068ff', color: 'white' }}
                            disabled={isSubmitting}
                        >
                            {isSubmitting ? "Đang gửi..." : "Gửi phản hồi"}
                        </Button>
                    </Box>
                )}

                {/* Nested Replies */}
                {loadingNestedReplies[reply._id] ? (
                    <Box style={{ textAlign: 'center', padding: '10px' }}>
                        <Text style={{ color: '#666' }}>Đang tải phản hồi...</Text>
                    </Box>
                ) : nestedRepliesData?.replies?.map(nestedReply => renderReply(nestedReply, true))}
            </Box>
        );
    };

    if (!announcement) return null;

    return (
        <Box className="container" style={{ minHeight: '100vh', display: 'flex', flexDirection: 'column', paddingBottom: '60px', backgroundColor: '#f5f5f5' }}>
            <HeaderEdu
                title="Chi tiết thông báo"
                showBackButton={true}
            />
            <HeaderSpacer />

            <Box style={{ padding: '15px', flex: 1 }}>
                {/* Announcement Content */}
                <Box style={{ backgroundColor: 'white', borderRadius: '10px', padding: '15px', marginBottom: '15px' }}>
                    <Box style={{ marginBottom: '15px' }}>
                        <Text bold size="large" style={{ marginBottom: '8px' }}>
                            {announcement.title || 'Thông báo'}
                        </Text>
                        <Box flex alignItems="center" style={{ marginBottom: '10px' }}>
                            <Text style={{ fontSize: '14px', color: '#666' }}>
                                Từ: <Text bold>{announcement.sender?.name}</Text>
                            </Text>
                            <Text style={{ fontSize: '14px', color: '#666', marginLeft: '15px' }}>
                                {formatDistanceToNow(new Date(announcement.createdAt), { locale: vi, addSuffix: true })}
                            </Text>
                        </Box>
                        {announcement.type && (
                            <Box style={{
                                backgroundColor: '#e8f0fe',
                                color: '#0068ff',
                                padding: '4px 8px',
                                borderRadius: '4px',
                                fontSize: '12px',
                                display: 'inline-block',
                                marginBottom: '10px'
                            }}>
                                {announcement.type === 'teacher_to_student' && 'Giáo viên gửi học sinh'}
                                {announcement.type === 'head_to_teacher' && 'Tổ trưởng gửi giáo viên'}
                                {announcement.type === 'principal_to_teacher' && 'Hiệu trưởng gửi giáo viên'}
                                {announcement.type === 'admin_to_all' && 'Ban giám hiệu gửi toàn trường'}
                            </Box>
                        )}
                    </Box>
                    <Box style={{
                        backgroundColor: '#f9f9f9',
                        padding: '15px',
                        borderRadius: '8px',
                        lineHeight: 1.6
                    }}>
                        <Text style={{ whiteSpace: 'pre-wrap' }}>{announcement.content}</Text>
                    </Box>
                    {announcement.zaloConfig?.enabled && (
                        <Box style={{ marginTop: '15px', padding: '10px', backgroundColor: '#f0f8ff', borderRadius: '6px' }}>
                            <Text style={{ fontSize: '12px', color: '#666' }}>
                                📱 Đã gửi qua Zalo: {announcement.zaloConfig.groupName}
                                {announcement.zaloConfig.sentAt && (
                                    <Text> • {formatDistanceToNow(new Date(announcement.zaloConfig.sentAt), { locale: vi, addSuffix: true })}</Text>
                                )}
                            </Text>
                        </Box>
                    )}
                </Box>

                {/* Reply Section */}
                <Box style={{ backgroundColor: 'white', borderRadius: '10px', padding: '15px', marginBottom: '15px' }}>
                    <Text bold size="large" style={{ marginBottom: '15px' }}>Phản hồi</Text>
                    
                    {/* Main Reply Input */}
                    {!replyingTo && (
                        <Box style={{ marginBottom: '20px' }}>
                            <Input
                                type="textarea"
                                value={replyContent}
                                onChange={(e) => setReplyContent(e.target.value)}
                                placeholder={isSubmitting ? "Đang gửi phản hồi..." : (editingReply ? "Chỉnh sửa phản hồi..." : "Viết phản hồi...")}
                                style={{ minHeight: '100px', marginBottom: '10px' }}
                                disabled={isSubmitting}
                            />
                            <Box style={{ display: 'flex', gap: '10px' }}>
                                {editingReply ? (
                                    <>
                                        <Button
                                            size="small"
                                            onClick={handleCancelReply}
                                            style={{ backgroundColor: '#f5f5f5', color: '#666' }}
                                            disabled={isSubmitting}
                                        >
                                            Hủy
                                        </Button>
                                        <Button
                                            size="small"
                                            onClick={() => handleEditReply(editingReply)}
                                            style={{ backgroundColor: '#0068ff', color: 'white' }}
                                            disabled={isSubmitting}
                                        >
                                            {isSubmitting ? "Đang cập nhật..." : "Cập nhật"}
                                        </Button>
                                    </>
                                ) : (
                                    <Button
                                        size="small"
                                        onClick={handleSubmitReply}
                                        style={{ backgroundColor: '#0068ff', color: 'white' }}
                                        disabled={isSubmitting}
                                    >
                                        {isSubmitting ? "Đang gửi..." : "Gửi phản hồi"}
                                    </Button>
                                )}
                            </Box>
                        </Box>
                    )}

                    {/* Replies List */}
                    {loading ? (
                        <Box style={{ textAlign: 'center', padding: '20px' }}>
                            <Text style={{ color: '#666' }}>Đang tải phản hồi...</Text>
                        </Box>
                    ) : replies.length === 0 ? (
                        <Box style={{ textAlign: 'center', padding: '20px' }}>
                            <Text style={{ color: '#666' }}>Chưa có phản hồi nào</Text>
                        </Box>
                    ) : (
                        <Box style={{ display: 'flex', flexDirection: 'column', gap: '12px' }}>
                            {replies.map(reply => renderReply(reply))}
                        </Box>
                    )}
                </Box>
            </Box>

            <BottomNavigationEdu />
        </Box>
    );
};

export default AnnouncementDetail; 
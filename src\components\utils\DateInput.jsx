import React from 'react';

const DateInput = ({ 
    value, 
    onChange, 
    placeholder = '', 
    style = {},
    ...props 
}) => {
    const defaultStyle = {
        width: '100%',
        padding: '10px 12px',
        border: '1px solid #e0e0e0',
        borderRadius: '6px',
        fontSize: '14px',
        backgroundColor: '#ffffff',
        boxShadow: '0 1px 3px rgba(0,0,0,0.1)',
        outline: 'none',
        transition: 'border-color 0.3s ease',
        fontFamily: 'inherit',
        minHeight: '40px',
        WebkitAppearance: 'none',
        MozAppearance: 'textfield',
        ...style
    };

    const handleFocus = (e) => {
        e.target.style.borderColor = '#0068ff';
        e.target.style.boxShadow = '0 0 0 3px rgba(0, 104, 255, 0.1)';
    };

    const handleBlur = (e) => {
        e.target.style.borderColor = '#e0e0e0';
        e.target.style.boxShadow = '0 1px 3px rgba(0,0,0,0.1)';
    };

    return (
        <input
            type="date"
            value={value}
            onChange={onChange}
            placeholder={placeholder}
            style={defaultStyle}
            onFocus={handleFocus}
            onBlur={handleBlur}
            {...props}
        />
    );
};

export default DateInput; 
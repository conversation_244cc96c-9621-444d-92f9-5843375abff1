import React, { useState, useContext, useEffect } from 'react';
import { Box, Button, Input, Text, SnackbarProvider, useNavigate, <PERSON><PERSON>, Page } from 'zmp-ui';
import { api } from '../utils/api';
import { AuthContext } from '../context/AuthContext';
import { parseJwt } from '../utils/jwt';
import { getPhoneNumber, getAccessToken } from "zmp-sdk/apis";
import { ICONS } from '../constants/icons';

const Login = () => {
    const navigate = useNavigate();
    const { user, loading: authLoading, updateToken, getToken } = useContext(AuthContext);
    const [username, setUsername] = useState('');
    const [password, setPassword] = useState('');
    const [role, setRole] = useState('student');
    const [usernameError, setUsernameError] = useState('');
    const [passwordError, setPasswordError] = useState('');
    const [loading, setLoading] = useState(false);
    const [generalError, setGeneralError] = useState('');
    const [loginSuccess, setLoginSuccess] = useState(false);

    // Redirect nếu đã đăng nhập
    useEffect(() => {
        console.log('Login: authLoading:', authLoading, 'user:', user, 'loginSuccess:', loginSuccess);
        if (!authLoading && user) {
            navigate('/school-year-selection', { replace: true });
        }
    }, [user, authLoading, navigate, loginSuccess]);

    // Hàm đăng nhập bằng username/password
    const handleLogin = async () => {
        setLoading(true);
        setUsernameError('');
        setPasswordError('');
        setGeneralError('');

        // Kiểm tra input
        if (!username) {
            setUsernameError('Vui lòng nhập tên đăng nhập');
            console.log('Set usernameError:', 'Vui lòng nhập tên đăng nhập');
            setLoading(false);
            return;
        }
        if (!password) {
            setPasswordError('Vui lòng nhập mật khẩu');
            console.log('Set passwordError:', 'Vui lòng nhập mật khẩu');
            setLoading(false);
            return;
        }

        try {
            console.log('Login: Attempting login with:', { studentId: username, password });
            const loginRes = await api.post('/auth/login', {
                studentId: username,
                password,
            });

            const data = loginRes.data;
            console.log('API response:', data);

            // Kiểm tra xem có token không
            if (data.token) {
                await handleLoginSuccess(data.token);
            } else {
                const errorMsg = data.msg || 'Thông tin đăng nhập không hợp lệ';
                setGeneralError(errorMsg);
                console.log('Set generalError:', errorMsg);
            }
        } catch (err) {
            const errorMsg = err.response?.data?.msg || 'Có lỗi xảy ra';
            setGeneralError(errorMsg);
            console.log('Error caught, set generalError:', errorMsg);
            localStorage.removeItem('token');
            localStorage.removeItem('user');
        } finally {
            setLoading(false);
        }
    };

    // Hàm xử lý chung cho việc đăng nhập thành công
    const handleLoginSuccess = async (token) => {
        console.log('Login: Token received:', token);

        // Phân tích token để lấy role
        const parsedToken = parseJwt(token);
        console.log('Login: Parsed token:', parsedToken);

        if (!parsedToken || !parsedToken.user || !parsedToken.user.role) {
            throw new Error('Invalid token structure');
        }

        // Clear any previous school year selection on new login
        localStorage.removeItem('selectedSchoolYear');
        localStorage.removeItem('showNoClassModal');

        // QUAN TRỌNG: Cập nhật token trong context và đợi nó hoàn thành
        await updateToken(token);

        // Đánh dấu login thành công
        setLoginSuccess(true);

        // Lấy token sau khi đã cập nhật
        const currentToken = getToken();
        console.log('Login: Current token after update:', currentToken);

        // Chuyển hướng đến trang chọn năm học
        navigate('/school-year-selection', { replace: true });
    };

    const handlePhoneLogin = async () => {
        setLoading(true);
        setGeneralError('');

        try {
            // Lấy accessToken từ Zalo
            const accessToken = await new Promise((resolve, reject) => {
                getAccessToken({
                    success: (token) => {
                        console.log("accessToken:", token);
                        resolve(token);
                    },
                    fail: (error) => {
                        console.log("accessToken error:", error);
                        reject(error);
                    },
                });
            });

            // Lấy token số điện thoại từ Zalo
            const phoneData = await new Promise((resolve, reject) => {
                getPhoneNumber({
                    success: (data) => resolve(data),
                    fail: (error) => reject(error),
                });
            });

            const { token: phoneToken } = phoneData;
            console.log("Phone token:", phoneToken);

            // Gọi API đăng nhập bằng số điện thoại
            console.log('Login: Attempting phone login with:', { accessToken, token: phoneToken });
            const loginRes = await api.post('/auth/phone', {
                accessToken: accessToken,
                token: phoneToken
            });

            const data = loginRes.data;
            console.log('API response:', data);

            // Kiểm tra xem có token không
            if (data.token) {
                await handleLoginSuccess(data.token);
            } else {
                const errorMsg = data.msg || 'Thông tin đăng nhập không hợp lệ';
                setGeneralError(errorMsg);
                console.log('Set generalError:', errorMsg);
            }
        } catch (err) {
            const errorMsg = err.response?.data?.msg || 'Có lỗi xảy ra khi đăng nhập bằng số điện thoại';
            setGeneralError(errorMsg);
            console.log('Error caught, set generalError:', errorMsg);
            localStorage.removeItem('token');
            localStorage.removeItem('user');
        } finally {
            setLoading(false);
        }
    };

    return (
        <SnackbarProvider>
            <Box
                className="container"
                style={{
                    maxWidth: '480px',
                    width: '100%',
                    margin: '0 auto',
                    backgroundColor: 'white',
                    minHeight: 'calc(100vh - env(safe-area-inset-top) - env(safe-area-inset-bottom))',
                    display: 'flex',
                    flexDirection: 'column',
                }}
            >
                {/* Header */}
                <Box
                    className="header"
                    style={{
                        backgroundColor: '#0068ff',
                        color: 'white',
                        padding: '30px 20px',
                        textAlign: 'center',
                        position: 'relative',
                        minHeight: '150px',
                        display: 'flex',
                        flexDirection: 'column',
                        justifyContent: 'center',
                        alignItems: 'center',
                    }}
                >
                    <Text
                        className="logo"
                        bold
                        size="xxLarge"
                        style={{ marginBottom: '10px' }}
                    >
                        THPT Số 1 Tư Nghĩa
                    </Text>
                    <Text
                        className="tagline"
                        size="large"
                        style={{ opacity: 0.9 }}
                    >
                        Kết nối giáo dục - Nâng tầm tri thức
                    </Text>
                    <Box
                        className="header-wave"
                        style={{ position: 'absolute', bottom: '-1px', left: 0, width: '100%' }}
                    >
                        <svg
                            viewBox="0 0 1200 120"
                            preserveAspectRatio="none"
                            style={{ width: 'calc(100% + 1.3px)', height: '30px' }}
                        >
                            <path
                                d="M321.39,56.44c58-10.79,114.16-30.13,172-41.86,82.39-16.72,168.19-17.73,250.45-.39C823.78,31,906.67,72,985.66,92.83c70.05,18.48,146.53,26.09,214.34,3V0H0V27.35A600.21,600.21,0,0,0,321.39,56.44Z"
                                style={{ fill: '#FFFFFF' }}
                            />
                        </svg>
                    </Box>
                </Box>

                {/* Main Content */}
                <Box
                    className="main-content"
                    flex
                    flexDirection="column"
                    justifyContent="center"
                    style={{ flex: 1, padding: '20px' }}
                >
                    <Box
                        className="illustration"
                        style={{
                            display: 'flex',
                            justifyContent: 'center',
                            alignItems: 'center',
                            margin: '20px 0',
                        }}
                    >
                        <img
                            src="/assets/images/logo-tunghia1.jpg"
                            alt="Học tập kết nối"
                            style={{ maxWidth: '70%', height: 'auto' }}
                        />
                    </Box>

                    <Text className="welcome-text" bold size="xxLarge" style={{ textAlign: 'center', marginBottom: '10px' }}>
                        Chào mừng đến với THPT Số 1 Tư Nghĩa
                    </Text>
                    <Text className="welcome-desc" style={{ textAlign: 'center', color: '#666', marginBottom: '30px' }}>
                        Nền tảng kết nối học sinh, giáo viên và phụ huynh
                    </Text>

                    <Box className="login-form" style={{ marginTop: '20px' }}>
                        <Box className="role-selection" flex justifyContent="space-between" style={{ marginBottom: '25px' }}>
                            <Box
                                className={`role-option ${role === 'student' ? 'active' : ''}`}
                                onClick={() => setRole('student')}
                                style={{
                                    flex: 1,
                                    textAlign: 'center',
                                    padding: '15px',
                                    border: `2px solid ${role === 'student' ? '#0068ff' : '#e0e0e0'}`,
                                    borderRadius: '8px',
                                    cursor: 'pointer',
                                    marginRight: '8px',
                                    backgroundColor: role === 'student' ? '#f0f6ff' : 'white',
                                }}
                            >
                                <Text className="role-icon" style={{ fontSize: '32px', marginBottom: '8px' }}>
                                    {ICONS.STUDENT}
                                </Text>
                                <Text className="role-text" bold>
                                    Học sinh
                                </Text>
                            </Box>
                            <Box
                                className={`role-option ${role === 'teacher' ? 'active' : ''}`}
                                onClick={() => setRole('teacher')}
                                style={{
                                    flex: 1,
                                    textAlign: 'center',
                                    padding: '15px',
                                    border: `2px solid ${role === 'teacher' ? '#0068ff' : '#e0e0e0'}`,
                                    borderRadius: '8px',
                                    cursor: 'pointer',
                                    marginLeft: '8px',
                                    backgroundColor: role === 'teacher' ? '#f0f6ff' : 'white',
                                }}
                            >
                                <Text className="role-icon" style={{ fontSize: '32px', marginBottom: '8px' }}>
                                    {ICONS.TEACHER}
                                </Text>
                                <Text className="role-text" bold>
                                    Giáo viên
                                </Text>
                            </Box>
                        </Box>

                        {generalError && (
                            <Text style={{ color: 'red', marginBottom: '15px', textAlign: 'center' }}>
                                {generalError}
                            </Text>
                        )}

                        <Box className="form-group" style={{ marginBottom: '20px' }}>
                            <Text className="form-label" bold style={{ marginBottom: '8px', color: '#555' }}>
                                Tên đăng nhập
                            </Text>
                            <Input
                                type="text"
                                value={username}
                                onChange={(e) => setUsername(e.target.value)}
                                placeholder="Nhập tên đăng nhập của bạn"
                                status={usernameError ? 'error' : 'default'}
                            />
                            {usernameError && (
                                <Text style={{ color: '#ff4757', fontSize: '12px', marginTop: '4px' }}>
                                    {usernameError}
                                </Text>
                            )}
                        </Box>

                        <Box className="form-group" style={{ marginBottom: '20px' }}>
                            <Text className="form-label" bold style={{ marginBottom: '8px', color: '#555' }}>
                                Mật khẩu
                            </Text>
                            <Input.Password
                                value={password}
                                onChange={(e) => setPassword(e.target.value)}
                                placeholder="Nhập mật khẩu của bạn"
                                status={passwordError ? 'error' : 'default'}
                                visibilityToggle={true}
                            />
                            {passwordError && (
                                <Text style={{ color: '#ff4757', fontSize: '12px', marginTop: '4px' }}>
                                    {passwordError}
                                </Text>
                            )}
                        </Box>

                        <Button
                            fullWidth
                            variant="primary"
                            onClick={handleLogin}
                            loading={loading}
                            disabled={loading}
                            style={{ marginBottom: '20px' }}
                        >
                            {loading ? 'Đang đăng nhập' : 'Đăng nhập'}
                        </Button>

                        {/* Đăng nhập qua số điện thoại */}
                        <Box style={{ textAlign: 'center' }}>
                            <Box
                                style={{
                                    display: 'inline-block',
                                    borderTop: '1px solid #e0e0e0',
                                    width: '100%',
                                    position: 'relative',
                                    marginBottom: '15px'
                                }}
                            >
                            </Box>
                            <Box
                                className="phone-login-btn"
                                onClick={handlePhoneLogin}
                                flex
                                alignItems="center"
                                justifyContent="center"
                                style={{
                                    cursor: 'pointer',
                                    color: '#0068ff',
                                    fontSize: '14px',
                                    fontWeight: '500',
                                    gap: '8px'
                                }}
                            >
                                <Text style={{ fontSize: '16px' }}>{ICONS.PHONE}</Text>
                                <Text>Đăng nhập qua số điện thoại</Text>
                            </Box>
                        </Box>
                    </Box>
                </Box>

                <Box className="footer" style={{ padding: '10px', textAlign: 'center', fontSize: '12px', color: '#888' }}>
                    © 2025 THPT Số 1 Tư Nghĩa - Tất cả quyền được bảo lưu
                </Box>
            </Box>
        </SnackbarProvider>
    );
};

export default Login;